{"cells": [{"cell_type": "markdown", "metadata": {"id": "n3ryhkSfIEfl"}, "source": ["# Video Tokenization Using [NVIDIA Cosmos Tokenizer](https://github.com/NVIDIA/Cosmos-Tokenizer) | [![Open In Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/nvidia/Cosmos-Tokenizer/blob/main/notebook/Video_Tokenization.ipynb)\n", "\n", "The Jupyter Notebook example utilizes the **Cosmos-Tokenizer** pretrained models, which include Continuous Video (CV) tokenizers that transform videos into continuous spatio-temporal latents and Discrete Video (DI) tokenizers that transform videos into discrete tokens. Both CV and DV tokenizers are available with compression rates of (`TxHxW` format) 4x8x8 and 8x8x8, and 8x16x16. For instance, **CV4x8x8** effectively downsizes the number of frames by a factor of 4 and both height and width by a factor of 8.\n", "\n", "Within the notebook, the `VideoTokenizer` class from the `cosmos_tokenizer.video_lib` module is employed to manage the encoder and decoder components of this model. The encoder compresses the input video into a condensed latent representation or discrete integers, while the decoder reconstructs the video from this latent representation or discrete integers.\n", "\n", "This instance of the Cosmos Tokenizer demonstrates its autoencoding capability: compressing a video into a smaller latent space and subsequently reconstructing it to its original form. This showcases the efficiency of video tokenization for tasks involving significant spatial compression during video reconstruction, a highly desirable feature for generative modeling.\n"]}, {"cell_type": "markdown", "metadata": {"id": "5BkjyLTPLM6e"}, "source": ["This tutorial follows a simple, step-by-step approach, making it easy to understand and adapt.\n", "\n", "## Step 1: <PERSON><PERSON> the Cosmos Tokenizer Repository"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"id": "TEV88M9YG973"}, "outputs": [], "source": ["# !git clone https://github.com/NVIDIA/Cosmos-Tokenizer.git"]}, {"cell_type": "markdown", "metadata": {"id": "AxOMEJpFL9QL"}, "source": ["## Step 2: Install **Cosmos-Tokenizer**\n", "Before proceeding, ensure you have the **Cosmos Tokenizer** installed. If you cloned the repository in Step 1, use the following command to install it in editable mode:"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"id": "XuwUR6HrIxD8"}, "outputs": [], "source": ["# # Step 2: # Install Cosmos-Tokenizer and its Python dependencies.\n", "# import os\n", "# if os.path.exists(\"Cosmos-Tokenizer\"):\n", "#     os.chdir(\"Cosmos-Tokenizer\")\n", "#     !apt-get update\n", "#     !apt-get install -y git-lfs\n", "#     !git lfs pull\n", "#     %pip install -e .\n", "# else:\n", "#     print('Cosmos-Tokenizer is already installed.')"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# # Step 2: # Install Cosmos-Tokenizer and its Python dependencies.\n", "# !git lfs pull\n", "# %pip install -e ."]}, {"cell_type": "markdown", "metadata": {"id": "id29RPiyMOtB"}, "source": ["## Step 3: Set Up Hugging Face API Token and Download Pretrained Models\n", "\n", "In this step, you'll configure the Hugging Face API token and download the pretrained model weights required for the **Cosmos Tokenizer**.\n", "\n", "1. **Ensure You Have a Hugging Face Account**  \n", "   If you do not already have a Hugging Face account, follow these steps to create one and generate an API token:\n", "   - Go to the [Hugging Face website](https://huggingface.co/) and sign up for a free account.\n", "   - After logging in, navigate to your [Settings → Access Tokens](https://huggingface.co/settings/tokens).\n", "   - Click on \"New Token\" to generate an API token with the required permissions.\n", "\n", "2. **Set the Hugging Face Token**  \n", "   Check if the Hugging Face token is already set in the environment variables. If not, you will be prompted to enter it manually. The token is essential to authenticate and access the Hugging Face models.\n", "\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"id": "joxcyOlnM7HQ"}, "outputs": [], "source": ["import os\n", "\n", "# Check if the token is already set\n", "if \"HUGGINGFACE_TOKEN\" not in os.environ:\n", "    os.environ[\"HUGGINGFACE_TOKEN\"] = \"*************************************\"\n", "!git config --global credential.helper store"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"id": "Lq7MAQ9pGPH9"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["downloading Cosmos-0.1-Tokenizer-CV4x8x8...\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "28e53914fbb947869191d38ba505e82e", "version_major": 2, "version_minor": 0}, "text/plain": ["Fetching 8 files:   0%|          | 0/8 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["downloading Cosmos-0.1-Tokenizer-CV8x8x8...\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d0249343301444d9a6bc79f2ff3425ce", "version_major": 2, "version_minor": 0}, "text/plain": ["Fetching 7 files:   0%|          | 0/7 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["downloading Cosmos-0.1-Tokenizer-CV8x16x16...\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "df6399841b9f4839932c753fe8639b9e", "version_major": 2, "version_minor": 0}, "text/plain": ["Fetching 7 files:   0%|          | 0/7 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["downloading Cosmos-0.1-Tokenizer-DV4x8x8...\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "cafa1d4a88424c7287409e1ec490a4a1", "version_major": 2, "version_minor": 0}, "text/plain": ["Fetching 7 files:   0%|          | 0/7 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["downloading Cosmos-0.1-Tokenizer-DV8x8x8...\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a5e1c06a51fb401aa883a38ecf242b8a", "version_major": 2, "version_minor": 0}, "text/plain": ["Fetching 7 files:   0%|          | 0/7 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["downloading Cosmos-0.1-Tokenizer-DV8x16x16...\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "3ff361d06a14455fa753a31a4d3df247", "version_major": 2, "version_minor": 0}, "text/plain": ["Fetching 7 files:   0%|          | 0/7 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["downloading Cosmos-1.0-Tokenizer-CV8x8x8...\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "f87bd261b01f448a92ad2836728858b2", "version_major": 2, "version_minor": 0}, "text/plain": ["Fetching 10 files:   0%|          | 0/10 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["downloading Cosmos-1.0-Tokenizer-DV8x16x16...\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ef8499e8859d43e5ba6c0e197c75116c", "version_major": 2, "version_minor": 0}, "text/plain": ["Fetching 7 files:   0%|          | 0/7 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from huggingface_hub import login, snapshot_download\n", "import os\n", "HUGGINGFACE_TOKEN = os.environ.get(\"HUGGINGFACE_TOKEN\")\n", "login(token=HUGGINGFACE_TOKEN, add_to_git_credential=True)\n", "model_names = [\n", "        \"Cosmos-0.1-Tokenizer-CV4x8x8\",\n", "        \"Cosmos-0.1-Tokenizer-CV8x8x8\",\n", "        \"Cosmos-0.1-Tokenizer-CV8x16x16\",\n", "        \"Cosmos-0.1-Tokenizer-DV4x8x8\",\n", "        \"Cosmos-0.1-Tokenizer-DV8x8x8\",\n", "        \"Cosmos-0.1-Tokenizer-DV8x16x16\",\n", "        \"Cosmos-1.0-Tokenizer-CV8x8x8\",\n", "        \"Cosmos-1.0-Tokenizer-DV8x16x16\",\n", "]\n", "for model_name in model_names:\n", "    hf_repo = \"nvidia/\" + model_name\n", "    local_dir = \"pretrained_ckpts/\" + model_name\n", "    os.makedirs(local_dir, exist_ok=True)\n", "    print(f\"downloading {model_name}...\")\n", "    snapshot_download(repo_id=hf_repo, local_dir=local_dir)"]}, {"cell_type": "markdown", "metadata": {"id": "ltZ-v2vzNv74"}, "source": ["## Step 4: Use Cosmos Tokenizer for Video Reconstruction\n", "\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"cellView": "form", "colab": {"base_uri": "https://localhost:8080/", "height": 833}, "id": "gZFPrGCBGwtC", "outputId": "b4bd44cc-fc90-4b8e-e12b-d8c916bda7f0"}, "outputs": [], "source": ["# @title In this step, load the required checkpoints, and perform video reconstruction. {\"run\":\"auto\"}\n", "import cv2\n", "import numpy as np\n", "import torch\n", "\n", "import importlib\n", "import cosmos_tokenizer.video_lib\n", "import mediapy as media\n", "\n", "importlib.reload(cosmos_tokenizer.video_lib)\n", "from cosmos_tokenizer.video_lib import CausalVideoTokenizer\n", "\n", "# 1) Specify the model name, and the paths to the encoder/decoder checkpoints.\n", "model_name = 'Cosmos-1.0-Tokenizer-CV8x8x8' # @param [\"Cosmos-0.1-Tokenizer-CV4x8x8\", \"Cosmos-0.1-Tokenizer-CV8x8x8\", \"Cosmos-0.1-Tokenizer-CV8x16x16\", \"Cosmos-0.1-Tokenizer-DV4x8x8\", \"Cosmos-0.1-Tokenizer-DV8x8x8\", \"Cosmos-0.1-Tokenizer-DV8x16x16\", \"Cosmos-1.0-Tokenizer-CV8x8x8\", \"Cosmos-1.0-Tokenizer-DV8x16x16\"]\n", "temporal_window = 49 # @param {type:\"slider\", min:1, max:121, step:8}\n", "\n", "encoder_ckpt = f\"pretrained_ckpts/{model_name}/encoder.jit\"\n", "decoder_ckpt = f\"pretrained_ckpts/{model_name}/decoder.jit\"\n", "\n", "# 2) Load or provide the video filename you want to tokenize & reconstruct.\n", "input_filepath = \"test_data/gait_video.mp4\"\n", "\n", "# 3) Read the video from disk (shape = T x H x W x 3 in BGR).\n", "input_video = media.read_video(input_filepath)[..., :3]\n", "assert input_video.ndim == 4 and input_video.shape[-1] == 3, \"Frames must have shape T x H x W x 3\"\n", "\n", "# 4) Expand dimensions to B x Tx H x W x C, since the CausalVideoTokenizer expects a batch dimension\n", "#    in the input. (Batch size = 1 in this example.)\n", "batched_input_video = np.expand_dims(input_video, axis=0)\n", "\n", "# 5) Create the CausalVideoTokenizer instance with the encoder & decoder.\n", "#    - device=\"cuda\" uses the GPU\n", "#    - dtype=\"bfloat16\" expects Ampere or newer GPU (A100, RTX 30xx, etc.)\n", "tokenizer = CausalVideoTokenizer(\n", "    checkpoint_enc=encoder_ckpt,\n", "    checkpoint_dec=decoder_ckpt,\n", "    device=\"cuda\",\n", "    dtype=\"bfloat16\",\n", ")\n", "\n", "# # 6) Use the tokenizer to autoencode (encode & decode) the video.\n", "# #    The output is a NumPy array with shape = B x T x H x W x C, range [0..255].\n", "# batched_output_video = tokenizer(batched_input_video,\n", "#                                  temporal_window=temporal_window)\n", "\n", "# # 7) Extract the single video from the batch (index 0).\n", "# output_video = batched_output_video[0]\n", "\n", "# # 9) Save the reconstructed video to disk.\n", "# input_dir, input_filename = os.path.split(input_filepath)\n", "# filename, ext = os.path.splitext(input_filename)\n", "# output_filepath = f\"{input_dir}/{filename}_{model_name.split('-')[-1]}{ext}\"\n", "# media.write_video(output_filepath, output_video)\n", "# print(\"Input video read from:\\t\", f\"{os.getcwd()}/{input_filepath}\")\n", "# print(\"Reconstruction saved:\\t\", f\"{os.getcwd()}/{output_filepath}\")\n", "\n", "# # 10) Visualization of the input video (left) and the reconstruction (right).\n", "# media.show_videos([input_video, output_video], [\"Input Video\", \"Reconstructed Video\"], height=720, fps=30)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": [" 25%|██▌       | 1/4 [00:02<00:07,  2.54s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["encoding:  torch.<PERSON><PERSON>([1, 16, 7, 60, 80])\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 50%|█████     | 2/4 [00:03<00:03,  1.68s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["encoding:  torch.<PERSON><PERSON>([1, 16, 7, 60, 80])\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 75%|███████▌  | 3/4 [00:03<00:01,  1.01s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["encoding:  torch.<PERSON><PERSON>([1, 16, 7, 60, 80])\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 4/4 [00:04<00:00,  1.10s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["encoding:  torch.<PERSON><PERSON>([1, 16, 5, 60, 80])\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["from cosmos_tokenizer.utils import (\n", "    numpy2<PERSON>or,\n", "    pad_video_batch,\n", "    tensor2numpy,\n", ")\n", "from tqdm import tqdm\n", "\n", "\n", "video = batched_input_video\n", "num_frames = video.shape[1]  # can be of any length.\n", "\n", "for idx in tqdm(range(0, (num_frames - 1) // temporal_window + 1)):\n", "    # Input video for the current window.\n", "    start, end = idx * temporal_window, (idx + 1) * temporal_window\n", "    input_video = video[:, start:end, ...]\n", "\n", "    # Spatio-temporally pad input_video so it's evenly divisible.\n", "    padded_input_video, crop_region = pad_video_batch(input_video)\n", "    input_tensor = numpy2tensor(\n", "        padded_input_video, dtype=tokenizer._dtype, device=tokenizer._device\n", "    )\n", "    encoding = tokenizer.encode(input_tensor)\n", "\n", "    print('encoding: ', encoding[0].shape)\n", "    #padded_output_video = tensor2numpy(encoding)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["torch.<PERSON><PERSON>([1, 3, 33, 480, 640])"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["input_tensor.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["torch.<PERSON><PERSON>([1, 16, 5, 60, 80])"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}, {"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31m<PERSON><PERSON> crashed while executing code in the current cell or a previous cell. \n", "\u001b[1;31m<PERSON><PERSON>se review the code in the cell(s) to identify a possible cause of the failure. \n", "\u001b[1;31mClick <a href='https://aka.ms/vscodeJupyterKernelCrash'>here</a> for more info. \n", "\u001b[1;31m<PERSON><PERSON><PERSON> <a href='command:jupyter.viewOutput'>log</a> for further details."]}], "source": ["encoding[0].shape"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Thu Aug  7 10:11:08 2025       \n", "+---------------------------------------------------------------------------------------+\n", "| NVIDIA-SMI 535.216.03             Driver Version: 535.216.03   CUDA Version: 12.2     |\n", "|-----------------------------------------+----------------------+----------------------+\n", "| GPU  Name                 Persistence-M | Bus-Id        Disp.A | Volatile Uncorr. ECC |\n", "| Fan  Temp   Perf          Pwr:Usage/Cap |         Memory-Usage | GPU-Util  Compute M. |\n", "|                                         |                      |               MIG M. |\n", "|=========================================+======================+======================|\n", "|   0  NVIDIA RTX A6000               On  | 00000000:01:00.0 Off |                  Off |\n", "| 30%   42C    P8              17W / 300W |  46706MiB / 49140MiB |      0%      Default |\n", "|                                         |                      |                  N/A |\n", "+-----------------------------------------+----------------------+----------------------+\n", "|   1  NVIDIA RTX A6000               On  | 00000000:23:00.0 Off |                  Off |\n", "| 30%   49C    P2              74W / 300W |  44032MiB / 49140MiB |      0%      Default |\n", "|                                         |                      |                  N/A |\n", "+-----------------------------------------+----------------------+----------------------+\n", "|   2  NVIDIA RTX A6000               On  | 00000000:41:00.0 Off |                  Off |\n", "| 35%   59C    P2              87W / 300W |  44032MiB / 49140MiB |      0%      Default |\n", "|                                         |                      |                  N/A |\n", "+-----------------------------------------+----------------------+----------------------+\n", "|   3  NVIDIA RTX A6000               On  | 00000000:61:00.0 Off |                  Off |\n", "| 31%   58C    P2              79W / 300W |  44032MiB / 49140MiB |      0%      Default |\n", "|                                         |                      |                  N/A |\n", "+-----------------------------------------+----------------------+----------------------+\n", "|   4  NVIDIA RTX A6000               On  | 00000000:81:00.0 Off |                  Off |\n", "| 30%   55C    P2              74W / 300W |  44032MiB / 49140MiB |      0%      Default |\n", "|                                         |                      |                  N/A |\n", "+-----------------------------------------+----------------------+----------------------+\n", "|   5  NVIDIA RTX A6000               On  | 00000000:A1:00.0 Off |                  Off |\n", "| 32%   59C    P2              80W / 300W |  44032MiB / 49140MiB |      0%      Default |\n", "|                                         |                      |                  N/A |\n", "+-----------------------------------------+----------------------+----------------------+\n", "|   6  NVIDIA RTX A6000               On  | 00000000:C1:00.0 Off |                  Off |\n", "| 30%   52C    P2              73W / 300W |  44032MiB / 49140MiB |      0%      Default |\n", "|                                         |                      |                  N/A |\n", "+-----------------------------------------+----------------------+----------------------+\n", "|   7  NVIDIA RTX A6000               On  | 00000000:E1:00.0 Off |                  Off |\n", "| 30%   47C    P2              77W / 300W |  44012MiB / 49140MiB |      0%      Default |\n", "|                                         |                      |                  N/A |\n", "+-----------------------------------------+----------------------+----------------------+\n", "                                                                                         \n", "+---------------------------------------------------------------------------------------+\n", "| Processes:                                                                            |\n", "|  GPU   GI   CI        PID   Type   Process name                            GPU Memory |\n", "|        ID   ID                                                             Usage      |\n", "|=======================================================================================|\n", "|    0   N/A  N/A    520400      C   python                                    46698MiB |\n", "|    1   N/A  N/A    520400      C   python                                    44024MiB |\n", "|    2   N/A  N/A    520400      C   python                                    44024MiB |\n", "|    3   N/A  N/A    520400      C   python                                    44024MiB |\n", "|    4   N/A  N/A    520400      C   python                                    44024MiB |\n", "|    5   N/A  N/A    520400      C   python                                    44024MiB |\n", "|    6   N/A  N/A    520400      C   python                                    44024MiB |\n", "|    7   N/A  N/A    520400      C   python                                    44004MiB |\n", "+---------------------------------------------------------------------------------------+\n"]}], "source": ["!nvidia-smi"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": []}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.18"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"state": {"005d7722b304464d8138e43fc76fec4e": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {}}, "01444c446d3e4f419f0af5c16b42c46d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "ProgressStyleModel", "state": {"description_width": ""}}, "02062e86d11441cba9934935ec7a580c": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {}}, "024b312bd6a54abb85fa202a4b2018ad": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"layout": "IPY_MODEL_d956d3f6b9104836912ea9cbe2888ce9", "style": "IPY_MODEL_3f61789a93194927a8c299d479bba98b", "value": " 7/7 [00:00&lt;00:00, 661.07it/s]"}}, "02f6275c9cc34516b21a71ac724495d1": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {}}, "0f32d47089054f7fb7228bf72b22d2cc": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HBoxModel", "state": {"children": ["IPY_MODEL_a59b93004a8343cc981442bf263d1e75", "IPY_MODEL_5cce80c2480e49a78da13ed585375d54", "IPY_MODEL_45e8ad5ebc6c4ba38801225078a2db2b"], "layout": "IPY_MODEL_87ca766ac6e0471f983bb78bf3a9107f"}}, "0f7180fad89e440eba0c4c6c646475ea": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"description_width": "", "font_size": null, "text_color": null}}, "12f4cc0b5f4644e39e310d785f4b658c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HBoxModel", "state": {"children": ["IPY_MODEL_2a5859b95ad544d8aa199060e3d555a1", "IPY_MODEL_a937ef2efe10428b88cab397ec84fc87", "IPY_MODEL_a4e5c42d0f844fe98e2ce85f045f63e3"], "layout": "IPY_MODEL_b700daf28d9743d8a96121ddd5cf80ed"}}, "19f6d67c998f4364b5f3fe81b22bae01": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {}}, "27606a9f47c8460e9a4a258dff090954": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {}}, "2813cd8496934a948a9a8546f2d2a053": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"layout": "IPY_MODEL_005d7722b304464d8138e43fc76fec4e", "style": "IPY_MODEL_837be22694dc4caf9034a315ca79c85f", "value": "Fetching 7 files: 100%"}}, "29452f7901454fc6b2251313e4195d38": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {}}, "2a5859b95ad544d8aa199060e3d555a1": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"layout": "IPY_MODEL_02f6275c9cc34516b21a71ac724495d1", "style": "IPY_MODEL_6283a99956454ccd92ee95af28c6bf63", "value": "Fetching 7 files: 100%"}}, "2ae0cf20d1914d9c9edea83baa98ecde": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "ProgressStyleModel", "state": {"description_width": ""}}, "2b1a71105c5a47fca421708da9c6b4b6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"description_width": "", "font_size": null, "text_color": null}}, "31afab2a2582481fa2532971f105e923": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"layout": "IPY_MODEL_27606a9f47c8460e9a4a258dff090954", "style": "IPY_MODEL_0f7180fad89e440eba0c4c6c646475ea", "value": " 7/7 [00:00&lt;00:00, 628.75it/s]"}}, "33553a0b3cf74d3aa8250ed37da89319": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"layout": "IPY_MODEL_19f6d67c998f4364b5f3fe81b22bae01", "style": "IPY_MODEL_b683edbd3faa4afbbfab126d6460b8a9", "value": " 7/7 [00:00&lt;00:00, 714.39it/s]"}}, "3c7ccc3df6a049b2a7c235a1ca63cc38": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"layout": "IPY_MODEL_f7f6ca1df44f4b16b0ed6678a331ff00", "style": "IPY_MODEL_7131a1a3e073469d8efac521ca1c7b99", "value": "Fetching 8 files: 100%"}}, "3f61789a93194927a8c299d479bba98b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"description_width": "", "font_size": null, "text_color": null}}, "440d6bd59ad04fcfba4edc986340337e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"layout": "IPY_MODEL_581014b871df4d3a9c4e9d766da32685", "style": "IPY_MODEL_b9c28aae92fb44a4b6e91b5a2c88a6a6", "value": "Fetching 7 files: 100%"}}, "45e8ad5ebc6c4ba38801225078a2db2b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"layout": "IPY_MODEL_945af54cb71443fd94acc060514bda28", "style": "IPY_MODEL_538f2f539e064f17a4294c96f001b10f", "value": " 10/10 [00:00&lt;00:00, 1095.32it/s]"}}, "45e916b2b4134864acaf6ac0f1bf2ce3": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {}}, "4775d52b80ed46cca7361a08f948ffce": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"description_width": "", "font_size": null, "text_color": null}}, "4cf78dfda4dc40fc8791b26b6e44d531": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"description_width": "", "font_size": null, "text_color": null}}, "538f2f539e064f17a4294c96f001b10f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"description_width": "", "font_size": null, "text_color": null}}, "55ac9ee7c76345eab592c7f95ef78a66": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {}}, "55cfd7109b7f430088afbf78c2a74333": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HBoxModel", "state": {"children": ["IPY_MODEL_440d6bd59ad04fcfba4edc986340337e", "IPY_MODEL_88628d0f4ef74a599cfb08ad98eb4190", "IPY_MODEL_33553a0b3cf74d3aa8250ed37da89319"], "layout": "IPY_MODEL_c630ee259e77449e9ba8fd039784407e"}}, "581014b871df4d3a9c4e9d766da32685": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {}}, "59884d445bc14bd5a04cd9c4a210d177": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "ProgressStyleModel", "state": {"description_width": ""}}, "5c91eb86616d4e3285d8580e2378d58f": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {}}, "5cce80c2480e49a78da13ed585375d54": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "FloatProgressModel", "state": {"bar_style": "success", "layout": "IPY_MODEL_fa59b02c7b4a48a7bfeca13deb3e307c", "max": 10, "style": "IPY_MODEL_6fb44f57488f49dd91458b45a87f16b5", "value": 10}}, "5e2c95ae98554a3c99d0647ef3df5966": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "ProgressStyleModel", "state": {"description_width": ""}}, "61c215e2458d465a99b71e59f6221ab6": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {}}, "6283a99956454ccd92ee95af28c6bf63": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"description_width": "", "font_size": null, "text_color": null}}, "628f776b65864836b4419a91767e7fe0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "FloatProgressModel", "state": {"bar_style": "success", "layout": "IPY_MODEL_67c7e86f03534c4ba54fa28172c447f0", "max": 7, "style": "IPY_MODEL_59884d445bc14bd5a04cd9c4a210d177", "value": 7}}, "62b642a15dfb4a3a87fb96564f4a4ecf": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"description_width": "", "font_size": null, "text_color": null}}, "64467ff947d541359ab685eda45c5e67": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "FloatProgressModel", "state": {"bar_style": "success", "layout": "IPY_MODEL_a1027294db2144d0a68ce979ebc4c753", "max": 7, "style": "IPY_MODEL_d34154b22a12460eaf696f10c56057a0", "value": 7}}, "66d26171578746ea97b3feba93a0498f": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {}}, "67c7e86f03534c4ba54fa28172c447f0": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {}}, "6fb44f57488f49dd91458b45a87f16b5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "ProgressStyleModel", "state": {"description_width": ""}}, "7131a1a3e073469d8efac521ca1c7b99": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"description_width": "", "font_size": null, "text_color": null}}, "71de327259bf462982866ac8dd2280bb": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {}}, "73280e390d414e72bb5592a523bbb0c3": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {}}, "837be22694dc4caf9034a315ca79c85f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"description_width": "", "font_size": null, "text_color": null}}, "87ca766ac6e0471f983bb78bf3a9107f": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {}}, "88628d0f4ef74a599cfb08ad98eb4190": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "FloatProgressModel", "state": {"bar_style": "success", "layout": "IPY_MODEL_962a237d6b3848089c21a0454bfb7a6d", "max": 7, "style": "IPY_MODEL_01444c446d3e4f419f0af5c16b42c46d", "value": 7}}, "8e45ff51a8764cc3ba00f4b9778808e7": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {}}, "8f588bc4501d482690d350c164aab19a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"description_width": "", "font_size": null, "text_color": null}}, "8fb71e0c1edd41eeba4fac71e392cea7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"layout": "IPY_MODEL_c9a4197d90e14ee4855f7244072cf822", "style": "IPY_MODEL_4775d52b80ed46cca7361a08f948ffce", "value": " 7/7 [00:00&lt;00:00, 593.78it/s]"}}, "90ee5121aa794f328a5b545c3b5c6200": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HBoxModel", "state": {"children": ["IPY_MODEL_b974515017d240b5aa5b6fd7a620f1fc", "IPY_MODEL_64467ff947d541359ab685eda45c5e67", "IPY_MODEL_31afab2a2582481fa2532971f105e923"], "layout": "IPY_MODEL_98a9e3707f904957a79984dac799fc08"}}, "945af54cb71443fd94acc060514bda28": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {}}, "962a237d6b3848089c21a0454bfb7a6d": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {}}, "98a9e3707f904957a79984dac799fc08": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {}}, "98efecb867594294ba6dcc3bfb33cf25": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HBoxModel", "state": {"children": ["IPY_MODEL_2813cd8496934a948a9a8546f2d2a053", "IPY_MODEL_cbaaa6c1b3a24119a451a3ebfb6c9839", "IPY_MODEL_8fb71e0c1edd41eeba4fac71e392cea7"], "layout": "IPY_MODEL_61c215e2458d465a99b71e59f6221ab6"}}, "9a45775d5e014e40a9855b7b60513786": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"layout": "IPY_MODEL_29452f7901454fc6b2251313e4195d38", "style": "IPY_MODEL_e0578c803518400f8a0f331ad0f2ddcd", "value": " 8/8 [00:00&lt;00:00, 439.60it/s]"}}, "9eb46562ec28492d876ceada5933d490": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {}}, "a1027294db2144d0a68ce979ebc4c753": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {}}, "a4849d51ae764a71a1567bfcdbdb5c05": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HBoxModel", "state": {"children": ["IPY_MODEL_3c7ccc3df6a049b2a7c235a1ca63cc38", "IPY_MODEL_f29987445276454a9022e081dd5e5caa", "IPY_MODEL_9a45775d5e014e40a9855b7b60513786"], "layout": "IPY_MODEL_9eb46562ec28492d876ceada5933d490"}}, "a4e5c42d0f844fe98e2ce85f045f63e3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"layout": "IPY_MODEL_c686ed158b5e4a9d9d8c122bb8809dc9", "style": "IPY_MODEL_62b642a15dfb4a3a87fb96564f4a4ecf", "value": " 7/7 [00:00&lt;00:00, 700.22it/s]"}}, "a59b93004a8343cc981442bf263d1e75": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"layout": "IPY_MODEL_66d26171578746ea97b3feba93a0498f", "style": "IPY_MODEL_b8495c5e93894672bae7ef7c644d4b46", "value": "Fetching 10 files: 100%"}}, "a937ef2efe10428b88cab397ec84fc87": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "FloatProgressModel", "state": {"bar_style": "success", "layout": "IPY_MODEL_02062e86d11441cba9934935ec7a580c", "max": 7, "style": "IPY_MODEL_5e2c95ae98554a3c99d0647ef3df5966", "value": 7}}, "ad6e9b7742384f86b1803b176e6d0f0b": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {}}, "b0b866fc1af448e1924e813db823c68c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "ProgressStyleModel", "state": {"description_width": ""}}, "b3ee152b231944c9815dee054c9e67da": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HBoxModel", "state": {"children": ["IPY_MODEL_d9ad63f022c44b94aac870c7ba7216c1", "IPY_MODEL_e7d79651f243467db2063397c1abb908", "IPY_MODEL_d60c8f385f0e41e69dc8154abe9e6ce9"], "layout": "IPY_MODEL_8e45ff51a8764cc3ba00f4b9778808e7"}}, "b683edbd3faa4afbbfab126d6460b8a9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"description_width": "", "font_size": null, "text_color": null}}, "b700daf28d9743d8a96121ddd5cf80ed": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {}}, "b8495c5e93894672bae7ef7c644d4b46": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"description_width": "", "font_size": null, "text_color": null}}, "b974515017d240b5aa5b6fd7a620f1fc": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"layout": "IPY_MODEL_45e916b2b4134864acaf6ac0f1bf2ce3", "style": "IPY_MODEL_4cf78dfda4dc40fc8791b26b6e44d531", "value": "Fetching 7 files: 100%"}}, "b9c28aae92fb44a4b6e91b5a2c88a6a6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"description_width": "", "font_size": null, "text_color": null}}, "c01898fa29294607aded814e0222e602": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {}}, "c24e424a79234039bebd33c007134365": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"description_width": "", "font_size": null, "text_color": null}}, "c630ee259e77449e9ba8fd039784407e": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {}}, "c686ed158b5e4a9d9d8c122bb8809dc9": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {}}, "c9a4197d90e14ee4855f7244072cf822": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {}}, "cbaaa6c1b3a24119a451a3ebfb6c9839": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "FloatProgressModel", "state": {"bar_style": "success", "layout": "IPY_MODEL_73280e390d414e72bb5592a523bbb0c3", "max": 7, "style": "IPY_MODEL_e5b0538b81c14c40b745f2fc5c1244a2", "value": 7}}, "cbdfb281146847d5b30067c943fbf5c1": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"layout": "IPY_MODEL_c01898fa29294607aded814e0222e602", "style": "IPY_MODEL_2b1a71105c5a47fca421708da9c6b4b6", "value": "Fetching 7 files: 100%"}}, "d0c583a11d26420a9b2938d037648570": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {}}, "d34154b22a12460eaf696f10c56057a0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "ProgressStyleModel", "state": {"description_width": ""}}, "d60c8f385f0e41e69dc8154abe9e6ce9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"layout": "IPY_MODEL_ad6e9b7742384f86b1803b176e6d0f0b", "style": "IPY_MODEL_c24e424a79234039bebd33c007134365", "value": " 7/7 [00:00&lt;00:00, 606.06it/s]"}}, "d956d3f6b9104836912ea9cbe2888ce9": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {}}, "d9ad63f022c44b94aac870c7ba7216c1": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"layout": "IPY_MODEL_5c91eb86616d4e3285d8580e2378d58f", "style": "IPY_MODEL_8f588bc4501d482690d350c164aab19a", "value": "Fetching 7 files: 100%"}}, "e0578c803518400f8a0f331ad0f2ddcd": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"description_width": "", "font_size": null, "text_color": null}}, "e5b0538b81c14c40b745f2fc5c1244a2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "ProgressStyleModel", "state": {"description_width": ""}}, "e7d79651f243467db2063397c1abb908": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "FloatProgressModel", "state": {"bar_style": "success", "layout": "IPY_MODEL_55ac9ee7c76345eab592c7f95ef78a66", "max": 7, "style": "IPY_MODEL_2ae0cf20d1914d9c9edea83baa98ecde", "value": 7}}, "efcafe17f3154107b02f7878b96d95c7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HBoxModel", "state": {"children": ["IPY_MODEL_cbdfb281146847d5b30067c943fbf5c1", "IPY_MODEL_628f776b65864836b4419a91767e7fe0", "IPY_MODEL_024b312bd6a54abb85fa202a4b2018ad"], "layout": "IPY_MODEL_71de327259bf462982866ac8dd2280bb"}}, "f29987445276454a9022e081dd5e5caa": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "FloatProgressModel", "state": {"bar_style": "success", "layout": "IPY_MODEL_d0c583a11d26420a9b2938d037648570", "max": 8, "style": "IPY_MODEL_b0b866fc1af448e1924e813db823c68c", "value": 8}}, "f7f6ca1df44f4b16b0ed6678a331ff00": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {}}, "fa59b02c7b4a48a7bfeca13deb3e307c": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {}}}, "version_major": 2, "version_minor": 0}}}, "nbformat": 4, "nbformat_minor": 4}