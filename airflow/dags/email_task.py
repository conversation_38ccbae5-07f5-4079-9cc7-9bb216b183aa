from datetime import datetime
from airflow import DAG

default_args = {
    'start_date': datetime(2023, 4, 30),
    'schedule_interval': None,
    'timezone': 'Asia/Seoul'
}

dag = DAG(
    'email_test',
    default_args=default_args,
)

from airflow.operators.python_operator import Python<PERSON>perator
import smtplib
from email.mime.text import MIMEText

def send_email_notification(context):
    try:
        # get the details of the completed task
        dag_id = context['dag'].dag_id
        task_id = context['task_instance'].task_id
        execution_date = context['ts']
        email_from = '<EMAIL>'
        email_to = '<EMAIL>'
        email_subject = f"Airflow job {task_id} in DAG {dag_id} completed successfully"
        email_body = f"The Airflow job {task_id} in DAG {dag_id} completed successfully at {execution_date}."

        # create the message
        message = MIMEText(email_body)
        message['From'] = email_from
        message['To'] = email_to
        message['Subject'] = email_subject

        # send the message
        server = smtplib.SMTP('smtp.naver.com', 587)
        server.starttls()
        server.login('youhs4554', '@ghtkd1234')
        server.sendmail(email_from, email_to, message.as_string())
        server.quit()

        print("Email sent!")
    except Exception as e:
        print(f"Error sending email: {e}")


task1 = PythonOperator(
    task_id='task1',
    python_callable=lambda : 10,
    on_success_callback=send_email_notification,
    dag=dag
)

task2 = PythonOperator(
    task_id='task2',
    python_callable=lambda : 20,
    on_success_callback=send_email_notification,
    dag=dag
)

task3 = PythonOperator(
    task_id='task3',
    python_callable=lambda: 30,
    on_success_callback=send_email_notification,
    dag=dag
)

task1 >> task2 >> task3

#send_email = PythonOperator(
#    task_id='send_email',
#    python_callable=send_email_notification,
#    provide_context=True,
#    dag=dag,
#    on_success_callback=send_email_notification,
#)

# Add the email task as the final task in your DAG
#send_email

