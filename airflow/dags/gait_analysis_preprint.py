from airflow.operators.dummy import DummyOperator
from airflow.operators.python import <PERSON><PERSON><PERSON><PERSON>, BranchPythonOperator
from airflow.utils.task_group import TaskGroup
from airflow import DAG
from datetime import datetime

with DAG(dag_id="gait_analysis_pipeline", 
         start_date=datetime(2022, 10 ,21),) as dag:
  with TaskGroup(group_id="data_sourcing") as data_sourcing:
    data_sourcing = DummyOperator(task_id="data_sourcing")

  with TaskGroup(group_id="preprocess", tooltip="Tasks for preprocess") as prepro_group:
    trim_video = DummyOperator(task_id="trim_video")
    detect_patient = DummyOperator(task_id="detect_patient_Yolo")
    data_cleansing = DummyOperator(task_id="data_cleansing")
    [trim_video,detect_patient] >> data_cleansing

  with TaskGroup('model_training') as model_training_group:
    hyper_tune = DummyOperator(task_id="hyperparamter_tunning")
    training_model_a = DummyOperator(
            task_id='training_model_a',
        )
    training_model_b = DummyOperator(
            task_id='training_model_b',
        )

    training_model_c = DummyOperator(
            task_id='training_model_c',
        )
    [training_model_a>>hyper_tune, training_model_b>>hyper_tune, training_model_c>>hyper_tune]

  with TaskGroup("model_evaluation") as model_evaluation_group:
    model_evaluation = DummyOperator(
        task_id='model_evaluation'
    )
    visualization = DummyOperator(task_id="visualization")
    
  with TaskGroup("deployment") as deployment_group:
    performance_analysis = DummyOperator(task_id="performance_analysis")
    dockerizing = DummyOperator(task_id="dockerizing")
    deployment = DummyOperator(task_id="deployment_to_cloud")
    performance_analysis>>dockerizing>>deployment

  notification = DummyOperator(task_id="summary_notification")
  data_sourcing>>prepro_group>>model_training_group>>model_evaluation_group>>deployment_group>>notification
