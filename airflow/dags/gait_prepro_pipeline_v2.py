from datetime import datetime
from airflow import DAG
from airflow.operators.bash import <PERSON><PERSON><PERSON><PERSON><PERSON>
from airflow.operators.python_operator import Python<PERSON>perator
from airflow.operators.python import BranchPythonOperator
from airflow.models.param import Param
from airflow.operators.dummy import DummyOperator

import smtplib
from email.mime.text import MIMEText

def send_email_notification(context):
    try:
        # get the details of the completed task
        dag_id = context['dag'].dag_id
        task_id = context['task_instance'].task_id
        execution_date = context['ts']
        email_from = '<EMAIL>'
        email_to = '<EMAIL>'
        email_subject = f"Airflow job {task_id} in DAG {dag_id} completed successfully"
        email_body = f"The Airflow job {task_id} in DAG {dag_id} completed successfully at {execution_date}."

        # create the message
        message = MIMEText(email_body)
        message['From'] = email_from
        message['To'] = email_to
        message['Subject'] = email_subject

        # send the message
        server = smtplib.SMTP('smtp.naver.com', 587)
        server.starttls()
        server.login('youhs4554', 'Dbwltkd1emd!')
        server.sendmail(email_from, email_to, message.as_string())
        server.quit()

        print("Email sent!")
    except Exception as e:
        print(f"Error sending email: {e}")


import pendulum

default_args = {
    'owner': 'hossay',
    'start_date': pendulum.datetime(2023, 5, 1, tz="Asia/Seoul"),
    'retries': 0,
    'email_on_failure': False,
    'email_on_retry': False,
    'schedule_interval': None,
    'timezone': 'Asia/Seoul',
    'catchup': False,
}

dag = DAG(
    'gait_prepro_pipeline_v2',
    default_args=default_args,
    description="MDB file paring v2 (chunked due to memory overflow); latest",
    schedule=None,
    render_template_as_native_obj=True,
    params={
         "auto_run": False,
         "timestamp": "",
         "save_full_detection_video": False
    },
)

import os
SCRIPT_FOLDER = f"{os.environ['HOME']}/airflow/scripts"
auto_run = "{{ params.auto_run }}"

run_status = 'auto' if auto_run else 'manual'
timestamp = "{{ params.timestamp }}"
save_full_detection_video = "{{ params.save_full_detection_video }}"
# Step 1 : MDB parsing
parse_mdb = BashOperator(
    task_id='parse_mdb_v2',
    bash_command=f'/home/<USER>/projects/Notebooks/gaitrite-parser-mercury/venv/bin/python {SCRIPT_FOLDER}/parse_mdb_v2.py {run_status}',
    on_success_callback=send_email_notification,
    dag=dag,
)

# Step 2 : Trimming video file and extract video frames
trim_video_and_extract_frames = BashOperator(
    task_id='trim_video',
    bash_command=f'/home/<USER>/projects/Notebooks/gaitrite-parser-mercury/venv/bin/python {SCRIPT_FOLDER}/trim_video.py v2 {run_status} {timestamp}',
    on_success_callback=send_email_notification,
    dag=dag,
)

# Step 3 : Patient localization using yolov4 + deepsort
loc_patient = BashOperator(
    task_id='loc_patient',
    bash_command=f'/home/<USER>/projects/Notebooks/gaitrite-pipeline/yolov4-deepsort/venv/bin/python {SCRIPT_FOLDER}/yolov4-deepsort.py v2 {run_status} {timestamp} {save_full_detection_video}',
    on_success_callback=send_email_notification,
    dag=dag,
)

# # (optional) Step 4 : Copy over ssh to computation server (server #1)
# scp_run = BashOperator(
#     task_id='scp_run',
#     bash_command='scp -r /data/GAITRite-dataset/v2 hossay@**************:/data/hossay/GAITRite-dataset',
#     on_success_callback=send_email_notification,
#     dag=dag,
# )

parse_mdb >> trim_video_and_extract_frames >> loc_patient # >> scp_run
