#!/usr/bin/env python
# coding: utf-8
# defaults

import sys, os

version = sys.argv[1]  # "v1" : all-at-once (previous), "v2" : chunked (due to .mdb overflow issue)

if 'v2' in version:
    version, run_status = version.split('-')

assert run_status in ['auto', 'manual']

dataset_root = f"/data/GAITRite-dataset/{version}"

if version == 'v1':
    mdb_file = f"{dataset_root}/mdb/GAITRite_convert.mdb" # access 2002-2003 버전으로 변환된 mdb 파일
    video_path = f"{dataset_root}/video"
    output_filename = "gaitrite_full_dataset.xlsx"
elif version == 'v2':
    from natsort import natsorted
    from glob import glob

    if run_status == 'auto':
        # process only latest ones
        mdb_file = str(sys.argv[2])  # access 2002-2003 버전으로 변환된 mdb 파일
        video_path = str(sys.argv[3])

        timestamp = os.path.basename(video_path).split('auto_')[1]
        output_filename = f"gaitrite_dataset_{timestamp}.xlsx"

    elif run_status == 'manual':
        chunk_index = int(sys.argv[2])

        mdb_files = natsorted(glob(f"{dataset_root}/mdb/*.mdb"))
        video_folders = natsorted(glob(f"{dataset_root}/video/*"))

        assert 0 <= chunk_index < len(mdb_files)

        mdb_file = mdb_files[chunk_index]  # access 2002-2003 버전으로 변환된 mdb 파일
        video_path = video_folders[chunk_index]
        output_filename = f"gaitrite_dataset_{chunk_index}.xlsx"
print(f"Parsing {mdb_file}...")

video_save_path = f"{dataset_root}/video_formated"
limit = 5
target = ['Velocity', 'Cadence',
          'Cycle_Time_L', 'Cycle_Time_R',
          'Stride_Len_L', 'Stride_Len_R',
          'StrideVelocity_L', 'StrideVelocity_R',
          'Supp_Base_L', 'Supp_Base_R',
          'Swing_Perc_L', 'Swing_Perc_R',
          'Stance_Perc_L', 'Stance_Perc_R',
          'D_Supp_PercL', 'D_Supp_PercR',
          'ToeInOutL', 'ToeInOutR',
          'StrideLen_SD_L', 'StrideLen_SD_R',
          'StrideTm_SD_L', 'StrideTm_SD_R']


import warnings

warnings.filterwarnings("ignore")

import pandas as pd
import pandas_access

dataset_df = pandas_access.read_table(mdb_file, "GAITRite")  # 전체 데이터셋
patient_map = pandas_access.read_table(mdb_file, "Patient", usecols=["Pt_id", "Pt_number"])  # 환자 id -> 차트번호 매핑 테이블
contact_intervals = pandas_access.read_table(mdb_file, "Test", usecols=["Gait_Id", "Pt_id", "FirstContact",
                                                                        "LastContact", "Xback"])  # 보행 시작/끝 타이밍 정보

def convert_to_numeric(col, tgt=target):
    is_numeric = col.astype(str).str.isnumeric().all()
    if not is_numeric and col.name in tgt:
        col = pd.to_numeric(col, errors="coerce")
    return col

from functools import partial

# string으로 저장된 target 컬럼 중에 numeric 컬럼을 numeric으로 변환
dataset_df = dataset_df.apply(convert_to_numeric)
patient_map = patient_map.apply(convert_to_numeric)
contact_intervals = contact_intervals.apply(partial(convert_to_numeric, tgt=["FirstContact", "LastContact", "Xback"]))
contact_intervals = contact_intervals.query("FirstContact > 0 and LastContact > 0")


patient_map = pd.Series(patient_map["Pt_number"].values, index=patient_map["Pt_id"])
dataset_df["Pt_number"] = dataset_df["Pt_id"].map(patient_map)

columns = dataset_df.columns
new_columns = [columns[-1]] + columns[0:-1].to_list()
dataset_df = dataset_df[new_columns]

import numpy as np


def check_validty(x):
    return np.nan if x == " " else x


# 빈 문자열을 nan으로 대체 후 dropna
dataset_df["VideoName"] = dataset_df["VideoName"].map(check_validty)
dataset_df.dropna(subset=["VideoName"], inplace=True)

dataset_df.rename(columns={"id": "Gait_Id"}, inplace=True)  # id -> Gait_id로 이름 변경 (id는 Gait_id와 같음)

from collections import defaultdict

contact_intervals_grouped = contact_intervals.groupby("Gait_Id", group_keys=True)
contact_intervals_merged = defaultdict(list)  # 여러개의 Gait_id로 나눠져있는 footprint contact intervals를 합산한 데이터를 담는 컨테이너
for gait_id, index_list in contact_intervals_grouped.groups.items():
    first_contact_list, last_contact_list, x_traces = contact_intervals.loc[index_list, ["FirstContact", "LastContact", "Xback"]].values.T
    
    first_contact, last_contact = first_contact_list[0], last_contact_list[-1]
    distance = abs(x_traces[0]-x_traces[-1])
    if distance > 300:
        contact_intervals_merged["Gait_Id"].append(gait_id)
        # contact_intervals_merged["Pt_id"].append(first_contact_row["Pt_id"]) # Pt_id는 더이상 불필요함
        contact_intervals_merged["FirstContact"].append(first_contact)  # Gait record 전체에서의 FirstContact
        contact_intervals_merged["LastContact"].append(last_contact)  # Gait record 전체에서의 LastContact

contact_intervals_merged = pd.DataFrame(contact_intervals_merged)
full_dataset_df = dataset_df.join(contact_intervals_merged.set_index("Gait_Id"), on="Gait_Id", how='inner')

full_dataset_df.drop_duplicates(inplace=True)
full_dataset_df.reset_index(inplace=True)

pd.set_option('display.max_columns', None)  # 모든 컬럼을 확인하기 위해서

import os
from pathlib import Path

# 비디오가 실제로 존재하는 샘플만 필터링
video_home_path = Path(video_path).resolve()
full_dataset_df["VideoFile"] = full_dataset_df["VideoName"].map(lambda x: video_home_path / x.strip("\\")).astype(str)

# 파일 경로 존재여부를 확인하는 필터
filter_exist = full_dataset_df["VideoFile"].map(lambda x: os.path.exists(x))

if sum(filter_exist) == 0:
    print("Exiting due to any video file has not been matched")
    sys.exit(0)

# 비디오 파일이 실제로 존재하는 row 필터링
full_dataset_df = full_dataset_df[filter_exist]

# FirstContact나 LastContact 없는 행 drop!
full_dataset_df.dropna(subset=["FirstContact", "LastContact"], inplace=True)


def reformat_name(row):
    """
    비디오 파일명 변환:
      {pid}_test_{test_idx}_trial_{trial_idx}.avi <- 예전에 사용했던 포맷
    """

    pid = row['Pt_number'].unique()[0]
    test_idxs = row['TestSetId']
    trial_idxs = range(len(row))

    row['VideoFile_new'] = [os.path.join(video_save_path,
                                         f"{pid}_test_{test_idx}_trial_{trial_idx}.avi") for test_idx, trial_idx in
                            zip(test_idxs, trial_idxs)]
    return row


full_dataset_df = full_dataset_df.groupby('Pt_number').apply(reformat_name)

# 비디오 저장경로에 저장하기
import subprocess

os.makedirs(video_save_path, exist_ok=True)
for _, row in full_dataset_df.iterrows():
    src_path = row['VideoFile']
    target_path = row['VideoFile_new']
    subprocess.run(['rsync', '-azh', src_path, target_path])

del full_dataset_df["VideoFile"]
full_dataset_df.rename({'VideoFile_new': 'VideoFile'}, axis=1, inplace=True)

default_columns = ["Pt_number", "Gait_Id", "Pt_id", "TestSetId", "VideoFile", "FirstContact",
                   "LastContact", "Time"]  # id와 질환과 관련된 정보는 기본적으로 포함됨
full_dataset_df = full_dataset_df.loc[:, default_columns + target]

# Add StrideTm columns
full_dataset_df["StrideTm_L"] = full_dataset_df["Stride_Len_L"] / full_dataset_df["StrideVelocity_L"]
full_dataset_df["StrideTm_R"] = full_dataset_df["Stride_Len_R"] / full_dataset_df["StrideVelocity_R"]

# Add coefficient of variation (CV) columns
full_dataset_df["StrideLen_CV_L"] = full_dataset_df["StrideLen_SD_L"] / (full_dataset_df["Stride_Len_L"] / 100.0)
full_dataset_df["StrideLen_CV_R"] = full_dataset_df["StrideLen_SD_R"] / (full_dataset_df["Stride_Len_R"] / 100.0)

full_dataset_df["StrideTm_CV_L"] = full_dataset_df["StrideTm_SD_L"] / (full_dataset_df["StrideTm_L"] / 100.0)
full_dataset_df["StrideTm_CV_R"] = full_dataset_df["StrideTm_SD_R"] / (full_dataset_df["StrideTm_R"] / 100.0)



# Data cleansing
full_dataset_df.dropna(subset=["Pt_number"], inplace=True)  # Pt_number 없는 행 제거
full_dataset_df = full_dataset_df.apply(convert_to_numeric,
                                        tgt=["FirstContact", "LastContact"])  # convert into numeric types

if version == "v1":
  print("## Dataset Preview ##")
  print(full_dataset_df.head(limit))
  print("Dataset size: " + str(full_dataset_df.shape))

if run_status == 'auto':
    new_output_filename = os.path.join(dataset_root, 'auto_datasets', output_filename)
    os.makedirs(os.path.dirname(new_output_filename), exist_ok=True)
    print("full_dataset:", full_dataset_df)
    full_dataset_df.to_excel(new_output_filename, index=False)
elif run_status == 'manual':
    new_output_filename = os.path.join(dataset_root, output_filename)
    full_dataset_df.to_excel(new_output_filename, index=False)
