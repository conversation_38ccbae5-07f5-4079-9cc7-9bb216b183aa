#!/usr/bin/env python
# coding: utf-8
# defaults

from pathlib import Path
import subprocess
import os, sys
import pandas as pd

version = "v2"  # will be given from script
dataset_root = f"/data/GAITRite-dataset/{version}"
output_filename = "gaitrite_full_dataset.xlsx"
python_interpreter = "/home/<USER>/projects/Notebooks/gaitrite-parser-mercury/venv/bin/python"

assert len(os.listdir(f"{dataset_root}/mdb")) == len(os.listdir(f"{dataset_root}/video")), "in version 2, the number of mdb files should be same with video subfolders."

SCRIPT_FOLDER = f"{os.environ['HOME']}/airflow/scripts"

# process only latest ones
mdb_files = sorted(Path(f"{dataset_root}/mdb").iterdir(), key=os.path.getctime, reverse=True)
video_folders = sorted(Path(f"{dataset_root}/video").iterdir(), key=os.path.getctime, reverse=True)

if len(mdb_files) != len(video_folders):
    raise Exception('mdb files and video folders are not matched!')

# process only latest ones
mdb_file = mdb_files[0]  # access 2002-2003 버전으로 변환된 mdb 파일
video_path = video_folders[0]
timestamp = os.path.basename(video_path).split('auto_')[1]

ret = subprocess.run([python_interpreter, f'{SCRIPT_FOLDER}/parse_mdb.py', version + '-auto', mdb_file, video_path])
new_output_filename = os.path.join(dataset_root, f"gaitrite_dataset_{timestamp}.xlsx")

new_df_full = pd.read_excel(new_output_filename).reset_index()
os.remove(new_output_filename)

org_file = os.path.join(dataset_root, output_filename)

if os.path.exists(org_file):
    org_df_full = pd.read_excel(org_file)
    df_full = pd.concat([org_df_full, new_df_full], axis=0).reset_index()
    df_full.to_excel(os.path.join(dataset_root, output_filename), index=False)

new_df_full.to_excel(os.path.join(dataset_root, new_output_filename), index=False)
print("## Dataset Preview ##")
print(new_df_full.head())

new_df_full.to_excel(os.path.join(dataset_root, new_output_filename), index=False)
print("Dataset size: " + str(new_df_full.shape))
