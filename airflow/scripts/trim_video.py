import pandas as pd
import os, sys
import cv2
from pathlib import Path
from tqdm import tqdm
import pickle
import re
import moviepy.editor as mp
from PIL import Image
from utils import read_excel

def write_trimmed_video_frames(video, start_time, end_time):
    """
    비디오 트리밍 후 비디오와 프레임 함께 저장
    """

    video_dir, filename = os.path.split(video)
    video_dir = str(Path(video_dir).resolve())  # symbolic target

    output_dir = os.path.splitext(video_dir)[0] + "_trim"
    output_file = os.path.join(output_dir, filename)

    cap = cv2.VideoCapture(video)
    ret, frame = cap.read()
    h, w = frame.shape[:-1]
    fourcc = cv2.VideoWriter_fourcc(*'DIVX')

    frame_dir = os.path.join(output_dir.replace("video_formated_trim", "frame"), filename[:-4])

    os.makedirs(output_dir, exist_ok=True)
    os.makedirs(frame_dir, exist_ok=True)

    fps = cap.get(cv2.CAP_PROP_FPS)

    # Open the video file
    video = mp.VideoFileClip(video)

    # Extract the specified time interval as a subclip
    subclip = video.subclip(start_time, end_time)

    # Save the subclip as a video file
    subclip.write_videofile(output_file, codec='libx264')

    # Extract the frames from the output video file
    output_video = mp.VideoFileClip(output_file)
    frames = output_video.iter_frames()

    # Set the output frame file format
    frame_file_format = "thumb{:04d}.jpg"

    start_ix = int(fps * start_time)  # 시작 인덱스

    # Save each frame as an image file
    for i, frame in enumerate(frames):
        frame_file = os.path.join(frame_dir, frame_file_format.format(start_ix + i))
        Image.fromarray(frame).save(frame_file)

def format_index(filename):
    s = os.path.basename(filename)
    numbers = re.findall('\d+', s)
    result = '_'.join(numbers)
    return result

if __name__ == "__main__":
    version = sys.argv[1]
    run_status = sys.argv[2]
    timestamp = sys.argv[3]

    # excel file created by parse_mdb.ipynb (through web UI)
    dataset_file = f"/data/GAITRite-dataset/{version}/gaitrite_full_dataset.xlsx"

    if run_status == 'auto' and timestamp:
        dataset_file = f"/data/GAITRite-dataset/{version}/auto_datasets/gaitrite_dataset_{timestamp}.xlsx"
    else:
        raise ValueError("Invalid argument has given")

    full_dataset_df = read_excel(dataset_file)
    #full_dataset_df = full_dataset_df.iloc[:10]
    #full_dataset_df = full_dataset_df.query(" VideoFile == '/data/GAITRite-dataset/v2/video_formated/7301137_test_2_trial_4.avi' ")

    mask = []
    # 시간 오래걸림 주의
    for video, start, end in tqdm(
            list(zip(*full_dataset_df.loc[:, ["VideoFile", "FirstContact", "LastContact"]].T.values))):
        is_valid = True
        try:
            write_trimmed_video_frames(video, start, end)
        except:
            is_valid = False
        mask.append(is_valid)

    full_dataset_df = full_dataset_df.loc[mask] # filter only valid file
    full_dataset_df["VideoFile"] = full_dataset_df["VideoFile"].str.replace('video_formated', 'video_formated_trim')
    full_dataset_df['index'] = full_dataset_df['VideoFile'].map(format_index)
    full_dataset_df.set_index('index', inplace=True)

    # save excel file
    full_dataset_df.to_excel(dataset_file, index=True)
    
    # save pickle file (as used in previous code)
    full_dataset_df.index.name = '' # remove index name (for compatability)
    dot_index = dataset_file.rfind('.')
    new_dataset_file = dataset_file[:dot_index] + '.pkl'
    with open(new_dataset_file, 'wb') as file:
        pickle.dump(full_dataset_df, file)
