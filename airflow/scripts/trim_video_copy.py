import pandas as pd
import os, sys
import cv2
from pathlib import Path
from tqdm import tqdm
import pickle
import re

def write_trimmed_video_frames(video, start, end):
    """
    비디오 트리밍 후 비디오와 프레임 함께 저장
    """

    cap = cv2.VideoCapture(video)
    ret, frame = cap.read()
    if not ret:
        return False, video
    h, w = frame.shape[:-1]
    fourcc = cv2.VideoWriter_fourcc(*'DIVX')

    video_dir, filename = os.path.split(video)
    video_dir = str(Path(video_dir).resolve())  # symbolic target

    output_dir = os.path.join(os.path.splitext(video_dir)[0] + "_trim")

    save_path = os.path.join(output_dir, filename)
    frame_dir = os.path.join(output_dir.replace("video_formated_trim", "frame"), filename[:-4])

    os.makedirs(output_dir, exist_ok=True)
    os.makedirs(frame_dir, exist_ok=True)

    fps = cap.get(cv2.CAP_PROP_FPS)

    start_ix = int(fps * start)  # 시작 인덱스
    end_ix = int(fps * end)  # 끝 인덱스

    out = cv2.VideoWriter(save_path, fourcc, 25.0, (w, h))

    cap.set(cv2.CAP_PROP_POS_FRAMES, start_ix)  # start 부터 end 까지 비디오 저장
    while cap.get(cv2.CAP_PROP_POS_FRAMES) < end_ix:
        ret, frame = cap.read()
        if not ret: break

        # write video stream
        out.write(frame)

        frame_index = int(cap.get(cv2.CAP_PROP_POS_FRAMES))

        # save video frame
        cv2.imwrite(os.path.join(frame_dir, f"thumb{frame_index:04d}.jpg"), frame)

    cap.release()
    out.release()
    return True, save_path

def format_index(filename):
    s = os.path.basename(filename)
    numbers = re.findall('\d+', s)
    result = '_'.join(numbers)
    return result

if __name__ == "__main__":
    version = sys.argv[1]

    # excel file created by parse_mdb.ipynb (through web UI)
    dataset_file = f"/data/GAITRite-dataset/{version}/gaitrite_full_dataset.xlsx"
    full_dataset_df = pd.read_excel(dataset_file)
    #full_dataset_df = full_dataset_df.iloc[:10]

    valid_filenames = []
    mask = []
    # 시간 오래걸림 주의
    for video, start, end in tqdm(
            list(zip(*full_dataset_df.loc[:, ["VideoFile", "FirstContact", "LastContact"]].T.values))):
        ret, filename = write_trimmed_video_frames(video, start, end)
        mask.append(ret)
        if ret:
            valid_filenames.append(filename)

    full_dataset_df.loc[mask, "VideoFile"] = valid_filenames
    full_dataset_df['index'] = full_dataset_df['VideoFile'].map(format_index)
    full_dataset_df.set_index('index', inplace=True)

    # save excel file
    full_dataset_df.to_excel(dataset_file, index=True)
    
    # save pickle file (as used in previous code)
    full_dataset_df.index.name = '' # remove index name (for compatability)
    dot_index = dataset_file.rfind('.')
    new_dataset_file = dataset_file[:dot_index] + '.pkl'
    with open(new_dataset_file, 'wb') as file:
        pickle.dump(full_dataset_df, file)
