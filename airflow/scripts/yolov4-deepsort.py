import subprocess
import os, sys
import pandas as pd
import math
from itertools import islice
from natsort import natsorted
import multiprocessing
import shutil
from pathlib import Path
from utils import read_excel
import tensorflow as tf

def recreate_directory(dirname):
    if os.path.exists(dirname):
        shutil.rmtree(dirname)
    os.makedirs(dirname, exist_ok=True)


if __name__ == '__main__':

    lib_home = "/home/<USER>/projects/Notebooks/gaitrite-pipeline/yolov4-deepsort"
    python_interpreter = "/home/<USER>/projects/Notebooks/gaitrite-pipeline/yolov4-deepsort/venv/bin/python"
    os.chdir(lib_home)

    if len(sys.argv) > 1:
        version, run_status, timestamp, save_full_detection_video = sys.argv[1:]

        assert version in ['v1', 'v2']
        assert run_status in ['auto', 'manual']

    dataset_dirname = f'/data/GAITRite-dataset/{version}'
    output_video_dirname = os.path.join(dataset_dirname, 'video_track')
    tracks_dirname = os.path.join(dataset_dirname, 'tracks')

    os.makedirs(output_video_dirname, exist_ok=True)
    os.makedirs(tracks_dirname, exist_ok=True)

    dataset_file = os.path.join(dataset_dirname, 'gaitrite_full_dataset.xlsx')

    if run_status == 'auto' and timestamp:
        dataset_file = f"/data/GAITRite-dataset/{version}/auto_datasets/gaitrite_dataset_{timestamp}.xlsx"
    
    df = read_excel(dataset_file)
    all_video_files = df["VideoFile"]


    if len(all_video_files) < 10:
        n_workers = 1
    else:
        n_workers = 3 # default : 6

    #gpus_available = [str(i) for i in range(1, n_workers + 1)] # skip gpu "0" due to small memory size
    gpus_available = list('456')
    chunk_size = math.ceil(len(all_video_files)/n_workers)

    def chunk(it, size):
        it = iter(it)
        return iter(lambda: tuple(islice(it, size)), ())

    def run_process(cmd):
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, shell=True)
        output, error = process.communicate()
        return output

    print(len(list(chunk(all_video_files, chunk_size))), len(all_video_files), chunk_size)

    chunks = list(chunk(all_video_files, chunk_size))

    processes = []
    for i, chunk in enumerate(chunks):
        chunk_filename = f'/tmp/chunk{i}.txt'
        video_files = [ line + '\n' for line in chunk ]
        with open(chunk_filename, 'w') as f:
            f.writelines(video_files)
        gpu_device = gpus_available[i % len(gpus_available)]
        os.environ['CUDA_VISIBLE_DEVICES'] = gpu_device
        cmd = f"{python_interpreter} inference.py {chunk_filename} {output_video_dirname} {tracks_dirname} {save_full_detection_video}"
        p = multiprocessing.Process(target=run_process, args=(cmd,))
        p.start()
        processes.append(p)

    for p in processes:
        p.join()

    # concat all csv into a one excel/pickle file
    tracks_files = natsorted(Path(tracks_dirname).glob("*.csv"))
    tracks_concat = pd.concat([pd.read_csv(f) for f in tracks_files], axis=0)
    target_data = read_excel(dataset_file)
    target_data = target_data.loc[target_data['VideoFile'].map(lambda x: Path(x).stem).isin(tracks_concat.vids)]

    out_file = os.path.join(dataset_dirname, "gaitrite_full_dataset.xlsx")
    if os.path.exists(out_file):
        org_df_full = read_excel(out_file)
        df_full = pd.concat([org_df_full, target_data], axis=0).set_index('index').reset_index()
        df_full.drop_duplicates(subset=['index'], inplace=True)
        df_full.to_excel(out_file, index=False)
    else:
        target_data.set_index('index').reset_index().to_excel(out_file, index=False)
    
    tracks_concat.reset_index(drop=True, inplace=True)
    org_track_file = os.path.join(dataset_dirname, "person_detection_and_tracking_results.csv")
    if os.path.exists(org_track_file):
        org_tracks = pd.read_csv(org_track_file)
        full_tracks = pd.concat([org_tracks, tracks_concat], axis=0).set_index('vids').reset_index()
        full_tracks.to_csv(org_track_file, index=False)
        full_tracks.to_pickle(org_track_file.replace('.csv', '.pkl'))
    else:
        tracks_concat.to_csv(os.path.join(dataset_dirname, "person_detection_and_tracking_results.csv"), index=False)
        tracks_concat.to_pickle(os.path.join(dataset_dirname, "person_detection_and_tracking_results.pkl"))
