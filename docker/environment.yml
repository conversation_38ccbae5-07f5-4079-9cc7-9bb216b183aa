name: gait
channels:
  - soft-matter
  - conda-forge
  - r
  - anaconda
  - defaults
dependencies:
  - _libgcc_mutex=0.1=main
  - anyjson=0.3.3=py36_1
  - argon2_cffi=19.1.0=py36h7b6447c_0
  - arrow=0.13.1=py36_0
  - asn1crypto=0.24.0=py36_0
  - astroid=2.2.5=py36_0
  - autopep8=1.4.4=py_0
  - babel=2.7.0=py_0
  - bcrypt=3.1.6=py36h7b6447c_0
  - blas=1.0=mkl
  - cffi=1.12.3=py36h2e261b9_0
  - click=7.0=py36_0
  - colour=0.1.5=py36_0
  - cryptography=2.7=py36h1ba5d50_0
  - cx_oracle=7.0.0=py36h7b6447c_0
  - cycler=0.10.0=py36_0
  - dbus=1.13.6=h746ee38_0
  - entrypoints=0.3=py36_0
  - expat=2.2.6=he6710b0_0
  - flake8=3.7.7=py36_0
  - flask=1.1.1=py_0
  - fontconfig=2.13.0=h9420a91_0
  - freetype=2.9.1=h8a8886c_1
  - furl=2.0.0=py36_0
  - glib=2.56.2=hd408876_0
  - gst-plugins-base=1.14.0=hbbd80ab_1
  - gstreamer=1.14.0=hb453b48_1
  - h5py=2.9.0=py36h7918eee_0
  - hdf5=1.10.4=hb1b8bf9_0
  - idna=2.8=py36_0
  - infinity=1.4=py36_0
  - intel-openmp=2019.3=199
  - intervals=0.8.1=py36_0
  - isort=4.3.21=py36_0
  - itsdangerous=1.1.0=py36_0
  - jinja2=2.10.1=py36_0
  - kiwisolver=1.0.1=py36hf484d3e_0
  - lazy-object-proxy=1.4.2=py36h7b6447c_0
  - libedit=3.1.20181209=hc058e9b_0
  - libgcc-ng=9.1.0=hdf63c60_0
  - libgfortran-ng=7.3.0=hdf63c60_0
  - libpng=1.6.37=hbc83047_0
  - libstdcxx-ng=8.2.0=hdf63c60_1
  - libtiff=4.0.10=h2733197_2
  - libuuid=1.0.3=h1bed415_2
  - libxcb=1.13=h1bed415_1
  - libxml2=2.9.9=he19cac6_0
  - markupsafe=1.1.1=py36h7b6447c_0
  - matplotlib=3.1.1=py36h5429711_0
  - mccabe=0.6.1=py36_1
  - mkl=2019.3=199
  - mkl_fft=1.0.12=py36ha843d7b_0
  - mkl_random=1.0.2=py36hd81dba3_0
  - ncurses=6.1=he6710b0_1
  - ninja=1.9.0=py36hfd86e86_0
  - numpy=1.16.1=py36h7e9f1db_1
  - numpy-base=1.16.1=py36hde5b4d6_1
  - olefile=0.46=py36_0
  - orderedmultidict=1.0.1=py36_0
  - pandas=0.24.2=py36he6710b0_0
  - passlib=1.7.1=py36h28b3542_0
  - pcre=8.43=he6710b0_0
  - phonenumbers=8.10.15=py_0
  - pillow=6.1.0=py36h34e0f95_0
  - pip=19.1.1=py36_0
  - pycodestyle=2.5.0=py36_0
  - pycparser=2.19=py36_0
  - pyflakes=2.1.1=py36_0
  - pylint=2.3.1=py36_0
  - pyparsing=2.4.0=py_0
  - pyqt=5.9.2=py36h05f1152_2
  - python=3.6.8=h0371630_0
  - python-dateutil=2.8.0=py36_0
  - pytz=2019.1=py_0
  - qt=5.9.7=h5867ecd_1
  - readline=7.0=h7b6447c_5
  - setuptools=41.2.0=py36_0
  - sip=4.19.8=py36hf484d3e_0
  - six=1.12.0=py36_0
  - sqlalchemy=1.3.7=py36h7b6447c_0
  - sqlalchemy-utils=0.33.11=py36_0
  - sqlite=3.29.0=h7b6447c_0
  - tk=8.6.8=hbc83047_0
  - tornado=6.0.2=py36h7b6447c_0
  - typed-ast=1.3.4=py36h7b6447c_0
  - werkzeug=0.15.4=py_0
  - wheel=0.33.4=py36_0
  - wrapt=1.11.2=py36h7b6447c_0
  - xz=5.2.4=h14c3975_4
  - zlib=1.2.11=h7b6447c_3
  - zstd=1.3.7=h0b5b093_0
  - av=7.0.1=py36h866369f_0
  - bzip2=1.0.8=h516909a_2
  - ffmpeg=4.1.3=h167e202_0
  - gmp=6.2.0=he1b5a44_2
  - gnutls=3.6.5=hd3a4fd2_1002
  - lame=3.100=h14c3975_1001
  - libiconv=1.15=h516909a_1005
  - nettle=3.4.1=h1bed415_1002
  - openh264=1.8.0=hdbcaa40_1000
  - x264=1!152.20180806=h14c3975_0
  - binutils_impl_linux-64=2.33.1=he6710b0_7
  - binutils_linux-64=2.33.1=h9595d00_15
  - ca-certificates=2019.8.28=0
  - cairo=1.14.12=h8948797_3
  - certifi=2019.9.11=py36_0
  - curl=7.69.1=hbc83047_0
  - fribidi=1.0.5=h7b6447c_0
  - gcc_impl_linux-64=7.3.0=habb00fd_1
  - gcc_linux-64=7.3.0=h553295d_15
  - gfortran_impl_linux-64=7.3.0=hdf63c60_1
  - gfortran_linux-64=7.3.0=h553295d_15
  - graphite2=1.3.13=h23475e2_0
  - gxx_impl_linux-64=7.3.0=hdf63c60_1
  - gxx_linux-64=7.3.0=h553295d_15
  - harfbuzz=1.8.8=hffaf4a1_0
  - icu=58.2=h9c2bf20_1
  - jpeg=9b=h024ee3a_2
  - krb5=1.17.1=h173b8e3_0
  - ld_impl_linux-64=2.33.1=h53a641e_7
  - libcurl=7.69.1=h20c2e04_0
  - libffi=3.2.1=hd88cf55_4
  - libssh2=1.9.0=h1ba5d50_1
  - make=4.2.1=h1bed415_1
  - openssl=1.1.1d=h7b6447c_1
  - pango=1.42.4=h049681c_0
  - pixman=0.38.0=h7b6447c_0
  - _r-mutex=1.0.0=mro_2
  - mro-base=3.5.1=3
  - mro-base_impl=3.5.1=h9a62091_0
  - r-assertthat=0.2.0=mro351hf348343_0
  - r-bh=1.66.0_1=mro351hf348343_0
  - r-bindr=0.1.1=mro351hf348343_0
  - r-bindrcpp=0.2.2=mro351hebc1506_0
  - r-bit=1.1_14=mro351hd10c6a6_0
  - r-bit64=0.9_7=mro351hd10c6a6_0
  - r-blob=1.1.1=mro351hf348343_0
  - r-cli=1.0.0=mro351hf348343_0
  - r-crayon=1.3.4=mro351hf348343_0
  - r-dbi=1.0.0=mro351hf348343_0
  - r-dbplyr=1.2.2=mro351hf348343_0
  - r-digest=0.6.15=mro351hd10c6a6_0
  - r-dplyr=0.7.6=mro351hebc1506_0
  - r-fansi=0.2.3=mro351hd10c6a6_0
  - r-glue=1.3.0=mro351hd10c6a6_0
  - r-magrittr=1.5=mro351hf348343_0
  - r-memoise=1.1.0=mro351hf348343_0
  - r-pillar=1.3.0=mro351hf348343_0
  - r-pkgconfig=2.0.1=mro351hf348343_0
  - r-plogr=0.2.0=mro351hf348343_0
  - r-prettyunits=1.0.2=mro351hf348343_0
  - r-purrr=0.2.5=mro351hd10c6a6_0
  - r-r6=2.2.2=mro351hf348343_0
  - r-rcpp=0.12.18=mro351hebc1506_0
  - r-revoutils=11.0.0=mro351_0
  - r-revoutilsmath=11.0.0=mro351_0
  - r-rlang=0.2.1=mro351hd10c6a6_0
  - r-rsqlite=2.1.1=mro351hebc1506_0
  - r-tibble=1.4.2=mro351hd10c6a6_0
  - r-tidyselect=0.2.4=mro351hebc1506_0
  - r-utf8=1.1.4=mro351hd10c6a6_0
  - rpy2=2.9.4=py36mro351h6853232_0
  - pyav=v0.2.2.post40=40_ge6dd987_py27
  - pip:
    - absl-py==0.7.1
    - albumentations==0.4.5
    - appdirs==1.4.3
    - astor==0.8.0
    - attrs==19.1.0
    - backcall==0.1.0
    - backports.weakref==1.0rc1
    - black==19.3b0
    - bleach==1.5.0
    - cachetools==3.1.1
    - chardet==3.0.4
    - cloudpickle==1.2.1
    - dask==1.2.2
    - dask-kubernetes==0.8.0
    - decorator==4.4.0
    - defusedxml==0.6.0
    - distributed==1.28.1
    - easydict==1.9
    - enum34==1.1.6
    - future==0.18.2
    - gast==0.2.2
    - google-auth==1.6.3
    - google-auth-oauthlib==0.4.1
    - google-pasta==0.2.0
    - grpcio==1.29.0
    - heapdict==1.0.0
    - html5lib==0.9999999
    - imageio==2.5.0
    - imbalanced-learn==0.7.0
    - imblearn==0.0
    - imgaug==0.2.6
    - imutils==0.5.3
    - ipykernel==5.1.0
    - ipython==7.4.0
    - ipython-genutils==0.2.0
    - ipywidgets==7.4.2
    - jedi==0.13.3
    - joblib==0.13.2
    - jsonpatch==1.24
    - jsonpointer==2.0
    - jsonschema==3.0.1
    - jupyter==1.0.0
    - jupyter-client==5.2.4
    - jupyter-console==6.0.0
    - jupyter-core==4.4.0
    - keras-applications==1.0.8
    - keras-preprocessing==1.1.2
    - kubernetes==9.0.0
    - markdown==3.1.1
    - matplotlib-venn==0.11.5
    - mistune==0.8.4
    - msgpack==0.6.1
    - natsort==6.0.0
    - nbconvert==5.4.1
    - nbformat==4.4.0
    - networkx==2.3
    - nose==1.3.7
    - notebook==5.7.8
    - numexpr==2.7.1
    - oauthlib==3.0.1
    - pandas-flavor==0.2.0
    - pandocfilters==1.4.2
    - parso==0.4.0
    - patsy==0.5.1
    - pexpect==4.7.0
    - pickleshare==0.7.5
    - pingouin==0.3.1
    - prometheus-client==0.6.0
    - prompt-toolkit==2.0.9
    - protobuf==3.8.0
    - psutil==5.6.3
    - ptyprocess==0.6.0
    - pyasn1==0.4.5
    - pyasn1-modules==0.2.5
    - pygments==2.3.1
    - pyrsistent==0.14.11
    - pywavelets==1.0.3
    - pyyaml==5.1.1
    - pyzmq==18.0.1
    - qtconsole==4.4.3
    - requests==2.22.0
    - requests-oauthlib==1.2.0
    - rsa==4.0
    - scikit-image==0.15.0
    - scikit-learn==0.23.2
    - scipy==1.4.1
    - seaborn==0.9.0
    - send2trash==1.5.0
    - skorch==0.5.0.post0
    - sortedcontainers==2.1.0
    - statsmodels==0.10.0
    - tables==3.6.1
    - tabulate==0.8.3
    - tblib==1.4.0
    - termcolor==1.1.0
    - terminado==0.8.2
    - testpath==0.4.2
    - threadpoolctl==2.1.0
    - toml==0.10.0
    - toolz==0.9.0
    - tqdm==4.46.0
    - traitlets==4.3.2
    - tzlocal==2.1
    - urllib3==1.25.3
    - visdom==*******
    - visualdl==1.3.0
    - wcwidth==0.1.7
    - webencodings==0.5.1
    - websocket-client==0.56.0
    - widgetsnbextension==3.4.2
    - xarray==0.14.1
    - xlrd==1.2.0
    - xlsxwriter==1.2.7
    - zict==0.1.4

