name: gait
channels:
  - conda-forge
  - defaults
dependencies:
  # ─── Core & <PERSON> ──────────────────────────────────────────
  - python=3.10.14
  - python_abi=3.10
  - pip=24.2
  - _libgcc_mutex=0.1
  - _openmp_mutex=5.1
  - ca-certificates=2025.4.26
  - openssl=3.5.0
  - sqlite=3.45.3
  - xz=5.4.6
  - zlib=1.2.13
  - libffi=3.4.4
  - libgcc-ng=14.2.0
  - libstdcxx-ng=14.2.0

  # ─── Jupyter/IPython ────────────────────────────────────────
  - ipykernel=6.29.5
  - ipython=8.18.1
  - jupyter_client=8.6.3
  - jupyter_core=5.7.2
  - matplotlib-inline=0.1.7
  - nest-asyncio=1.6.0
  - traitlets=5.14.3
  - tornado=6.5
  - comm=0.2.2
  - debugpy=1.8.14

  # ─── Build Toolchain ───────────────────────────────────────
  - gcc_impl_linux-64=14.2.0
  - gxx_impl_linux-64=14.2.0
  - gcc_linux-64=14.2.0
  - gxx_linux-64=14.2.0
  - binutils_impl_linux-64=2.40
  - binutils_linux-64=2.40
  - ld_impl_linux-64=2.40
  - libgomp=14.2.0
  - libsanitizer=14.2.0
  - kernel-headers_linux-64=3.10.0
  - sysroot_linux-64=2.17

  # ─── Common Runtime Libs ────────────────────────────────────
  - ncurses=6.4
  - readline=8.2
  - keyutils=1.6.1
  - krb5=1.21.3
  - libsodium=1.0.20
  - zeromq=4.3.5
  - libedit=3.1.20191231
  - libgcc=14.2.0
  - libgcc-devel_linux-64=14.2.0
  - libstdcxx=14.2.0
  - libstdcxx-devel_linux-64=14.2.0
  - matplotlib-inline=0.1.7
  - asttokens=3.0.0
  - parso=0.8.4
  - pure_eval=0.2.3
  - python-dateutil=2.9.0.post0
  - six=1.17.0
  - stack_data=0.6.3
  - typing_extensions=4.13.2
  - wcwidth=0.2.13
  - wheel=0.44.0
  - platformdirs=4.3.8
  - pexpect=4.9.0
  - ptyprocess=0.7.0
  - openssl=3.5.0
  - tk=8.6.14
  - pyzmq=26.4.0

  # ─── pip-installed packages ────────────────────────────────
  - pip:
      - about-time==4.2.1
      - absl-py==2.1.0
      - aiofiles==24.1.0
      - aioredis==2.0.1
      - alembic==1.12.1
      - alive-progress==3.2.0
      - annotated-types==0.7.0
      - anyio==4.7.0
      - async-timeout==5.0.1
      - attrs==24.3.0
      - authlib==1.6.0
      - autograd==1.7.0
      - bcrypt==4.3.0
      - beautifulsoup4==4.12.3
      - blinker==1.9.0
      - boto3==1.38.20
      - botocore==1.38.20
      - cachetools==5.5.2
      - catboost==1.2.7
      - category-encoders==2.6.4
      - certifi==2024.12.14
      - cffi==1.17.1
      - charset-normalizer==3.4.0
      - chumpy==0.70
      - click==8.1.7
      - cloudpickle==3.1.0
      - cma==3.2.2
      - colorlog==6.9.0
      - colour==0.1.5
      - contourpy==1.3.0
      - cryptography==45.0.5
      - cycler==0.12.1
      - cython==3.0.12
      - decorator==5.1.1
      - decord==0.6.0
      - deprecated==1.2.15
      - dill==0.3.9
      - dnspython==2.7.0
      - dtreeviz==2.2.2
      - ecdsa==0.19.1
      - email-validator==2.0.0.post2
      - et-xmlfile==2.0.0
      - exceptiongroup==1.2.2
      - executing==2.1.0
      - fastapi==0.115.6
      - filelock==3.16.1
      - filterpy==1.4.5
      - flask==3.1.0
      - flatbuffers==24.3.25
      - fonttools==4.55.3
      - fsspec==2024.10.0
      - ftfy==6.3.1
      - gdown==5.2.0
      - google-auth==2.40.3
      - grapheme==0.6.0
      - greenlet==3.1.1
      - h11==0.16.0
      - httpcore==1.0.9
      - httpx==0.28.1
      - idna==3.10
      - importlib-metadata==8.5.0
      - importlib-resources==6.4.5
      - iniconfig==2.0.0
      - itsdangerous==2.2.0
      - jax==0.4.30
      - jaxlib==0.4.30
      - jinja2==3.1.4
      - jmespath==1.0.1
      - joblib==1.4.2
      - jsonschema==4.23.0
      - jsonschema-specifications==2024.10.1
      - jstyleson==0.0.2
      - kiwisolver==1.4.7
      - lapx==0.5.11.post1
      - lightgbm==4.5.0
      - llvmlite==0.43.0
      - loguru==0.7.3
      - mako==1.3.8
      - markdown==3.7
      - markdown-it-py==3.0.0
      - markupsafe==3.0.2
      - matplotlib==3.9.4
      - mdurl==0.1.2
      - mediapipe==0.10.14
      - ml-dtypes==0.5.0
      - mljar-scikit-plot==0.3.12
      - mljar-supervised==1.1.9
      - mpmath==1.3.0
      - natsort==8.4.0
      - networkx==3.2.1
      - ninja==********
      - nncf==2.14.0
      - numba==0.60.0
      - numpy==1.23.5
      - nvidia-cublas-cu12==********
      - nvidia-cuda-cupti-cu12==12.1.105
      - nvidia-cuda-nvrtc-cu12==12.1.105
      - nvidia-cuda-runtime-cu12==12.1.105
      - nvidia-cudnn-cu12==********
      - nvidia-cufft-cu12==*********
      - nvidia-curand-cu12==**********
      - nvidia-cusolver-cu12==**********
      - nvidia-cusparse-cu12==**********
      - nvidia-nccl-cu12==2.20.5
      - nvidia-nvjitlink-cu12==12.6.85
      - nvidia-nvtx-cu12==12.1.105
      - opencv-contrib-python==*********
      - opencv-python==*********
      - openpyxl==3.1.5
      - openvino==2024.0.0
      - openvino-telemetry==2024.5.0
      - opt-einsum==3.4.0
      - optuna==4.1.0
      - packaging==24.2
      - pandas==2.2.3
      - passlib==1.7.4
      - patsy==1.0.1
      - pillow==11.0.0
      - plotly==5.24.1
      - pluggy==1.5.0
      - prompt-toolkit==3.0.48
      - protobuf==4.25.5
      - psutil==6.1.0
      - psycopg2-binary==2.9.10
      - py-cpuinfo==9.0.0
      - pyasn1==0.6.1
      - pyasn1-modules==0.4.2
      - pycparser==2.22
      - pydantic==2.10.3
      - pydantic-core==2.27.1
      - pydot==2.0.0
      - pygments==2.18.0
      - pyjwt==2.10.1
      - pymoo==*******
      - pyparsing==3.2.0
      - pysocks==1.7.1
      - pytest==8.3.4
      - python-dotenv==1.0.1
      - python-jose==3.3.0
      - python-multipart==0.0.20
      - pytz==2024.2
      - pyyaml==6.0.2
      - referencing==0.35.1
      - regex==2024.11.6
      - requests==2.32.3
      - rich==13.9.4
      - rpds-py==0.22.3
      - rsa==4.9.1
      - s3transfer==0.12.0
      - scikit-learn==1.4.1.post1
      - scipy==1.11.4
      - seaborn==0.13.2
      - setuptools==80.0.0
      - shap==0.46.0
      - slicer==0.0.8
      - sniffio==1.3.1
      - sounddevice==0.5.1
      - soupsieve==2.6
      - sqlalchemy==2.0.36
      - starlette==0.41.3
      - statsmodels==0.14.4
      - sympy==1.13.3
      - tabulate==0.9.0
      - tenacity==9.0.0
      - threadpoolctl==3.5.0
      - tomli==2.2.1
      - torch==2.3.1
      - torchvision==0.18.1
      - tqdm==4.67.1
      - triton==2.3.1
      - tzdata==2024.2
      - ultralytics==8.3.51
      - ultralytics-thop==2.0.13
      - urllib3==1.26.20
      - uvicorn==0.34.0
      - werkzeug==3.1.3
      - wordcloud==1.9.4
      - wrapt==1.17.0
      - xgboost==2.1.3
      - yacs==0.1.8

