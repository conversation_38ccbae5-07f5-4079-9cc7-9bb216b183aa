{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import os\n", "import pandas as pd\n", "from collections import defaultdict\n", "from IPython.core.debugger import set_trace\n", "import cv2\n", "import matplotlib.pyplot as plt\n", "import matplotlib.patches as patches\n", "from IPython.display import clear_output\n", "from tqdm import tqdm\n", "from itertools import combinations\n", "plt.ion()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["file_path = '../darknet/results/person_bbox_merged.txt'"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = defaultdict(list)\n", "for line in open(file_path):\n", "    vid, idx, person_pos = line.strip().split('\\t')\n", "    for col,val in zip(['vid','idx', 'person_pos'], [vid, idx, person_pos]):\n", "        data[col].append(val)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = pd.DataFrame(data=data)\n", "df.tail()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_corner_points(coord, H=480, W=640):\n", "    x,y,w,h = coord\n", "    return ( max(0,x-w/2), max(0,y-h/2),   # (xmin,ymin)\n", "             min(W, x+w/2), min(H,y+h/2) )  # (xmax,ymax)\n", "    \n", "def bb_intersection_over_union(boxA, boxB):\n", "    # determine the (x, y)-coordinates of the intersection rectangle\n", "    xA = max(boxA[0], boxB[0])\n", "    yA = max(boxA[1], boxB[1])\n", "    xB = min(boxA[2], boxB[2])\n", "    yB = min(boxA[3], boxB[3])\n", "\n", "    # compute the area of intersection rectangle\n", "    interArea = max(0, xB - xA + 1) * max(0, yB - yA + 1)\n", "\n", "    # compute the area of both the prediction and ground-truth\n", "    # rectangles\n", "    boxAArea = (boxA[2] - boxA[0] + 1) * (boxA[3] - boxA[1] + 1)\n", "    boxBArea = (boxB[2] - boxB[0] + 1) * (boxB[3] - boxB[1] + 1)\n", "\n", "    # compute the intersection over union by taking the intersection\n", "    # area and dividing it by the sum of prediction + ground-truth\n", "    # areas - the interesection area\n", "    iou = interArea / float(boxAArea + boxBArea - interArea)\n", "\n", "    # return the intersection over union value\n", "    return iou"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class Box:\n", "    def __init__(self, pos, lab):\n", "        self.pos = pos\n", "        self.lab = lab\n", "        \n", "    def set_label(self, lab):\n", "        self.lab = lab\n", "        \n", "def init_box(pos, lab):\n", "    return [ Box(pos=get_corner_points(eval(e)), lab=v) for e,v in zip(pos.split('@'),lab) ]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": true}, "outputs": [], "source": ["def stack_tracking_log(df, vid, tracking_log):\n", "    \n", "    # select part of df based on vid\n", "    df = df[df.vid==vid]\n", "\n", "    try:\n", "        # initial box positions & frame idx\n", "        initial_pos, initial_idx = df.iloc[0].person_pos, eval(df.iloc[0].idx)\n", "        initial_pos = init_box(pos=initial_pos, lab=range(len(initial_pos)))\n", "    except:\n", "        set_trace()\n", "\n", "    lab2history = {k:[(v,initial_idx)] for k,v in zip(range(len(initial_pos)), initial_pos)}\n", "    tracking_log[vid] = lab2history\n", "\n", "    for row_idx in range(1,len(df)):\n", "        cur_row = df.iloc[row_idx]\n", "        pos, idx = cur_row.person_pos, eval(cur_row.idx)\n", "\n", "        cur_pos = init_box(pos, lab=[None]*len(pos))\n", "        for cur_box in cur_pos:\n", "            log = []\n", "            for prev_box in list(lab2history.values()):\n", "                last_prev_box, _ = prev_box[-1]           \n", "                iou = bb_intersection_over_union(last_prev_box.pos, cur_box.pos)\n", "                log.append((last_prev_box,cur_box,iou))\n", "\n", "            sorted_log = sorted(log, key=lambda x: -x[2])\n", "\n", "            # nearest prev box\n", "            nearest_prev_box, target_cur_box, iou_max = sorted_log[0]\n", "\n", "            if iou_max > 0.1:\n", "                # assign prev_box label \n", "                cur_pos[cur_pos.index(target_cur_box)].set_label(nearest_prev_box.lab)\n", "                tracking_log[vid][nearest_prev_box.lab].append((target_cur_box, idx))\n", "            else:\n", "                # add new label\n", "                new_lab = max(lab2history) + 1\n", "                cur_pos[cur_pos.index(target_cur_box)].set_label(new_lab)\n", "                tracking_log[vid][new_lab] = [(target_cur_box, idx)]    \n", "    \n", "    # pop label histories that have length less than half of entire sequence\n", "    for lab in list(lab2history.keys()):\n", "        if len(tracking_log[vid][lab]) < len(df)*0.5:\n", "            tracking_log[vid].pop(lab)\n", "\n", "def get_XY_traces(vid, tracking_log, plot=False):\n", "    if plot:\n", "        fig, ax = plt.subplots(nrows=2, ncols=1, figsize=(11,11))\n", "        \n", "    lab_center_dist = defaultdict(list)\n", "\n", "    for lab in tracking_log[vid].keys():\n", "        for box, c in tracking_log[vid][lab]:\n", "            xmin,ymin,xmax,ymax = box.pos\n", "            xc, yc = xmin + (xmax-xmin)/2, ymin + (ymax-ymin)/2\n", "            lab_center_dist[lab].append((xc,yc))\n", "        dist_arr = np.array(lab_center_dist[lab])\n", "        \n", "        if plot:\n", "            ax[0].plot(dist_arr[:,0], label=str(lab)+'-'+'xpos')\n", "            ax[0].legend()\n", "            ax[1].plot(dist_arr[:,1], label=str(lab)+'-'+'ypos')\n", "            ax[1].legend()\n", "        \n", "    return lab_center_dist\n", "\n", "def select_target_trace(lab_center_dist, epsilon = 1e-3):\n", "    # sorted based on length of y-trace/x-trace (descending order)\n", "    sorted_dist = sorted(lab_center_dist.items(), key=lambda x: -abs(x[1][0][1]-x[1][-1][1])/(abs(x[1][0][0]-x[1][-1][0]+epsilon)))\n", "\n", "    # select one with logest y-trace and shorted x-trace\n", "    target_trace = sorted_dist[0]\n", "    return target_trace\n", "\n", "        \n", "def analize_XY_traces(vid, tracking_log):\n", "    lab_center_dist = defaultdict(list)\n", "    \n", "    tmp = defaultdict(list)\n", "    for lab in tracking_log[vid].keys():\n", "        for box, c in tracking_log[vid][lab]:\n", "            xmin,ymin,xmax,ymax = box.pos\n", "            xc, yc = xmin+(xmax-xmin)/2, ymin+(ymax-ymin)/2\n", "            lab_center_dist[lab].append((xc,yc))\n", "        dist_arr = np.array(lab_center_dist[lab])\n", "        x_traces, y_traces = dist_arr[:,0], dist_arr[:,1]\n", "        tmp[lab].append(( abs(x_traces[0]-x_traces[-1]), abs(y_traces[0]-y_traces[-1])) )\n", "\n", "    \n", "    sorted_tmp = sorted(tmp.items(), key=lambda x: x[0])\n", "    sorted_tmp = {k:v for k,v in sorted_tmp}\n", "    xvals, yvals = np.array(list(sorted_tmp.values()))[:,:,:2].transpose(2,0,1)\n", "    fig, ax = plt.subplots(1, figsize=(11,9))\n", "    \n", "    ind = np.arange(len(xvals.flatten()))\n", "    width = 0.35\n", "    \n", "    rects1 = ax.bar(ind - width/2, xvals.flatten(), width, label='X-axis')\n", "    rects2 = ax.bar(ind + width/2, yvals.flatten(), width, label='Y-axis')        \n", "    \n", "    # Add some text for labels, title and custom x-axis tick labels, etc.\n", "    ax.set_ylabel('Length of traces')\n", "    ax.set_title('Length of traces for each label')\n", "    ax.set_xticks(ind)\n", "    ax.set_xticklabels([ f'Label-{x}' for x in tracking_log[vid].keys() ])\n", "    \n", "    ax.legend()\n", "    \n", "    for ix, (xval, yval) in enumerate(zip(xvals.flatten(), yvals.flatten())):\n", "        height = max(xval, yval)\n", "        ratio = yval/xval\n", "        ax.annotate('y/x={:.3f}'.format(ratio),\n", "                    xy=(ind[ix], height),\n", "                    xytext=(0*3, 3),  # use 3 points offset\n", "                    textcoords=\"offset points\",  # in both directions\n", "                    ha='center', va='bottom')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "        \n", "def run_tracker(vid, tracking_log, analize=False, plot_dist=False):\n", "    stack_tracking_log(df, vid, tracking_log)\n", "    \n", "    if analize:\n", "        analize_XY_traces(vid, tracking_log)\n", "    \n", "    # get lab_center dist\n", "    lab_center_dist = get_XY_traces(vid, tracking_log, plot=plot_dist)\n", "\n", "    # select target trace (walker)\n", "    target_trace = select_target_trace(lab_center_dist)\n", "    \n", "    return target_trace\n", "\n", "\n", "def crop_and_save_frame_video(video_home, vid, frame_home, anno_path, start_end, analize, plot_dist):\n", "    \n", "    if not os.path.exists(frame_home):\n", "        os.system(f'mkdir -p {frame_home}')\n", "        \n", "    start_ix, end_ix = start_end\n", "    \n", "    tracking_log = {}\n", "\n", "    target_trace = run_tracker(vid, tracking_log, analize=analize, plot_dist=plot_dist)\n", "    \n", "    # parse target_trace\n", "    lab, _ = target_trace\n", "\n", "    hist = tracking_log[vid][lab]\n", "    ix2box = {ix:box for (box,ix) in hist}\n", "    \n", "    video_path = os.path.join(video_home, vid + '.avi')\n", "    frame_save_path = os.path.join(frame_home, vid)\n", "    \n", "    cap = cv2.VideoCapture(video_path)\n", "    ret, frame = cap.read()\n", "\n", "    width = frame.shape[1]\n", "    height = frame.shape[0]\n", "    \n", "    c = 0\n", "    \n", "    stacked_arr = []\n", "    while True:\n", "        ret, frame = cap.read()\n", "        if not ret:\n", "            break\n", "\n", "        if c in ix2box and c in range(start_ix, end_ix):\n", "            xmin,ymin,xmax,ymax = ix2box[c].pos\n", "            xmin,ymin,xmax,ymax = [ int(e) for e in [xmin,ymin,xmax,ymax] ]\n", "            \n", "            crop = frame[ymin:ymax, xmin:xmax]\n", "            stacked_arr.append(crop)\n", "            \n", "            line = vid + '\\t' + str(c) + '\\t' + ','.join(str(x) for x in ix2box[c].pos)\n", "            os.system(\"echo \\'{}\\' >> {}\".format(line, anno_path))\n", "\n", "        c += 1\n", "        \n", "    stacked_arr = np.array(stacked_arr)\n", "    np.save(frame_save_path, stacked_arr)\n", "        \n", "\n", "def crop_and_save_from_bunch_of_video(video_home, frame_home, anno_path, target_df_path, analize=False, plot_dist=False, fps=24.0):\n", "    \n", "    # if annotation file exists already, delte it!\n", "    if os.path.exists(anno_path):\n", "        os.system(f'rm {anno_path}')\n", "\n", "    \n", "    list_of_vid = list(os.path.splitext(x)[0] for x in os.listdir(video_home) if not x.startswith('vid'))\n", "    \n", "    target_df = pd.read_pickle(target_df_path)\n", "    \n", "    for vid in tqdm(list_of_vid):\n", "        # assemble new vid \n", "        pid,_,test_id,_,trial_id = vid.split('_')\n", "        vid_as_index = '_'.join([pid,test_id,trial_id]) # convert vid as form of index of df\n", "        if vid_as_index not in target_df.index:\n", "            continue\n", "            \n", "        start, end = target_df.loc[vid_as_index,'start':'end'].values\n", "        \n", "        start_ix, end_ix = [ int(fps*t) for t in [start, end] ]\n", "\n", "        crop_and_save_frame_video(video_home, vid, frame_home, anno_path, (start_ix,end_ix), analize=analize, plot_dist=plot_dist)\n", "        \n", "\n", "\n", "# main function\n", "crop_and_save_from_bunch_of_video(video_home = '/data/GaitData/Video',\n", "                                  frame_home = '/data/GaitData/CroppedFrameArrays',\n", "                                  anno_path='../data/person_detection_and_tracking_results.csv',\n", "                                  target_df_path='../data/targets_dataframe.pkl',\n", "                                  analize=False,\n", "                                  plot_dist=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Visualize target person"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from bokeh.plotting import figure\n", "from bokeh.io import output_notebook, show, push_notebook\n", "import cv2, os\n", "import time\n", "import random\n", "\n", "output_notebook()\n", "\n", "vid = '1810488_test_0_trial_3'\n", "\n", "tracking_log = {}\n", "target_trace = run_tracker(vid, tracking_log, plot_dist=False)\n", "\n", "lab, _ = target_trace\n", "\n", "hist = tracking_log[vid][lab]\n", "ix2box = {ix:box for (box,ix) in hist}\n", "\n", "cap = cv2.VideoCapture(os.path.join('/data/GaitData/Video', vid + '.avi'))\n", "ret, frame = cap.read()\n", "frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGBA) # because Bokeh expects a RGBA image\n", "frame = cv2.flip(frame, 0)\n", "\n", "width = frame.shape[1]\n", "height = frame.shape[0]\n", "p = figure(x_range=(0,width), y_range=(0,height), output_backend=\"webgl\", width=width, height=height)\n", "myImage = p.image_rgba(image=[frame], x=0, y=0, dw=width, dh=height)\n", "show(p, notebook_handle=True)\n", "\n", "c = 0\n", "\n", "while True:\n", "    ret, frame = cap.read()\n", "    if not ret:\n", "        break\n", "        \n", "    frame=cv2.cvtColor(frame, cv2.COLOR_BGR2RGBA)\n", "    frame=cv2.flip(frame, 0) # because <PERSON><PERSON><PERSON> flips vertically\n", "    \n", "    if c in ix2box:\n", "        xmin,ymin,xmax,ymax = ix2box[c].pos\n", "        xmin,ymin,xmax,ymax = [ int(e) for e in [xmin,ymin,xmax,ymax] ]\n", "\n", "        frame = cv2.rectangle(frame, (xmin, 480-ymin), (xmax, 480-ymax), [0,255,0,255], 2)\n", "            \n", "    myImage.data_source.data['image']=[frame]\n", "    push_notebook()\n", "    time.sleep(5e-3)\n", "    \n", "    c += 1\n", "    "]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Visualize entire tracking results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from bokeh.plotting import figure\n", "from bokeh.io import output_notebook, show, push_notebook\n", "import cv2, os\n", "import time\n", "import random\n", "\n", "output_notebook()\n", "\n", "vid = '1810488_test_0_trial_3'\n", "\n", "tracking_log = {}\n", "target_trace = run_tracker(vid, tracking_log, plot_dist=False)\n", "\n", "ix2box_list = []\n", "\n", "for lab in tracking_log[vid].keys():\n", "    hist = tracking_log[vid][lab]\n", "    ix2box = {ix:box for (box,ix) in hist}\n", "    ix2box_list.append(ix2box)\n", "\n", "cap = cv2.VideoCapture(os.path.join('/data/GaitData/Video', vid + '.avi'))\n", "ret, frame = cap.read()\n", "frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGBA) # because Bokeh expects a RGBA image\n", "frame = cv2.flip(frame, 0)\n", "\n", "width = frame.shape[1]\n", "height = frame.shape[0]\n", "p = figure(x_range=(0,width), y_range=(0,height), output_backend=\"webgl\", width=width, height=height)\n", "myImage = p.image_rgba(image=[frame], x=0, y=0, dw=width, dh=height)\n", "show(p, notebook_handle=True)\n", "\n", "c = 0\n", "\n", "colors = []\n", "for _ in tracking_log[vid].keys():\n", "    r_color = [ random.randint(0,255) for _ in range(3) ] + [255]\n", "    colors.append(r_color)\n", "\n", "while True:\n", "    ret, frame = cap.read()\n", "    if not ret:\n", "        break\n", "        \n", "    frame=cv2.cvtColor(frame, cv2.COLOR_BGR2RGBA)\n", "    frame=cv2.flip(frame, 0) # because <PERSON><PERSON><PERSON> flips vertically\n", "    \n", "    for pi, (ix2box, r_color) in enumerate(zip(ix2box_list, colors)):\n", "        if c in ix2box:\n", "            xmin,ymin,xmax,ymax = ix2box[c].pos\n", "            xmin,ymin,xmax,ymax = [ int(e) for e in [xmin,ymin,xmax,ymax] ]\n", "\n", "            frame = cv2.rectangle(frame, (xmin, 480-ymin), (xmax, 480-ymax), r_color, 2)\n", "            frame = cv2.putText(frame, f'{pi}', (xmin,480-ymax), cv2.FONT_HERSHEY_SIMPLEX, 1,r_color,2,cv2.LINE_AA, bottomLeftOrigin=True)\n", "    myImage.data_source.data['image']=[frame]\n", "    push_notebook()\n", "    time.sleep(5e-2)\n", "    \n", "    c += 1\n", "    "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.8"}}, "nbformat": 4, "nbformat_minor": 2}