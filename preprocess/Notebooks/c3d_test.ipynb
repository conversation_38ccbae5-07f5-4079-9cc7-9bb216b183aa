{"cells": [{"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting tensorflow-gpu==1.3.0\n", "\u001b[?25l  Downloading https://files.pythonhosted.org/packages/b8/16/14fbe414f2b3c39c5d01521e38e7aaec384f8fbf26eec021d247345260c3/tensorflow_gpu-1.3.0-cp36-cp36m-manylinux1_x86_64.whl (159.1MB)\n", "\u001b[K    100% |████████████████████████████████| 159.1MB 261kB/s eta 0:00:01\n", "\u001b[?25hRequirement already satisfied: protobuf>=3.3.0 in /home/<USER>/anaconda3/envs/torch/lib/python3.6/site-packages (from tensorflow-gpu==1.3.0) (3.8.0)\n", "Requirement already satisfied: numpy>=1.11.0 in /home/<USER>/anaconda3/envs/torch/lib/python3.6/site-packages (from tensorflow-gpu==1.3.0) (1.14.3)\n", "Requirement already satisfied: wheel>=0.26 in /home/<USER>/anaconda3/envs/torch/lib/python3.6/site-packages (from tensorflow-gpu==1.3.0) (0.33.1)\n", "Requirement already satisfied: six>=1.10.0 in /home/<USER>/anaconda3/envs/torch/lib/python3.6/site-packages (from tensorflow-gpu==1.3.0) (1.12.0)\n", "Collecting tensorflow-tensorboard<0.2.0,>=0.1.0 (from tensorflow-gpu==1.3.0)\n", "\u001b[?25l  Downloading https://files.pythonhosted.org/packages/93/31/bb4111c3141d22bd7b2b553a26aa0c1863c86cb723919e5bd7847b3de4fc/tensorflow_tensorboard-0.1.8-py3-none-any.whl (1.6MB)\n", "\u001b[K    100% |████████████████████████████████| 1.6MB 17.4MB/s ta 0:00:01\n", "\u001b[?25hRequirement already satisfied: setuptools in /home/<USER>/anaconda3/envs/torch/lib/python3.6/site-packages (from protobuf>=3.3.0->tensorflow-gpu==1.3.0) (41.0.0)\n", "Requirement already satisfied: bleach==1.5.0 in /home/<USER>/anaconda3/envs/torch/lib/python3.6/site-packages (from tensorflow-tensorboard<0.2.0,>=0.1.0->tensorflow-gpu==1.3.0) (1.5.0)\n", "Requirement already satisfied: html5lib==0.9999999 in /home/<USER>/anaconda3/envs/torch/lib/python3.6/site-packages (from tensorflow-tensorboard<0.2.0,>=0.1.0->tensorflow-gpu==1.3.0) (0.9999999)\n", "Requirement already satisfied: werkzeug>=0.11.10 in /home/<USER>/anaconda3/envs/torch/lib/python3.6/site-packages (from tensorflow-tensorboard<0.2.0,>=0.1.0->tensorflow-gpu==1.3.0) (0.15.4)\n", "Requirement already satisfied: markdown>=2.6.8 in /home/<USER>/anaconda3/envs/torch/lib/python3.6/site-packages (from tensorflow-tensorboard<0.2.0,>=0.1.0->tensorflow-gpu==1.3.0) (3.1.1)\n", "Installing collected packages: tensorflow-tensorboard, tensorflow-gpu\n", "  Found existing installation: tensorflow-tensorboard 0.4.0\n", "    Uninstalling tensorflow-tensorboard-0.4.0:\n", "      Successfully uninstalled tensorflow-tensorboard-0.4.0\n", "  Found existing installation: tensorflow-gpu 1.4.0\n", "    Uninstalling tensorflow-gpu-1.4.0:\n", "      Successfully uninstalled tensorflow-gpu-1.4.0\n", "Successfully installed tensorflow-gpu-1.3.0 tensorflow-tensorboard-0.1.8\n"]}], "source": ["!pip install tensorflow-gpu==1.3.0"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"ename": "ImportError", "evalue": "Traceback (most recent call last):\n  File \"/home/<USER>/anaconda3/envs/torch/lib/python3.6/site-packages/tensorflow/python/pywrap_tensorflow.py\", line 41, in <module>\n    from tensorflow.python.pywrap_tensorflow_internal import *\n  File \"/home/<USER>/anaconda3/envs/torch/lib/python3.6/site-packages/tensorflow/python/pywrap_tensorflow_internal.py\", line 28, in <module>\n    _pywrap_tensorflow_internal = swig_import_helper()\n  File \"/home/<USER>/anaconda3/envs/torch/lib/python3.6/site-packages/tensorflow/python/pywrap_tensorflow_internal.py\", line 24, in swig_import_helper\n    _mod = imp.load_module('_pywrap_tensorflow_internal', fp, pathname, description)\n  File \"/home/<USER>/anaconda3/envs/torch/lib/python3.6/imp.py\", line 243, in load_module\n    return load_dynamic(name, filename, file)\n  File \"/home/<USER>/anaconda3/envs/torch/lib/python3.6/imp.py\", line 343, in load_dynamic\n    return _load(spec)\nImportError: libcusolver.so.8.0: cannot open shared object file: No such file or directory\n\n\nFailed to load the native TensorFlow runtime.\n\nSee https://www.tensorflow.org/install/install_sources#common_installation_problems\n\nfor some common reasons and solutions.  Include the entire stack trace\nabove this error message when asking for help.", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mImportError\u001b[0m                               Traceback (most recent call last)", "\u001b[0;32m~/anaconda3/envs/torch/lib/python3.6/site-packages/tensorflow/python/pywrap_tensorflow.py\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[1;32m     40\u001b[0m     \u001b[0msys\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0msetdlopenflags\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0m_default_dlopen_flags\u001b[0m \u001b[0;34m|\u001b[0m \u001b[0mctypes\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mRTLD_GLOBAL\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m---> 41\u001b[0;31m   \u001b[0;32mfrom\u001b[0m \u001b[0mtensorflow\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mpython\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mpywrap_tensorflow_internal\u001b[0m \u001b[0;32mimport\u001b[0m \u001b[0;34m*\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m     42\u001b[0m   \u001b[0;32mfrom\u001b[0m \u001b[0mtensorflow\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mpython\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mpywrap_tensorflow_internal\u001b[0m \u001b[0;32mimport\u001b[0m \u001b[0m__version__\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/anaconda3/envs/torch/lib/python3.6/site-packages/tensorflow/python/pywrap_tensorflow_internal.py\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[1;32m     27\u001b[0m             \u001b[0;32mreturn\u001b[0m \u001b[0m_mod\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m---> 28\u001b[0;31m     \u001b[0m_pywrap_tensorflow_internal\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mswig_import_helper\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m     29\u001b[0m     \u001b[0;32mdel\u001b[0m \u001b[0mswig_import_helper\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/anaconda3/envs/torch/lib/python3.6/site-packages/tensorflow/python/pywrap_tensorflow_internal.py\u001b[0m in \u001b[0;36mswig_import_helper\u001b[0;34m()\u001b[0m\n\u001b[1;32m     23\u001b[0m             \u001b[0;32mtry\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m---> 24\u001b[0;31m                 \u001b[0m_mod\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mimp\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mload_module\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m'_pywrap_tensorflow_internal'\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mfp\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mpathname\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mdescription\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m     25\u001b[0m             \u001b[0;32mfinally\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/anaconda3/envs/torch/lib/python3.6/imp.py\u001b[0m in \u001b[0;36mload_module\u001b[0;34m(name, file, filename, details)\u001b[0m\n\u001b[1;32m    242\u001b[0m         \u001b[0;32melse\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 243\u001b[0;31m             \u001b[0;32mreturn\u001b[0m \u001b[0mload_dynamic\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mname\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mfilename\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mfile\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    244\u001b[0m     \u001b[0;32melif\u001b[0m \u001b[0mtype_\u001b[0m \u001b[0;34m==\u001b[0m \u001b[0mPKG_DIRECTORY\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/anaconda3/envs/torch/lib/python3.6/imp.py\u001b[0m in \u001b[0;36mload_dynamic\u001b[0;34m(name, path, file)\u001b[0m\n\u001b[1;32m    342\u001b[0m             name=name, loader=loader, origin=path)\n\u001b[0;32m--> 343\u001b[0;31m         \u001b[0;32mreturn\u001b[0m \u001b[0m_load\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mspec\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    344\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mImportError\u001b[0m: libcusolver.so.8.0: cannot open shared object file: No such file or directory", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[0;31mImportError\u001b[0m                               Traceback (most recent call last)", "\u001b[0;32m<ipython-input-5-6010d429a23a>\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[0;32m----> 1\u001b[0;31m \u001b[0;32mimport\u001b[0m \u001b[0mtensorflow\u001b[0m \u001b[0;32mas\u001b[0m \u001b[0mtf\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m      2\u001b[0m \u001b[0;32mimport\u001b[0m \u001b[0mc3d_wrapper\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      3\u001b[0m \u001b[0;32mimport\u001b[0m \u001b[0mos\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      4\u001b[0m \u001b[0;32mfrom\u001b[0m \u001b[0mIPython\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mcore\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mdebugger\u001b[0m \u001b[0;32mimport\u001b[0m \u001b[0mset_trace\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      5\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/anaconda3/envs/torch/lib/python3.6/site-packages/tensorflow/__init__.py\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[1;32m     22\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     23\u001b[0m \u001b[0;31m# pylint: disable=wildcard-import\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m---> 24\u001b[0;31m \u001b[0;32mfrom\u001b[0m \u001b[0mtensorflow\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mpython\u001b[0m \u001b[0;32mimport\u001b[0m \u001b[0;34m*\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m     25\u001b[0m \u001b[0;31m# pylint: enable=wildcard-import\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     26\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/anaconda3/envs/torch/lib/python3.6/site-packages/tensorflow/python/__init__.py\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[1;32m     47\u001b[0m \u001b[0;32mimport\u001b[0m \u001b[0mnumpy\u001b[0m \u001b[0;32mas\u001b[0m \u001b[0mnp\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     48\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m---> 49\u001b[0;31m \u001b[0;32mfrom\u001b[0m \u001b[0mtensorflow\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mpython\u001b[0m \u001b[0;32mimport\u001b[0m \u001b[0mpywrap_tensorflow\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m     50\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     51\u001b[0m \u001b[0;31m# Protocol buffers\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/anaconda3/envs/torch/lib/python3.6/site-packages/tensorflow/python/pywrap_tensorflow.py\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[1;32m     50\u001b[0m \u001b[0;32mfor\u001b[0m \u001b[0msome\u001b[0m \u001b[0mcommon\u001b[0m \u001b[0mreasons\u001b[0m \u001b[0;32mand\u001b[0m \u001b[0msolutions\u001b[0m\u001b[0;34m.\u001b[0m  \u001b[0mInclude\u001b[0m \u001b[0mthe\u001b[0m \u001b[0mentire\u001b[0m \u001b[0mstack\u001b[0m \u001b[0mtrace\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     51\u001b[0m above this error message when asking for help.\"\"\" % traceback.format_exc()\n\u001b[0;32m---> 52\u001b[0;31m   \u001b[0;32mraise\u001b[0m \u001b[0mImportError\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mmsg\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m     53\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     54\u001b[0m \u001b[0;31m# pylint: enable=wildcard-import,g-import-not-at-top,unused-import,line-too-long\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mImportError\u001b[0m: Traceback (most recent call last):\n  File \"/home/<USER>/anaconda3/envs/torch/lib/python3.6/site-packages/tensorflow/python/pywrap_tensorflow.py\", line 41, in <module>\n    from tensorflow.python.pywrap_tensorflow_internal import *\n  File \"/home/<USER>/anaconda3/envs/torch/lib/python3.6/site-packages/tensorflow/python/pywrap_tensorflow_internal.py\", line 28, in <module>\n    _pywrap_tensorflow_internal = swig_import_helper()\n  File \"/home/<USER>/anaconda3/envs/torch/lib/python3.6/site-packages/tensorflow/python/pywrap_tensorflow_internal.py\", line 24, in swig_import_helper\n    _mod = imp.load_module('_pywrap_tensorflow_internal', fp, pathname, description)\n  File \"/home/<USER>/anaconda3/envs/torch/lib/python3.6/imp.py\", line 243, in load_module\n    return load_dynamic(name, filename, file)\n  File \"/home/<USER>/anaconda3/envs/torch/lib/python3.6/imp.py\", line 343, in load_dynamic\n    return _load(spec)\nImportError: libcusolver.so.8.0: cannot open shared object file: No such file or directory\n\n\nFailed to load the native TensorFlow runtime.\n\nSee https://www.tensorflow.org/install/install_sources#common_installation_problems\n\nfor some common reasons and solutions.  Include the entire stack trace\nabove this error message when asking for help."]}], "source": ["import tensorflow as tf\n", "import c3d_wrapper\n", "import os\n", "from IPython.core.debugger import set_trace\n", "\n", "os.environ[\"CUDA_VISIBLE_DEVICES\"]=\"3\"\n", "MODEL_PATH = '/data/GaitData/pretrained/C3D/conv3d_deepnetA_sport1m_iter_1900000_TF.model'\n", "BATCH_SIZE = 1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# define graph\n", "net = c3d_wrapper.C3DNet(\n", "    pretrained_model_path=MODEL_PATH, trainable=False,\n", "    batch_size=BATCH_SIZE)\n", "\n", "tf_video_clip = tf.placeholder(tf.float32,\n", "                               [BATCH_SIZE, None, 112, 112, 3],\n", "                               name='tf_video_clip')  # (batch,num_frames,112,112,3)\n", "tf_output = net(inputs=tf_video_clip)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# create session\n", "sess = tf.Session()\n", "sess.run(tf.global_variables_initializer())"]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": true}, "outputs": [], "source": ["with open('labels.txt', 'r') as f:\n", "    labels = [line.strip() for line in f.readlines()]\n", "print('Total labels: {}'.format(len(labels)))\n", "\n", "import cv2\n", "import numpy as np\n", "\n", "mean_val = np.load('train01_16_128_171_mean.npy').transpose(1,2,3,0)\n", "\n", "stacked_arr = np.load('/data/GaitData/CroppedFrameArrays/am022_test_2_trial_1.npy')\n", "\n", "\n", "vid = []\n", "for img in stacked_arr:\n", "    vid.append(cv2.resize(img, (171,128)))\n", "\n", "vid = np.array(vid)\n", "\n", "\n", "X = vid[0:16]-mean_val\n", "X = X[:, 8:120, 30:142, :]\n", "\n", "set_trace()\n", "\n", "output = sess.run(tf_output, feed_dict={tf_video_clip:[X]})\n", "\n", "set_trace()\n", "\n", "#plt.plot(output[0]); plt.show()\n", "\n", "print('Position of maximum probability: {}'.format(output[0].argmax()))\n", "print('Maximum probability: {:.5f}'.format(max(output[0])))\n", "print('Corresponding label: {}'.format(labels[output[0].argmax()]))\n", "\n", "# sort top five predictions from softmax output\n", "top_inds = output[0].argsort()[::-1][:5]  # reverse sort and take five largest items\n", "print('\\nTop 5 probabilities and labels:')\n", "\n", "for i in top_inds:\n", "    print('{:.5f} {}'.format(output[0][i], labels[i]))\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.8"}}, "nbformat": 4, "nbformat_minor": 2}