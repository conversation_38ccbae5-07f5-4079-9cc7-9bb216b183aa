{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Preprocess Meta data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from __future__ import unicode_literals\n", "\n", "%matplotlib inline\n", "\n", "from collections import defaultdict\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["meta_home = '/mnt/hdd/GaitData/MetaData'"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Merge basic_test.txt files"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import glob\n", "import os\n", "from natsort import natsorted\n", "\n", "basic_files = natsorted(glob.glob(os.path.join(meta_home, '*_basic_test_*.txt')))    \n", "df_to_merge = []\n", "for f in basic_files:\n", "    df = pd.read_csv(f, delimiter='\\t').dropna()  # drop nan\n", "    df_to_merge.append(df)\n", "\n", "df = pd.concat(df_to_merge)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = defaultdict(list)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["params = df[\"PARAMETER DESCRIPTION\"].values.tolist()\n", "tot_or_left = df[\"Total/Left\"].values.tolist()\n", "right = df[\"Right\"].values.tolist()\n", "\n", "# strip before further processing\n", "params = [ x.strip() for x in params ]\n", "tot_or_left = [ x.strip() for x in tot_or_left ]\n", "right = [ x.strip() for x in right ]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from IPython.core.debugger import set_trace\n", "\n", "for _param, _tot_or_left, _right in zip(params, tot_or_left, right):    \n", "\n", "    _tot_or_left = '0.0' if _tot_or_left=='.' else _tot_or_left\n", "    _right = '0.0' if _right=='.' else _right\n", "    \n", "    if _tot_or_left == '.' and _right == '.':\n", "        # this params is dropped\n", "        pass\n", "    elif _tot_or_left and not _right:\n", "        # this case, only total value exists\n", "        data[str(_param)].append(eval(_tot_or_left))\n", "    else:\n", "        # this case, left/right values eixsts\n", "        data[str(_param+'/L')].append(eval(_tot_or_left))\n", "        data[str(_param+'/R')].append(eval(_right))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["targets = ['Velocity', 'Cadence', 'Functional Amb. Profile']\n", "\n", "for item in ['Cycle Time(sec)', 'Stride Length(cm)', 'HH Base Support(cm)', 'Swing Time(sec)', 'Stance Time(sec)',\n", "             'Double Supp. Time(sec)', 'Toe In / Out']:\n", "    targets += list('{}/'.format(item) + x for x in ['L','R'])\n", "\n", "df = pd.DataFrame(data, index=natsorted([ os.path.splitext(os.path.basename(x))[0] for x in basic_files ]))\n", "target_df = df.loc[:,targets]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["target_df.to_pickle('../data/targets_dataframe.pkl')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": true}, "outputs": [], "source": ["pd.read_pickle('../data/targets_dataframe.pkl').tail()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["# Pose Output Format (BODY_25)\n", "<img src=\"https://github.com/CMU-Perceptual-Computing-Lab/openpose/raw/master/doc/media/keypoints_pose_25.png\" width=\"200\">"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["part_pairs = [(8,9),\n", "              (9,10),\n", "              (10,11),\n", "              (8,12),\n", "              (12,13),\n", "              (13,14),\n", "              (14,19),\n", "              (19,20),\n", "              (14,21),\n", "              (11,22),\n", "              (22,23),\n", "              (11,24)]\n", "\n", "part_map = {0: \"Nose\",\n", "            1: \"Neck\",\n", "            2:  \"<PERSON><PERSON><PERSON>\",\n", "            3:  \"R<PERSON>l<PERSON>\",\n", "            4:  \"<PERSON><PERSON><PERSON>\",\n", "            5:  \"LShoulder\",\n", "            6:  \"LEl<PERSON>\",\n", "            7:  \"L<PERSON><PERSON>\",\n", "            8:  \"MidHip\",\n", "            9:  \"RH<PERSON>\",\n", "            10: \"<PERSON><PERSON><PERSON>\",\n", "            11: \"<PERSON><PERSON><PERSON>\",\n", "            12: \"LHip\",\n", "            13: \"<PERSON><PERSON><PERSON>\",\n", "            14: \"<PERSON><PERSON><PERSON>\",\n", "            15: \"<PERSON><PERSON>ye\",\n", "            16: \"LEye\",\n", "            17: \"R<PERSON><PERSON>\",\n", "            18: \"L<PERSON>ar\",\n", "            19: \"<PERSON><PERSON>igT<PERSON>\",\n", "            20: \"LSmallToe\",\n", "            21: \"<PERSON><PERSON><PERSON>\",\n", "            22: \"<PERSON><PERSON><PERSON><PERSON>\",\n", "            23: \"<PERSON>mal<PERSON><PERSON><PERSON>\",\n", "            24: \"<PERSON><PERSON><PERSON>\",\n", "            25: \"Background\"}\n", "\n", "\n", "inv_part_map = { v: k for k,v in part_map.items() }"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Extract keypoints from video file"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from __future__ import division, print_function"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from openpose import pyopenpose as op\n", "import cv2\n", "import pyrealsense2 as rs\n", "import time\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "from IPython.core.debugger import set_trace\n", "from IPython.display import clear_output\n", "import os\n", "import json\n", "from itertools import chain\n", "\n", "%matplotlib inline\n", "plt.ion()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["LowerPart_idxs = sorted(list(set(chain(*part_pairs))))"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["[8, 9, 10, 11, 12, 13, 14, 19, 20, 21, 22, 23, 24]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["LowerPart_idxs"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["dir_path = '/workspace/openpose/'\n", "save_path='../data/keypoints_from_openpose.csv'\n", "\n", "if os.path.exists(save_path):\n", "    os.remove(save_path)\n", "\n", "nPoints = 25\n", "\n", "#Setting OpenPose parameters\n", "def set_params():\n", "\n", "    params = dict()\n", "    params[\"logging_level\"] = 3\n", "    params[\"output_resolution\"] = \"-1x-1\"\n", "    params[\"net_resolution\"] = \"-1x368\"\n", "    params[\"model_pose\"] = \"BODY_25\"\n", "    params[\"alpha_pose\"] = 0.6\n", "    params[\"scale_gap\"] = 0.3\n", "    params[\"scale_number\"] = 1\n", "    params[\"render_threshold\"] = 0.05\n", "    # If GPU version is built, and multiple GPUs are available, set the ID here\n", "    params[\"num_gpu_start\"] = 0\n", "    params[\"disable_blending\"] = False\n", "    # Ensure you point to the correct path where models are located\n", "    params[\"model_folder\"] = dir_path + \"models/\"\n", "    return params\n", "\n", "params = set_params()\n", "\n", "# Starting OpenPose\n", "opWrapper = op.WrapperPython()\n", "opWrapper.configure(params)\n", "opWrapper.start()\n", "\n", "# Define datum instance\n", "datum = op.Datum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_keypoints(opWrapper, datum, frame):\n", "    # update input data\n", "    datum.cvInputData = frame\n", "\n", "    # feed data\n", "    opWrapper.emplaceAndPop([datum])\n", "\n", "    # fetch keypoints, and output_image\n", "    keypoints = datum.poseKeypoints\n", "    res_frame = datum.cvOutputData\n", "    \n", "    try:\n", "        # Print the human pose keypoints, i.e., a [#people x #keypoints x 3]-dimensional numpy object with the keypoints of all the people on that image\n", "        if len(keypoints)>0:\n", "            # print('Human(s) Pose Estimated!')\n", "            res_frame = res_frame\n", "\n", "    except TypeError:\n", "        # print('No humans detected!')\n", "        keypoints = None\n", "        res_frame = frame\n", "        \n", "    # overlay over org frame\n", "    res_frame = cv2.addWeighted(frame, 0.2, res_frame, 0.8, 0.5)\n", "    \n", "    return keypoints, res_frame\n", "\n", "\n", "def extract_keypoints_from_file(opWrapper, datum, video_file,\n", "                                visualize=False,):\n", "    \n", "    \n", "    video_capture = cv2.VideoCapture(video_file)\n", "    \n", "    frame_cnt = 0\n", "    \n", "    while frame_cnt < video_capture.get(cv2.CAP_PROP_FRAME_COUNT):\n", "            \n", "        _, frame = video_capture.read()\n", "        \n", "        # parse output\n", "        keypoints, res_frame = get_keypoints(opWrapper, datum, frame)\n", "        \n", "        vid = os.path.basename(video_file)\n", "        \n", "        if keypoints is not None:\n", "            save_keypoints_as_csv_format(keypoints, vid, frame_cnt)\n", "\n", "        if visualize:\n", "            fig = plt.figure(2, figsize=(10,10))\n", "\n", "            plt.imshow(res_frame,)\n", "            plt.show()\n", "\n", "            # Display the frame until new frame is available\n", "            clear_output(wait=True)\n", "                    \n", "        frame_cnt += 1\n", "                \n", "def save_keypoints_as_csv_format(keypoints, vid, frame_cnt):\n", "    all_keypoints = []\n", "    for i in range(len(keypoints)):\n", "        p_keypoints_flat = keypoints[i][LowerPart_idxs].flatten().tolist()\n", "        all_keypoints.append(p_keypoints_flat)\n", "    \n", "    columns = [ str(x) for x in [vid, frame_cnt] + list(chain(all_keypoints)) ]\n", "\n", "    line = ','.join(columns) + '\\n'\n", "    if 'nan' in line:\n", "        set_trace()\n", "    with open(save_path, 'a+') as f:\n", "        f.write(line)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["video_files = !ls /mnt/hdd/GaitData/Video/*.avi\n", "video_files = list(x for x in video_files if not os.path.basename(x).startswith('vid'))\n", "\n", "n_batch = n_batch = eval(os.popen('nproc --all').read().strip())\n", "step = int( len(video_files) / n_batch )\n", "\n", "batches = []\n", "for i in range(n_batch):\n", "    batch = video_files[i*step:(i+1)*step]\n", "    batches.append(batch)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from time import sleep\n", "from tqdm import tqdm_notebook as tqdm\n", "from joblib import Parallel, delayed\n", "    \n", "def progresser(batch):\n", "\n", "    # parse batch tuple composed of (item, ix)\n", "    item, ix = batch\n", "    \n", "    # This line is the strange hack\n", "    print(' ')\n", "\n", "    text = \"progresser #{}\".format(ix)\n", "    for i in tqdm(item, desc=text):\n", "        extract_keypoints_from_file(op<PERSON><PERSON><PERSON>, datum, i, visualize=False)\n", "        sleep(0.001)\n", "\n", "# run \n", "Parallel(n_jobs=-1, backend=\"threading\")(delayed(progresser)(batch) for batch in zip(batches, range(n_batch)))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Parse .csv File -> Pandas dataframe"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["from collections import defaultdict\n", "\n", "def rank_of_list(x):\n", "    n_rank = 0\n", "    item = x\n", "    while True:\n", "        try:\n", "            item = item[0]\n", "        except TypeError:\n", "            return n_rank\n", "        n_rank += 1\n", "\n", "class DatasetParser:\n", "    def __init__(self, csv_file, resolution=(640,480), Xpos_tolerance=100):\n", "        self.csv_file = csv_file\n", "        self.resolution = (640, 480)\n", "        self.Xpos_tolerance = Xpos_tolerance\n", "        \n", "        self.dataset = defaultdict(lambda: [])\n", "        self.size = 0\n", "        \n", "        print('Start Dataset Parsing ...')\n", "        self.run()\n", "\n", "    def parse_line_and_register_into_dataset(self, line):\n", "        line = line.strip().split(',')\n", "        try:\n", "            vid, frame_cnt = line[:2]\n", "        except:\n", "            return\n", "        \n", "        frame_width = self.resolution[0]\n", "\n", "        try:\n", "            multi_person_pose = eval(','.join(line[2:]))\n", "        except:\n", "            return\n", "        if rank_of_list(multi_person_pose) < 2:\n", "            multi_person_pose = [multi_person_pose]\n", "            patient_idx = 0\n", "        \n", "        multi_Xpos = []\n", "        for Xpos in np.array(multi_person_pose)[:,::3]:\n", "            valid_Xpos = Xpos[Xpos>0]\n", "            if valid_Xpos.size==0:\n", "                continue\n", "            multi_Xpos.append(valid_Xpos.mean())\n", "        \n", "        if not multi_Xpos:\n", "            # if all zero Xpos, return!!\n", "            return\n", "\n", "        # who is right most person?\n", "        patient_idx = np.argmax(multi_Xpos)\n", "        patient_Xpos = multi_Xpos[patient_idx]\n", "        \n", "        if patient_Xpos < frame_width / 2 - self.Xpos_tolerance or \\\n", "            patient_Xpos > frame_width / 2 + self.Xpos_tolerance:\n", "            return\n", "        \n", "        self.dataset['vid'].append(vid)\n", "        self.dataset['frame_cnt'].append(eval(frame_cnt))\n", "        for i, part_ix in enumerate(LowerPart_idxs):\n", "            part_name = part_map[part_ix]\n", "            pos_val = multi_person_pose[patient_idx]\n", "            for j, coord in enumerate(list('XYZ')):\n", "                self.dataset['{}_{}'.format(part_name, coord)].append(pos_val[3*i+j])\n", "        \n", "        self.size += 1\n", "        \n", "    def run(self):\n", "        from tqdm import tqdm_notebook as tqdm\n", "        lines = open(self.csv_file).readlines()\n", "        for line in tqdm(lines):\n", "            self.parse_line_and_register_into_dataset(line)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Start Dataset Parsing ...\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "7df1a11a1f4d4bfc8e76230fc11c1512", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(IntProgress(value=0, max=1020526), HTML(value=u'')))"]}, "metadata": {}, "output_type": "display_data"}], "source": ["dataset_parser = DatasetParser(csv_file='../data/keypoints_from_openpose.csv')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = pd.DataFrame(data=dataset_parser.dataset,)\n", "df = df[sorted([x for x in df.columns if x], key=lambda x: inv_part_map.get(x.split('_')[0]))]\n", "df.to_pickle('../data/keypoints_dataframe.pkl')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# plot Z-position"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inv_part_map = { v: k for k,v in part_map.items() }\n", "\n", "df_to_plot = df[df.vid==\"1017175_test_0_trial_0.avi\"]\n", "Z_pos = df_to_plot[[x for x in df_to_plot.columns if x.endswith('Z')]]\n", "plt.plot(Z_pos.mean(1))"]}], "metadata": {"kernelspec": {"display_name": "Python 2", "language": "python", "name": "python2"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.12"}}, "nbformat": 4, "nbformat_minor": 2}