{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from __future__ import unicode_literals\n", "\n", "from collections import defaultdict\n", "import pandas as pd\n", "from IPython.core.debugger import set_trace\n", "import csv\n", "from tqdm import tqdm\n", "import os"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["meta_home = '/media/hossay/hdd1/GaitData/MetaData_converted'"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Basic Files containing GAIT params"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5ae063e4e6d240bb8c0555376e01bc5d", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(IntProgress(value=0, description='status', max=3760, style=ProgressStyle(description_width='ini…"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}], "source": ["import glob\n", "import os\n", "from natsort import natsorted\n", "from tqdm import tqdm_notebook as tqdm\n", "\n", "basic_files = natsorted(glob.glob(os.path.join(meta_home, '*_basic_test_*.txt')))    \n", "df_to_merge = []\n", "for f in tqdm(basic_files, desc='status'):\n", "    df = pd.read_csv(f, delimiter='\\t').dropna()  # drop nan\n", "    df_to_merge.append(df)\n", "\n", "df = pd.concat(df_to_merge)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["data = defaultdict(list)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["params = df[\"PARAMETER DESCRIPTION\"].values.tolist()\n", "tot_or_left = df[\"Total/Left\"].values.tolist()\n", "right = df[\"Right\"].values.tolist()\n", "\n", "# strip before further processing\n", "params = [ x.strip() for x in params ]\n", "tot_or_left = [ x.strip() for x in tot_or_left ]\n", "right = [ x.strip() for x in right ]"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["from IPython.core.debugger import set_trace\n", "\n", "for _param, _tot_or_left, _right in zip(params, tot_or_left, right):    \n", "\n", "    _tot_or_left = '0.0' if _tot_or_left=='.' else _tot_or_left\n", "    _right = '0.0' if _right=='.' else _right\n", "    \n", "    if _tot_or_left == '.' and _right == '.':\n", "        # this params is dropped\n", "        pass\n", "    elif _tot_or_left and not _right:\n", "        # this case, only total value exists\n", "        data[str(_param)].append(eval(_tot_or_left))\n", "    else:\n", "        # this case, left/right values eixsts\n", "        data[str(_param+'/L')].append(eval(_tot_or_left))\n", "        data[str(_param+'/R')].append(eval(_right))"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["targets = ['Velocity', 'Cadence', 'Functional Amb. Profile']\n", "\n", "for item in ['Cycle Time(sec)', 'Stride Length(cm)', 'HH Base Support(cm)', 'Swing Time(sec)', 'Stance Time(sec)',\n", "             'Double Supp. Time(sec)', 'Toe In / Out']:\n", "    targets += list('{}/'.format(item) + x for x in ['L','R'])\n", "\n", "indices = []\n", "for x in basic_files:\n", "    pid,_,_,test_idx,_,trial_idx = os.path.splitext(os.path.basename(x))[0].split('_')\n", "    indices.append('_'.join([pid,test_idx,trial_idx]))\n", "        \n", "df = pd.DataFrame(data, index=natsorted(indices))\n", "target_df = df.loc[:,targets]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Add COP Files containing start/end timing info"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["import csv\n", "from tqdm import tqdm"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 3744/3744 [00:56<00:00, 65.70it/s]\n"]}], "source": ["\n", "cop_files = os.popen(f'ls {meta_home}/*_cop_test_*.txt').readlines()\n", "cop_files = [ x.strip() for x in cop_files ]\n", "\n", "def check_direction(positions):\n", "    if positions[0] < positions[-1]:\n", "        return 'approach'\n", "    else:\n", "        return 'leave'\n", "\n", "time_stamps = defaultdict(list)\n", "\n", "for f in tqdm(cop_files):\n", "    fp = open(f, 'r', encoding='utf-8')\n", "    reader = csv.reader(fp, delimiter='\\t')\n", "\n", "    tmp=[]\n", "\n", "    for i,line in enumerate(reader):\n", "        if i==0: continue  # skip first line!\n", "        # parse each line\n", "        _,time,_,pos,_ = line\n", "        \n", "        if not time: break # if we encounter empty str, break this loop!\n", "        time, pos = [ eval(x) for x in [time, pos] ]\n", "        \n", "        tmp.append([time,pos])\n", "        \n", "    tmp = [ x[0] for x in tmp if x[0]>0 and x[1] <=350 ]\n", "         \n", "    start, end = tmp[0], tmp[-1]\n", "    \n", "    \n", "    pid,_,_,test_idx,_,trial_idx = os.path.splitext(os.path.basename(f))[0].split('_')\n", "    sample_id = '_'.join([pid,test_idx,trial_idx])\n", "    \n", "    # add data\n", "    time_stamps['sample_id'].append(sample_id)\n", "    time_stamps['start'].append(start)\n", "    time_stamps['end'].append(end)    "]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["# insert start/end time data to corresponding row of \"target_df\" and create new index \"start\" / \"end\"\n", "for name in ['start', 'end']:\n", "    target_df.loc[time_stamps['sample_id'], name] = time_stamps[name]\n", "target_df = target_df.dropna() # drop nan"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"ename": "KeyError", "evalue": "'3'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "\u001b[0;32m~/.conda/envs/torch/lib/python3.6/site-packages/pandas/core/indexes/base.py\u001b[0m in \u001b[0;36mget_loc\u001b[0;34m(self, key, method, tolerance)\u001b[0m\n\u001b[1;32m   2656\u001b[0m             \u001b[0;32mtry\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 2657\u001b[0;31m                 \u001b[0;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_engine\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mget_loc\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mkey\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   2658\u001b[0m             \u001b[0;32mexcept\u001b[0m \u001b[0mKeyError\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32mpandas/_libs/index.pyx\u001b[0m in \u001b[0;36mpandas._libs.index.IndexEngine.get_loc\u001b[0;34m()\u001b[0m\n", "\u001b[0;32mpandas/_libs/index.pyx\u001b[0m in \u001b[0;36mpandas._libs.index.IndexEngine.get_loc\u001b[0;34m()\u001b[0m\n", "\u001b[0;32mpandas/_libs/hashtable_class_helper.pxi\u001b[0m in \u001b[0;36mpandas._libs.hashtable.PyObjectHashTable.get_item\u001b[0;34m()\u001b[0m\n", "\u001b[0;32mpandas/_libs/hashtable_class_helper.pxi\u001b[0m in \u001b[0;36mpandas._libs.hashtable.PyObjectHashTable.get_item\u001b[0;34m()\u001b[0m\n", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m: '3'", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-18-fa0971ae7e4f>\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[0;32m----> 1\u001b[0;31m \u001b[0mtarget_df\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m'3'\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[0;32m~/.conda/envs/torch/lib/python3.6/site-packages/pandas/core/frame.py\u001b[0m in \u001b[0;36m__getitem__\u001b[0;34m(self, key)\u001b[0m\n\u001b[1;32m   2925\u001b[0m             \u001b[0;32mif\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mcolumns\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mnlevels\u001b[0m \u001b[0;34m>\u001b[0m \u001b[0;36m1\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   2926\u001b[0m                 \u001b[0;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_getitem_multilevel\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mkey\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 2927\u001b[0;31m             \u001b[0mindexer\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mcolumns\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mget_loc\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mkey\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   2928\u001b[0m             \u001b[0;32mif\u001b[0m \u001b[0mis_integer\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mindexer\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   2929\u001b[0m                 \u001b[0mindexer\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;34m[\u001b[0m\u001b[0mindexer\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/.conda/envs/torch/lib/python3.6/site-packages/pandas/core/indexes/base.py\u001b[0m in \u001b[0;36mget_loc\u001b[0;34m(self, key, method, tolerance)\u001b[0m\n\u001b[1;32m   2657\u001b[0m                 \u001b[0;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_engine\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mget_loc\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mkey\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   2658\u001b[0m             \u001b[0;32mexcept\u001b[0m \u001b[0mKeyError\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 2659\u001b[0;31m                 \u001b[0;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_engine\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mget_loc\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_maybe_cast_indexer\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mkey\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   2660\u001b[0m         \u001b[0mindexer\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mget_indexer\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mkey\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mmethod\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mmethod\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mtolerance\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mtolerance\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   2661\u001b[0m         \u001b[0;32mif\u001b[0m \u001b[0mindexer\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mndim\u001b[0m \u001b[0;34m>\u001b[0m \u001b[0;36m1\u001b[0m \u001b[0;32mor\u001b[0m \u001b[0mindexer\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0msize\u001b[0m \u001b[0;34m>\u001b[0m \u001b[0;36m1\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32mpandas/_libs/index.pyx\u001b[0m in \u001b[0;36mpandas._libs.index.IndexEngine.get_loc\u001b[0;34m()\u001b[0m\n", "\u001b[0;32mpandas/_libs/index.pyx\u001b[0m in \u001b[0;36mpandas._libs.index.IndexEngine.get_loc\u001b[0;34m()\u001b[0m\n", "\u001b[0;32mpandas/_libs/hashtable_class_helper.pxi\u001b[0m in \u001b[0;36mpandas._libs.hashtable.PyObjectHashTable.get_item\u001b[0;34m()\u001b[0m\n", "\u001b[0;32mpandas/_libs/hashtable_class_helper.pxi\u001b[0m in \u001b[0;36mpandas._libs.hashtable.PyObjectHashTable.get_item\u001b[0;34m()\u001b[0m\n", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m: '3'"]}], "source": ["target_df['3']"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"ename": "FileNotFoundError", "evalue": "[Errno 2] No such file or directory: '../data/targets_dataframe.pkl'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mFileNotFoundError\u001b[0m                         <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-16-dfd5f691fdad>\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[0;31m# save data frame as pickle file\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m----> 2\u001b[0;31m \u001b[0mtarget_df\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mto_pickle\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m'../data/targets_dataframe.pkl'\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[0;32m~/.conda/envs/torch/lib/python3.6/site-packages/pandas/core/generic.py\u001b[0m in \u001b[0;36mto_pickle\u001b[0;34m(self, path, compression, protocol)\u001b[0m\n\u001b[1;32m   2591\u001b[0m         \u001b[0;32mfrom\u001b[0m \u001b[0mpandas\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mio\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mpickle\u001b[0m \u001b[0;32mimport\u001b[0m \u001b[0mto_pickle\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   2592\u001b[0m         return to_pickle(self, path, compression=compression,\n\u001b[0;32m-> 2593\u001b[0;31m                          protocol=protocol)\n\u001b[0m\u001b[1;32m   2594\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   2595\u001b[0m     \u001b[0;32mdef\u001b[0m \u001b[0mto_clipboard\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mexcel\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;32mTrue\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0msep\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;32mNone\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/.conda/envs/torch/lib/python3.6/site-packages/pandas/io/pickle.py\u001b[0m in \u001b[0;36mto_pickle\u001b[0;34m(obj, path, compression, protocol)\u001b[0m\n\u001b[1;32m     71\u001b[0m     f, fh = _get_handle(path, 'wb',\n\u001b[1;32m     72\u001b[0m                         \u001b[0mcompression\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mcompression\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m---> 73\u001b[0;31m                         is_text=False)\n\u001b[0m\u001b[1;32m     74\u001b[0m     \u001b[0;32mif\u001b[0m \u001b[0mprotocol\u001b[0m \u001b[0;34m<\u001b[0m \u001b[0;36m0\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     75\u001b[0m         \u001b[0mprotocol\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mpkl\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mHIGHEST_PROTOCOL\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/.conda/envs/torch/lib/python3.6/site-packages/pandas/io/common.py\u001b[0m in \u001b[0;36m_get_handle\u001b[0;34m(path_or_buf, mode, encoding, compression, memory_map, is_text)\u001b[0m\n\u001b[1;32m    428\u001b[0m         \u001b[0;32melse\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    429\u001b[0m             \u001b[0;31m# Python 3 and binary mode\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 430\u001b[0;31m             \u001b[0mf\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mopen\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mpath_or_buf\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mmode\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    431\u001b[0m         \u001b[0mhandles\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mappend\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mf\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    432\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mFileNotFoundError\u001b[0m: [Errno 2] No such file or directory: '../data/targets_dataframe.pkl'"]}], "source": ["# save data frame as pickle file\n", "target_df.to_pickle('../data/targets_dataframe.pkl')"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Fhinish\n"]}], "source": ["print('Fhinish')"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.8"}}, "nbformat": 4, "nbformat_minor": 2}