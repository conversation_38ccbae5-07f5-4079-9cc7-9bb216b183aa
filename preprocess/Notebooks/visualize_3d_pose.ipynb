{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%matplotlib notebook\n", "\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from mpl_toolkits.mplot3d import Axes3D\n", "import cv2\n", "from IPython.display import clear_output\n", "from IPython.core.debugger import set_trace\n", "from itertools import chain\n", "plt.ion()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Pose Output Format (BODY_25)\n", "<img src=\"https://github.com/CMU-Perceptual-Computing-Lab/openpose/raw/master/doc/media/keypoints_pose_25.png\" width=\"300\">"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["part_pairs = [(8,9),\n", "              (9,10),\n", "              (10,11),\n", "              (8,12),\n", "              (12,13),\n", "              (13,14),\n", "              (14,19),\n", "              (19,20),\n", "              (14,21),\n", "              (11,22),\n", "              (22,23),\n", "              (11,24)]\n", "\n", "part_map = {0: \"Nose\",\n", "            1: \"Neck\",\n", "            2:  \"<PERSON><PERSON><PERSON>\",\n", "            3:  \"R<PERSON>l<PERSON>\",\n", "            4:  \"<PERSON><PERSON><PERSON>\",\n", "            5:  \"LShoulder\",\n", "            6:  \"LEl<PERSON>\",\n", "            7:  \"L<PERSON><PERSON>\",\n", "            8:  \"MidHip\",\n", "            9:  \"RH<PERSON>\",\n", "            10: \"<PERSON><PERSON><PERSON>\",\n", "            11: \"<PERSON><PERSON><PERSON>\",\n", "            12: \"LHip\",\n", "            13: \"<PERSON><PERSON><PERSON>\",\n", "            14: \"<PERSON><PERSON><PERSON>\",\n", "            15: \"<PERSON><PERSON>ye\",\n", "            16: \"LEye\",\n", "            17: \"R<PERSON><PERSON>\",\n", "            18: \"L<PERSON>ar\",\n", "            19: \"<PERSON><PERSON>igT<PERSON>\",\n", "            20: \"LSmallToe\",\n", "            21: \"<PERSON><PERSON><PERSON>\",\n", "            22: \"<PERSON><PERSON><PERSON><PERSON>\",\n", "            23: \"<PERSON>mal<PERSON><PERSON><PERSON>\",\n", "            24: \"<PERSON><PERSON><PERSON>\",\n", "            25: \"Background\"}\n", "\n", "\n", "inv_part_map = { v: k for k,v in part_map.items() }\n", "\n", "LowerPart_idxs = sorted(list(set(chain(*part_pairs))))\n", "\n", "part_ix_map = {x: LowerPart_idxs.index(x) for x in LowerPart_idxs}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def show3Dpose(video_file, channels, ax, add_labels=False):\n", "    \"\"\"\n", "    Visualize a 3d skeleton\n", "    <PERSON><PERSON><PERSON>\n", "    channels: 75x1 vector. The pose to plot.\n", "    ax: mat<PERSON>lotlib 3d axis to draw on\n", "    lcolor: color for left part of the body\n", "    rcolor: color for right part of the body\n", "    add_labels: whether to add coordinate labels\n", "    Returns\n", "    Nothing. Draws on ax.\n", "    \"\"\"\n", "    \n", "    frame_height = cv2.VideoCapture(video_file).get(cv2.CAP_PROP_FRAME_HEIGHT)\n", "\n", "    vals = np.reshape( channels, (13, -1) )\n", "\n", "    I = np.array(part_pairs)[:,0]\n", "    J = np.array(part_pairs)[:,1]\n", "    \n", "    # Make connection matrix\n", "    points = []\n", "    for i in np.arange( len(I) ):\n", "        x, y, z = [np.array( [vals[part_ix_map[I[i]], j], vals[part_ix_map[J[i]], j]] ) for j in range(3)]\n", "        ax.plot(x, z, frame_height-y, lw=2, c='b')\n", "        points.append((x,z,frame_height-y))\n", "    \n", "    np_points = np.array(points)\n", "    ax.scatter3D(np_points[:,0],np_points[:,1],np_points[:,2], color='red')\n", "    \n", "    # redraw the canvas\n", "    fig.canvas.draw()\n", "    plt.cla()\n", "\n", "    RADIUS = 250 # space around the subject\n", "    xroot, yroot, zroot = vals[0,0], vals[0,1], vals[0,2]\n", "    ax.set_xlim3d([-RADIUS+xroot, RADIUS+xroot])\n", "    ax.set_ylim3d([-RADIUS+zroot, RADIUS+zroot])\n", "    ax.set_zlim3d([-RADIUS+yroot, RADIUS+yroot])\n", "\n", "    if add_labels:\n", "        ax.set_xlabel(\"x\")\n", "        ax.set_ylabel(\"z\")\n", "        ax.set_zlabel(\"y\")\n", "\n", "    # Get rid of the ticks and tick labels\n", "    ax.set_xticks([])\n", "    ax.set_yticks([])\n", "    ax.set_zticks([])\n", "\n", "    # Get rid of the panes (actually, make them white)\n", "    white = (1.0, 1.0, 1.0, 0.0)\n", "    ax.w_xaxis.set_pane_color(white)\n", "    ax.w_yaxis.set_pane_color(white)\n", "    # Keep z pane\n", "\n", "    # Get rid of the lines in 3d\n", "    ax.w_xaxis.line.set_color(white)\n", "    ax.w_yaxis.line.set_color(white)\n", "    ax.w_zaxis.line.set_color(white)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import time\n", "from matplotlib import animation"]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": false}, "outputs": [], "source": ["df = pd.read_pickle('./data/keypoints_dataframe.pkl')\n", "df = df[df.vid==\"1017175_test_0_trial_0.avi\"]\n", "\n", "fig = plt.figure(figsize=(8,8))\n", "ax = fig.add_subplot(111, projection='3d')\n", "\n", "\n", "pose_data = df.iloc[:,2:].values\n", "\n", "def update(t, pose_data):\n", "    show3Dpose('/mnt/hdd/GaitData/Video/1017175_test_0_trial_0.avi', pose_data[t], ax, add_labels=True)\n", "    return ax.lines\n", "\n", "anim = animation.FuncAnimation(fig, update, 284, fargs=(pose_data,),\n", "                               interval=50, blit=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 2", "language": "python", "name": "python2"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.12"}}, "nbformat": 4, "nbformat_minor": 2}