{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from openpose import pyopenpose as op\n", "import cv2\n", "import pyrealsense2 as rs\n", "import time\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "from IPython.core.debugger import set_trace\n", "from IPython.display import clear_output\n", "%matplotlib inline\n", "plt.ion()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dir_path = '/workspace/openpose/'\n", "nPoints = 25\n", "\n", "#Setting OpenPose parameters\n", "def set_params():\n", "\n", "    params = dict()\n", "    params[\"logging_level\"] = 3\n", "    params[\"output_resolution\"] = \"-1x-1\"\n", "    params[\"net_resolution\"] = \"-1x368\"\n", "    params[\"model_pose\"] = \"BODY_25\"\n", "    params[\"alpha_pose\"] = 0.6\n", "    params[\"scale_gap\"] = 0.3\n", "    params[\"scale_number\"] = 1\n", "    params[\"render_threshold\"] = 0.05\n", "    # If GPU version is built, and multiple GPUs are available, set the ID here\n", "    params[\"num_gpu_start\"] = 0\n", "    params[\"disable_blending\"] = False\n", "    # Ensure you point to the correct path where models are located\n", "    params[\"model_folder\"] = dir_path + \"models/\"\n", "    return params\n", "\n", "params = set_params()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Starting OpenPose\n", "opWrapper = op.WrapperPython()\n", "opWrapper.configure(params)\n", "opWrapper.start()\n", "\n", "# Define datum instance\n", "datum = op.Datum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def main():\n", "    \n", "    pipeline = rs.pipeline()\n", "    config = rs.config()\n", "\n", "    # depth_aligned_to_color  rs.stream.depth\n", "    config.enable_stream(rs.stream.color, 640, 480, rs.format.bgr8, 30)\n", "\n", "    pipeline.start(config)\n", "    \n", "    # stabilizing\n", "    for _ in range(30):\n", "        pipeline.wait_for_frames()\n", "    \n", "    lets_check = 30\n", "    cnt = 0\n", "    fps = -1.0\n", "\n", "    start_time = time.time()\n", "\n", "    while True:\n", "        frames = pipeline.wait_for_frames()\n", "        frame = cv2.cvtColor(np.asanyarray(frames.get_color_frame().get_data()), cv2.COLOR_BGR2RGB)\n", "\n", "        # update input data\n", "        datum.cvInputData = frame\n", "\n", "        # feed data\n", "        opWrapper.emplaceAndPop([datum])\n", "\n", "        # fetch keypoints, and output_image\n", "        keypoints = datum.poseKeypoints\n", "        res_frame = datum.cvOutputData\n", "        \n", "        try:\n", "            # Print the human pose keypoints, i.e., a [#people x #keypoints x 3]-dimensional numpy object with the keypoints of all the people on that image\n", "            if len(keypoints)>0:\n", "                # print('Human(s) Pose Estimated!')\n", "                res_frame = res_frame\n", "                \n", "        except TypeError:\n", "            # print('No humans detected!')\n", "            print(keypoints)\n", "            res_frame = frame\n", "        \n", "        # overlay over org frame\n", "        res_frame = cv2.addWeighted(frame, 0.2, res_frame, 0.8, 0.5)\n", "        \n", "        plt.figure(figsize=(10,10))\n", "\n", "        # Display the frame\n", "        plt.title(\"FrameCount : {}, [FPS] = {:.2f}\".format(cnt, fps))\n", "        plt.imshow(res_frame,)\n", "        plt.show()\n", "\n", "        # Display the frame until new frame is available\n", "        clear_output(wait=True)\n", "\n", "        cnt += 1\n", "\n", "        if cnt % lets_check == 0:\n", "            elaps = time.time()-start_time\n", "            fps = lets_check / elaps\n", "\n", "            # update start_time\n", "            start_time = time.time()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["main()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.8"}}, "nbformat": 4, "nbformat_minor": 2}