from ctypes import *
import math
import random

def sample(probs):
    s = sum(probs)
    probs = [a/s for a in probs]
    r = random.uniform(0, 1)
    for i in range(len(probs)):
        r = r - probs[i]
        if r <= 0:
            return i
    return len(probs)-1

def c_array(ctype, values):
    arr = (ctype*len(values))()
    arr[:] = values
    return arr

class BOX(Structure):
    _fields_ = [("x", c_float),
                ("y", c_float),
                ("w", c_float),
                ("h", c_float)]

class DETECTION(Structure):
    _fields_ = [("bbox", BOX),
                ("classes", c_int),
                ("prob", POINTER(c_float)),
                ("mask", POINTER(c_float)),
                ("objectness", c_float),
                ("sort_class", c_int)]


class IMAGE(Structure):
    _fields_ = [("w", c_int),
                ("h", c_int),
                ("c", c_int),
                ("data", POINTER(c_float))]

class METADATA(Structure):
    _fields_ = [("classes", c_int),
                ("names", POINTER(c_char_p))]


# lib = CDLL(LIB_PATH, RTLD_GLOBAL)
lib = CDLL('./libdarknet.so', RTLD_GLOBAL)

lib.network_width.argtypes = [c_void_p]
lib.network_width.restype = c_int
lib.network_height.argtypes = [c_void_p]
lib.network_height.restype = c_int

predict = lib.network_predict
predict.argtypes = [c_void_p, POINTER(c_float)]
predict.restype = POINTER(c_float)

set_gpu = lib.cuda_set_device
set_gpu.argtypes = [c_int]

make_image = lib.make_image
make_image.argtypes = [c_int, c_int, c_int]
make_image.restype = IMAGE

get_network_boxes = lib.get_network_boxes
get_network_boxes.argtypes = [c_void_p, c_int, c_int, c_float, c_float, POINTER(c_int), c_int, POINTER(c_int)]
get_network_boxes.restype = POINTER(DETECTION)

make_network_boxes = lib.make_network_boxes
make_network_boxes.argtypes = [c_void_p]
make_network_boxes.restype = POINTER(DETECTION)

free_detections = lib.free_detections
free_detections.argtypes = [POINTER(DETECTION), c_int]

free_ptrs = lib.free_ptrs
free_ptrs.argtypes = [POINTER(c_void_p), c_int]

network_predict = lib.network_predict
network_predict.argtypes = [c_void_p, POINTER(c_float)]

reset_rnn = lib.reset_rnn
reset_rnn.argtypes = [c_void_p]

load_net = lib.load_network
load_net.argtypes = [c_char_p, c_char_p, c_int]
load_net.restype = c_void_p

do_nms_obj = lib.do_nms_obj
do_nms_obj.argtypes = [POINTER(DETECTION), c_int, c_int, c_float]

do_nms_sort = lib.do_nms_sort
do_nms_sort.argtypes = [POINTER(DETECTION), c_int, c_int, c_float]

free_image = lib.free_image
free_image.argtypes = [IMAGE]

letterbox_image = lib.letterbox_image
letterbox_image.argtypes = [IMAGE, c_int, c_int]
letterbox_image.restype = IMAGE

load_meta = lib.get_metadata
lib.get_metadata.argtypes = [c_char_p]
lib.get_metadata.restype = METADATA

load_image = lib.load_image_color
load_image.argtypes = [c_char_p, c_int, c_int]
load_image.restype = IMAGE

rgbgr_image = lib.rgbgr_image
rgbgr_image.argtypes = [IMAGE]

predict_image = lib.network_predict_image
predict_image.argtypes = [c_void_p, IMAGE]
predict_image.restype = POINTER(c_float)

def classify(net, meta, im):
    out = predict_image(net, im)
    res = []
    for i in range(meta.classes):
        res.append((meta.names[i], out[i]))
    res = sorted(res, key=lambda x: -x[1])
    return res

def detect(net, meta, image, thresh=.3, hier_thresh=.5, nms=.45):
    im = load_image(image, 0, 0)
    num = c_int(0)
    pnum = pointer(num)
    predict_image(net, im)
    dets = get_network_boxes(net, im.w, im.h, thresh, hier_thresh, None, 0, pnum)
    num = pnum[0]
    if (nms): do_nms_obj(dets, num, meta.classes, nms);

    res = []
    for j in range(num):
        for i in range(meta.classes):
            if dets[j].prob[i] > 0:
                b = dets[j].bbox
                res.append((meta.names[i], dets[j].prob[i], (b.x, b.y, b.w, b.h)))
    res = sorted(res, key=lambda x: -x[1])
    free_image(im)
    free_detections(dets, num)
    return res

import cv2, os
import json
from tqdm import tqdm
import numpy as np
import lmdb
from ipdb import set_trace
import matplotlib.pyplot as plt
import matplotlib.patches as patches


# import the necessary packages
from imutils.video import VideoStream
from imutils.video import FPS
import argparse
import imutils
import time
import copy

plt.ion()

filename='/media/hossay/hdd1/GaitData/person_detect.db'
if os.path.exists(filename):
    os.system('rm -rf {}'.format(filename))
lmdb_env = lmdb.open(filename, map_size=int(1e9))

fig, axes = plt.subplots(1,2, figsize=(10,10))

def dict_to_binary(the_dict):
    str = json.dumps(the_dict)
    binary = ' '.join(format(ord(letter), 'b') for letter in str)
    return binary

def binary_to_dict(the_binary):
    jsn = ''.join(chr(int(x, 2)) for x in the_binary.split())
    d = json.loads(jsn)  
    return d

def write_lmdb(vid, frame, pos, ix):
    anno = dict_to_binary({'pos': pos})

    with lmdb_env.begin(write=True) as lmdb_txn:
        lmdb_txn.put('X_'+str(vid)+'@'+str(ix), cv2.imencode('.jpg', frame)[1])
        lmdb_txn.put('y_'+str(vid)+'@'+str(ix), anno)
        
def read_lmdb(filename):
    print 'Read lmdb'

    lmdb_env = lmdb.open(filename)
    lmdb_txn = lmdb_env.begin()
    lmdb_cursor = lmdb_txn.cursor()

    #also can do it without iteration via txn.get('key1')?

    n_counter=0
    with lmdb_env.begin() as lmdb_txn:
        with lmdb_txn.cursor() as lmdb_cursor:
            for key, value in lmdb_cursor:  
                print key
                if('X' in key):
                    X_str= np.frombuffer(value, dtype=np.uint8)
                    X= cv2.imdecode(X_str, cv2.IMREAD_COLOR)
                    print 'X.shape', X.shape
                if('y' in key):
                    print binary_to_dict(value)

                n_counter=n_counter+1

    print 'n_samples',n_counter

    
    
def Normal(x, mu=0.0, sigma=1.0):
    return (1/(sigma * np.sqrt(2 * np.pi)) * np.exp( - (x - mu)**2 / (2 * sigma**2) ))

def get_corner_points(coord):
    x,y,w,h = coord
    return ( int(x-w/2), int(y-h/2),   # top-left
             int(x+w/2), int(y+h/2) )  # bottom-right
    
def bb_intersection_over_union(boxA, boxB):
    # determine the (x, y)-coordinates of the intersection rectangle
    xA = max(boxA[0], boxB[0])
    yA = max(boxA[1], boxB[1])
    xB = min(boxA[2], boxB[2])
    yB = min(boxA[3], boxB[3])

    # compute the area of intersection rectangle
    interArea = max(0, xB - xA + 1) * max(0, yB - yA + 1)

    # compute the area of both the prediction and ground-truth
    # rectangles
    boxAArea = (boxA[2] - boxA[0] + 1) * (boxA[3] - boxA[1] + 1)
    boxBArea = (boxB[2] - boxB[0] + 1) * (boxB[3] - boxB[1] + 1)

    # compute the intersection over union by taking the intersection
    # area and dividing it by the sum of prediction + ground-truth
    # areas - the interesection area
    iou = interArea / float(boxAArea + boxBArea - interArea)

    # return the intersection over union value
    return iou


def detect_from_video(net, meta, video, frame_home):
    video_name = os.path.basename(os.path.splitext(video)[0])
    frame_save_path = os.path.join(frame_home, video_name)
    
    # creat directory if it does not exist (recursive mkdir supports)
    if not os.path.exists(frame_save_path):
        os.makedirs(frame_save_path)
        
    cap = cv2.VideoCapture(video)
    H, W = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT)), int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))

    
    buff = [] # (raw_frame, bbox, cropped)
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        cv2.imwrite('tmp.jpg', frame)
        res = detect(net, meta, 'tmp.jpg')
        
        person_BB = []
        for i in range(len(res)):
            if res[i]:
                lab, conf, (bx,by,bw,bh) = res[i]
                if lab=='person' and conf > 0.8:
                    p_BB = (bx,by,bw,bh)
                    person_BB.append(p_BB)
        
        if not person_BB: continue
            
        # who is right-most
        valid_BB = sorted(person_BB, key=lambda x: -x[0])[0]
        bx,by,bw,bh = valid_BB

        cropped = frame[max(0,int(by-bh/2)):min(H,int(by+bh/2)), max(0,int(bx-bw/2)):min(W,int(bx+bw/2))]
        buff.append([ valid_BB, frame, cropped ])

    
    # seq of entire traces
    cur_slice = np.array([ x[0] for x in buff ])
    
    # binary search to get bbox with small variance(=stationary) w.r.t ytrace
    while True:
        left_split = cur_slice[:len(cur_slice)//2]
        right_split = cur_slice[len(cur_slice)//2:]
        
        # move to split with smaller std!
        cur_slice = left_split if left_split[:,1].std() < right_split[:,1].std() else right_split
        
        if cur_slice[:,1].std() < 1.0:
            break
    
    # sort by bbox size
    smallest_box = sorted(cur_slice, key=lambda x: x[2]*x[3])[0]
    
    # sample idx with small variance
    sample_idx = np.array([ x[0] for x in buff ]).tolist().index(smallest_box.tolist())
    
    # select ref_frame
    ref_frame = buff[sample_idx][2]
    
    # select ref_BB
    bx,by,bw,bh = ref_BB = buff[sample_idx][0]
        
    lower_left=(int(bx-bw/2), int(by-bh/2)) # lower_left point of rect
    ref_rect = patches.Rectangle(lower_left, bw, bh, linewidth=1, edgecolor='m', facecolor='none')

    axes[0].imshow(cv2.cvtColor(ref_frame, cv2.COLOR_BGR2RGB))
    axes[0].set_title('ReferenceFrame')
    
    def calc_hist(frame):
        HSV = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
        hist = cv2.calcHist([HSV], [0], None, [8], [0, 181])
        hist = cv2.normalize(hist, hist).flatten()

        return hist
    
    for c, (valid_BB, frame, cropped) in enumerate(buff):
        
        # convert coordination (xc,yc,w,h) -> (xmin,ymin,xmax,ymax)
        box = get_corner_points(valid_BB)
        box_ref = get_corner_points(ref_BB)
        
        # compute iou with box_ref
        iou = bb_intersection_over_union(box, box_ref)
        
        # compute histogram
        hist = calc_hist(cropped)
        ref_hist = calc_hist(ref_frame)
                
        # compare histogram        
        score = cv2.compareHist(hist,ref_hist,cv2.HISTCMP_CORREL)
        ref_size = (box_ref[2]-box_ref[0])*(box_ref[3]-box_ref[1])
        yolo_size = (box[2]-box[0])*(box[3]-box[1])
        
        print(video_name, c, "Hist corr (with ref): {:.4f} / IOU : {:.4f} / ref_size: {} / yolo_size: {}".format(score, iou,  ref_size, yolo_size))

        axes[1].set_title('LiveStream')
        
        single_frame_path = os.path.join(frame_save_path, '{}.jpg'.format(c))        
        
        if iou >= 0.05 and yolo_size >= ref_size:

            # visualize
            bx,by,bw,bh = valid_BB

            lower_left=(int(bx-bw/2), int(by-bh/2)) # lower_left point of rect
            rect = patches.Rectangle(lower_left, bw, bh, linewidth=1, edgecolor='g', facecolor='none')

            axes[1].imshow(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
            axes[1].add_patch(rect)

            axes[1].add_patch(ref_rect)

            # ground point (valid)
            gd_x, gd_y = (int(bx), int(by+bh/2))

            axes[1].scatter(gd_x, gd_y, s=100, c='r')

            plt.xlim(0,640)
            plt.ylim(480,0)

            plt.pause(1e-4)
            axes[1].clear()

            # info about cropped_frame
            (h,w,_) = cropped.shape
            npad = ((((H-h)/2,(H-h)-(H-h)/2), ((W-w)/2,(W-w)-(W-w)/2), (0,0)))

            # zero padding around cropped frame
            padded = np.pad(cropped, npad, 'constant', constant_values=(0))

            # write data to lmdb
            write_lmdb(video_name, cropped, valid_BB, c)

            cv2.imwrite(single_frame_path, cropped)

        else:
            empty_frame = np.zeros_like(frame)  # empty frame

            # write data to lmdb
            write_lmdb(video_name, empty_frame, [], c)
            cv2.imwrite(single_frame_path, empty_frame)
        
        c += 1
        
    cap.release()

def detect_from_bunch_of_video(net, meta, video_home, frame_home):
    #video_list = list(os.path.join(video_home,x) for x in os.listdir(video_home) if not x.startswith('vid'))[2:10]
    video_list = ["/media/hossay/hdd1/GaitData/Video/1853413_test_2_trial_2.avi"]
    for video in tqdm(video_list):
        detect_from_video(net,meta,video, frame_home=frame_home)
        
if __name__ == "__main__":
    net = load_net("cfg/yolov3.cfg", "yolov3.weights", 0)
    meta = load_meta("cfg/coco.data")
    detect_from_bunch_of_video(net, meta, '/media/hossay/hdd1/GaitData/Video', frame_home='/media/hossay/hdd1/GaitData/CroppedFrames')
