from ctypes import *
import math
import random

def sample(probs):
    s = sum(probs)
    probs = [a/s for a in probs]
    r = random.uniform(0, 1)
    for i in range(len(probs)):
        r = r - probs[i]
        if r <= 0:
            return i
    return len(probs)-1

def c_array(ctype, values):
    arr = (ctype*len(values))()
    arr[:] = values
    return arr

class BOX(Structure):
    _fields_ = [("x", c_float),
                ("y", c_float),
                ("w", c_float),
                ("h", c_float)]

class DETECTION(Structure):
    _fields_ = [("bbox", BOX),
                ("classes", c_int),
                ("prob", POINTER(c_float)),
                ("mask", POINTER(c_float)),
                ("objectness", c_float),
                ("sort_class", c_int)]


class IMAGE(Structure):
    _fields_ = [("w", c_int),
                ("h", c_int),
                ("c", c_int),
                ("data", POINTER(c_float))]

class METADATA(Structure):
    _fields_ = [("classes", c_int),
                ("names", POINTER(c_char_p))]


# lib = CDLL(LIB_PATH, RTLD_GLOBAL)
import os
darknet_home = os.path.dirname(os.path.dirname(__file__))
lib = CDLL(os.path.join(darknet_home,'./libdarknet.so'), RTLD_GLOBAL)

lib.network_width.argtypes = [c_void_p]
lib.network_width.restype = c_int
lib.network_height.argtypes = [c_void_p]
lib.network_height.restype = c_int

predict = lib.network_predict
predict.argtypes = [c_void_p, POINTER(c_float)]
predict.restype = POINTER(c_float)

set_gpu = lib.cuda_set_device
set_gpu.argtypes = [c_int]

make_image = lib.make_image
make_image.argtypes = [c_int, c_int, c_int]
make_image.restype = IMAGE

get_network_boxes = lib.get_network_boxes
get_network_boxes.argtypes = [c_void_p, c_int, c_int, c_float, c_float, POINTER(c_int), c_int, POINTER(c_int)]
get_network_boxes.restype = POINTER(DETECTION)

make_network_boxes = lib.make_network_boxes
make_network_boxes.argtypes = [c_void_p]
make_network_boxes.restype = POINTER(DETECTION)

free_detections = lib.free_detections
free_detections.argtypes = [POINTER(DETECTION), c_int]

free_ptrs = lib.free_ptrs
free_ptrs.argtypes = [POINTER(c_void_p), c_int]

network_predict = lib.network_predict
network_predict.argtypes = [c_void_p, POINTER(c_float)]

reset_rnn = lib.reset_rnn
reset_rnn.argtypes = [c_void_p]

load_net = lib.load_network
load_net.argtypes = [c_char_p, c_char_p, c_int]
load_net.restype = c_void_p

do_nms_obj = lib.do_nms_obj
do_nms_obj.argtypes = [POINTER(DETECTION), c_int, c_int, c_float]

do_nms_sort = lib.do_nms_sort
do_nms_sort.argtypes = [POINTER(DETECTION), c_int, c_int, c_float]

free_image = lib.free_image
free_image.argtypes = [IMAGE]

letterbox_image = lib.letterbox_image
letterbox_image.argtypes = [IMAGE, c_int, c_int]
letterbox_image.restype = IMAGE

load_meta = lib.get_metadata
lib.get_metadata.argtypes = [c_char_p]
lib.get_metadata.restype = METADATA

load_image = lib.load_image_color
load_image.argtypes = [c_char_p, c_int, c_int]
load_image.restype = IMAGE

rgbgr_image = lib.rgbgr_image
rgbgr_image.argtypes = [IMAGE]

predict_image = lib.network_predict_image
predict_image.argtypes = [c_void_p, IMAGE]
predict_image.restype = POINTER(c_float)

def classify(net, meta, im):
    out = predict_image(net, im)
    res = []
    for i in range(meta.classes):
        res.append((meta.names[i], out[i]))
    res = sorted(res, key=lambda x: -x[1])
    return res

def get_dim(arr):
    res = 1
    for s in arr.shape:
        res *= s
    return res

def detect(net, meta, im, image, thresh=.5, hier_thresh=.5, nms=.45):

    h,w,c = image.shape
    image = (cv2.cvtColor(image, cv2.COLOR_BGR2RGB) / 255).transpose(2, 0, 1).reshape(c * h, w).astype(np.float32)
    data = image.flatten()

    # data allocation
    im.data = data.ctypes.data_as(POINTER(c_float))

    im.h = h
    im.w = w
    im.c = c

    num = c_int(0)
    pnum = pointer(num)
    predict_image(net, im)
    dets = get_network_boxes(net, im.w, im.h, thresh, hier_thresh, None, 0, pnum)
    num = pnum[0]
    if (nms): do_nms_obj(dets, num, meta.classes, nms);

    res = []
    for j in range(num):
        for i in range(meta.classes):
            if dets[j].prob[i] > 0:
                b = dets[j].bbox
                res.append((meta.names[i], dets[j].prob[i], (b.x, b.y, b.w, b.h)))
    res = sorted(res, key=lambda x: -x[1])
    #free_image(im)
    free_detections(dets, num)
    return res

import cv2, os
import json
from tqdm import tqdm
import numpy as np
from ipdb import set_trace
import shutil

def detect_from_video(net, meta, video, res_path):
    video_name = os.path.basename(os.path.splitext(video)[0])
    
    cap = cv2.VideoCapture(video)
    c = 0

    while True:
        ret, frame = cap.read()
        if not ret:
            break
        c += 1
        cv2.imwrite('tmp.jpg', frame)
        res = detect(net, meta, 'tmp.jpg')
        
        person_BB = []
        for i in range(len(res)):
            if res[i]:
                lab, conf, (bx,by,bw,bh) = res[i]
                if lab=='person':
                    p_BB = [bx,by,bw,bh]
                    person_BB.append(p_BB)
        
        if not person_BB: continue
        
        for i in range(len(person_BB)):
            person_BB[i] = ','.join([ str(e) for e in person_BB[i] ])
            
        person_BB = '@'.join(person_BB)
        line = video_name + '\t' + str(c) + '\t' + person_BB
        
        os.system("echo \'{}\' >> {}".format(line, res_path))
        
    cap.release()

def detect_from_bunch_of_video(net, meta, video_home, res_path):
    # re-create res_dir
    res_dir = os.path.dirname(res_path)

    print('re-create \'results\' directory...')
    if os.path.exists(res_dir):
        shutil.rmtree(res_dir, ignore_errors=True)
    os.makedirs(res_dir)
    
    video_list = list(os.path.join(video_home,x) for x in os.listdir(video_home) if not x.startswith('vid'))
    for video in tqdm(video_list):
        detect_from_video(net,meta,video,res_path)
        
if __name__ == "__main__":
    cfg_path = os.path.join(darknet_home, "cfg/yolov3.cfg")
    weight_path = os.path.join(darknet_home, "yolov3.weights")
    anno_path = os.path.join(darknet_home, "cfg/coco.data")
    res_path = os.path.join(darknet_home, 'results/person_bbox_merged.txt')

    net = load_net(cfg_path, weight_path, 0)
    meta = load_meta(anno_path)
    detect_from_bunch_of_video(net, meta, '/data/GaitData/Video', res_path=res_path)