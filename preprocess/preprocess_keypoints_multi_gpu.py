#!/usr/bin/env python
# coding: utf-8

# # Pose Output Format (BODY_25)
# <img src="https://github.com/CMU-Perceptual-Computing-Lab/openpose/raw/master/doc/media/keypoints_pose_25.png" width="200">

# In[1]:
from __future__ import division, print_function


part_pairs = [(8,9),
              (9,10),
              (10,11),
              (8,12),
              (12,13),
              (13,14),
              (14,19),
              (19,20),
              (14,21),
              (11,22),
              (22,23),
              (11,24)]

part_map = {0: "Nose",
            1: "<PERSON>",
            2:  "<PERSON><PERSON><PERSON>",
            3:  "<PERSON><PERSON><PERSON><PERSON>",
            4:  "<PERSON><PERSON><PERSON>",
            5:  "<PERSON><PERSON>houlder",
            6:  "<PERSON><PERSON><PERSON><PERSON>",
            7:  "<PERSON><PERSON><PERSON>",
            8:  "MidHip",
            9:  "RHip",
            10: "RKnee",
            11: "RAnk<PERSON>",
            12: "LHip",
            13: "<PERSON>Knee",
            14: "<PERSON><PERSON><PERSON>",
            15: "<PERSON>Eye",
            16: "LEye",
            17: "REar",
            18: "LEar",
            19: "LBigToe",
            20: "LS<PERSON><PERSON>Toe",
            21: "LHeel",
            22: "RBigT<PERSON>",
            23: "RSmallToe",
            24: "RHeel",
            25: "Background"}


inv_part_map = { v: k for k,v in part_map.items() }


# # Extract keypoints from video file

# In[2]:




# In[3]:


from openpose import pyopenpose as op
import cv2
import pyrealsense2 as rs
import time
import numpy as np
from IPython.core.debugger import set_trace
from IPython.display import clear_output
import os
import json
from itertools import chain


# In[4]:


LowerPart_idxs = sorted(list(set(chain(*part_pairs))))


# In[5]:


LowerPart_idxs


# In[6]:


dir_path = '/workspace/openpose/'
save_path='../data/keypoints_from_openpose.csv'

if os.path.exists(save_path):
    os.remove(save_path)

nPoints = 25

#Setting OpenPose parameters
def set_params(gpu_id):

    params = dict()
    params["logging_level"] = 3
    params["output_resolution"] = "-1x-1"
    params["net_resolution"] = "-1x368"
    params["model_pose"] = "BODY_25"
    params["alpha_pose"] = 0.6
    params["scale_gap"] = 0.3
    params["scale_number"] = 1
    params["render_threshold"] = 0.05
    # If GPU version is built, and multiple GPUs are available, set the ID here
    params["num_gpu"] = 1
    params["num_gpu_start"] = gpu_id
    params["disable_blending"] = False
    # Ensure you point to the correct path where models are located
    params["model_folder"] = dir_path + "models/"
    return params


import argparse

parser = argparse.ArgumentParser()
parser.add_argument("--gpu", type=str)
args = parser.parse_args()

opWrappers = []

devices = list(eval(args.gpu))

for d in devices:
    params = set_params(gpu_id=d)

    # Starting OpenPose
    opWrapper = op.WrapperPython()
    opWrapper.configure(params)
    opWrapper.start()

    opWrappers.append(opWrapper)


# Define datum instance
datum = op.Datum()


# In[7]:


def get_keypoints(opWrapper, datum, frame):
    # update input data
    datum.cvInputData = frame

    # feed data
    opWrapper.emplaceAndPop([datum])

    # fetch keypoints, and output_image
    keypoints = datum.poseKeypoints
    res_frame = datum.cvOutputData
    
    try:
        # Print the human pose keypoints, i.e., a [#people x #keypoints x 3]-dimensional numpy object with the keypoints of all the people on that image
        if len(keypoints)>0:
            # print('Human(s) Pose Estimated!')
            res_frame = res_frame

    except TypeError:
        # print('No humans detected!')
        keypoints = None
        res_frame = frame
        
    # overlay over org frame
    res_frame = cv2.addWeighted(frame, 0.2, res_frame, 0.8, 0.5)
    
    return keypoints, res_frame


def extract_keypoints_from_file(opWrapper, datum, video_file,
                                visualize=False,):
    
    video_capture = cv2.VideoCapture(video_file)
    
    frame_cnt = 0
    
    while frame_cnt < video_capture.get(cv2.CAP_PROP_FRAME_COUNT):
            
        _, frame = video_capture.read()
        
        # parse output
        keypoints, res_frame = get_keypoints(opWrapper, datum, frame)
        
        vid = os.path.basename(video_file)
        
        if keypoints is not None:
            save_keypoints_as_csv_format(keypoints, vid, frame_cnt)
                    
        frame_cnt += 1
                
def save_keypoints_as_csv_format(keypoints, vid, frame_cnt):
    all_keypoints = []
    for i in range(len(keypoints)):
        p_keypoints_flat = keypoints[i][LowerPart_idxs].flatten().tolist()
        all_keypoints.append(p_keypoints_flat)
    
    columns = [ str(x) for x in [vid, frame_cnt] + list(chain(all_keypoints)) ]

    line = ','.join(columns) + '\n'
    
    with open(save_path, 'a+') as f:
        f.write(line)


# In[10]:


video_files = list(x.strip() for x in os.popen(u'ls /mnt/hdd/GaitData/Video/*.avi').readlines())
video_files = list(x for x in video_files if not os.path.basename(x).startswith('vid'))    


n_batch = len(devices)
step = int( len(video_files) / n_batch )

batches = []
for i in range(n_batch):
    start = i*step
    end = (i+1)*step if i != n_batch-1 else None
    batch = video_files[start:end]
    batches.append(batch)

# In[13]:

from time import sleep
from tqdm import tqdm
from joblib import Parallel, delayed
    
def progresser(batch):

    # parse batch tuple composed of (item, ix)
    item, ix, opWrapper = batch
    
    # This line is the strange hack
    print(' ')

    text = "--->progresser #{}".format(ix)
    for i in tqdm(item, desc=text):
        extract_keypoints_from_file(opWrapper, datum, i, visualize=False)
        sleep(0.001)

# run 
Parallel(n_jobs=-1, backend="threading")(delayed(progresser)(batch) for batch in zip(batches, range(n_batch),opWrappers))

# # Parse .csv File -> Pandas dataframe

# In[ ]:


from collections import defaultdict

def rank_of_list(x):
    n_rank = 0
    item = x
    while True:
        try:
            item = item[0]
        except TypeError:
            return n_rank
        n_rank += 1

class DatasetParser:
    def __init__(self, csv_file, resolution=(640,480), Xpos_tolerance=100):
        self.csv_file = csv_file
        self.resolution = (640, 480)
        self.Xpos_tolerance = Xpos_tolerance
        
        self.dataset = defaultdict(lambda: [])
        self.size = 0
        
        print('Start Dataset Parsing ...')
        self.run()

    def parse_line_and_register_into_dataset(self, line):
        line = line.strip().split(',')
        try:
            vid, frame_cnt = line[:2]
        except:
            return
        
        frame_width = self.resolution[0]

        try:
            multi_person_pose = eval(','.join(line[2:]))
        except:
            return
        if rank_of_list(multi_person_pose) < 2:
            multi_person_pose = [multi_person_pose]
            patient_idx = 0
        
        multi_Xpos = []
        for Xpos in np.array(multi_person_pose)[:,::3]:
            valid_Xpos = Xpos[Xpos>0]
            if valid_Xpos.size==0:
                continue
            multi_Xpos.append(valid_Xpos.mean())
        
        if not multi_Xpos:
            # if all zero Xpos, return!!
            return

        # who is right most person?
        patient_idx = np.argmax(multi_Xpos)
        patient_Xpos = multi_Xpos[patient_idx]
        
        if patient_Xpos < frame_width / 2 - self.Xpos_tolerance or \
            patient_Xpos > frame_width / 2 + self.Xpos_tolerance:
            return
        
        self.dataset['vid'].append(vid)
        self.dataset['frame_cnt'].append(eval(frame_cnt))
        for i, part_ix in enumerate(LowerPart_idxs):
            part_name = part_map[part_ix]
            pos_val = multi_person_pose[patient_idx]
            for j, coord in enumerate(list('XYZ')):
                self.dataset['{}_{}'.format(part_name, coord)].append(pos_val[3*i+j])
        
        self.size += 1
        
    def run(self):
        from tqdm import tqdm_notebook as tqdm
        lines = open(self.csv_file).readlines()
        for line in tqdm(lines):
            self.parse_line_and_register_into_dataset(line)

# In[ ]:


dataset_parser = DatasetParser(csv_file='../data/keypoints_from_openpose.csv')


# In[ ]:


import pandas as pd


# In[ ]:


df = pd.DataFrame(data=dataset_parser.dataset,)
df = df[sorted([x for x in df.columns if x], key=lambda x: inv_part_map.get(x.split('_')[0]))]
df.to_pickle('../data/keypoints_dataframe.pkl')
