#!/usr/bin/env python
# coding: utf-8

# In[1]:


from __future__ import unicode_literals

from collections import defaultdict
import pandas as pd
from tqdm import tqdm
# # Basic Files containing GAIT params

import glob
import os
from natsort import natsorted

def load_basic_data(meta_home):
    basic_files = natsorted(glob.glob(os.path.join(meta_home, '*_basic_test_*.txt')))
    df_to_merge = []
    for f in tqdm(basic_files, desc='status'):
        df = pd.read_csv(f, delimiter='\t').dropna()  # drop nan
        df_to_merge.append(df)

    df = pd.concat(df_to_merge)

    data = defaultdict(list)

    params = df["PARAMETER DESCRIPTION"].values.tolist()
    tot_or_left = df["Total/Left"].values.tolist()
    right = df["Right"].values.tolist()

    # strip before further processing
    params = [ x.strip() for x in params ]
    tot_or_left = [ x.strip() for x in tot_or_left ]
    right = [ x.strip() for x in right ]

    for _param, _tot_or_left, _right in zip(params, tot_or_left, right):

        _tot_or_left = '0.0' if _tot_or_left=='.' else _tot_or_left
        _right = '0.0' if _right=='.' else _right

        if _tot_or_left == '.' and _right == '.':
            # this params is dropped
            pass
        elif _tot_or_left and not _right:
            # this case, only total value exists
            data[str(_param)].append(eval(_tot_or_left))
        else:
            # this case, left/right values eixsts
            data[str(_param+'/L')].append(eval(_tot_or_left))
            data[str(_param+'/R')].append(eval(_right))

    return data, basic_files


# In[8]:
def create_target_df(meta_home, save_path,
                     single_cols,
                     pair_cols):

    data, basic_files = load_basic_data(meta_home=meta_home)
    targets = single_cols

    for item in pair_cols:
        targets += list('{}/'.format(item) + x for x in ['L','R'])

    indices = []
    for x in basic_files:
        pid,_,_,test_idx,_,trial_idx = os.path.splitext(os.path.basename(x))[0].split('_')
        indices.append('_'.join([pid,test_idx,trial_idx]))

    df = pd.DataFrame(data, index=natsorted(indices))
    target_df = df.loc[:,targets]
    target_df = target_df.dropna() # drop nan

    # save data frame as pickle file
    target_df.to_pickle(save_path)

    print('Fhinish')
