#!/bin/bash

mkdir -p ../data

source $1
AVAILABLE_GPU=$(lspci|grep 'VGA\|Display' | grep "NVIDIA" | wc -l)
REQ_GPU=${#GPU[@]}

if (( REQ_GPU > AVAILABLE_GPU ))
then
    echo "#############################"
    echo "Invalid GPU selection"
    echo "#############################"
    echo "-> Failed!!"
    exit 0
fi


echo "->Start metadata preprocessing"
python3 ../preprocess_metadata.py    # metadata
echo "->Done!"
echo ""

echo "->Start keypoints preprocessing"
if (( REQ_GPU == 1 ))
then
    echo "-->Single GPU mode..."
    python ../preprocess_keypoints.py

elif (( REQ_GPU > 1 ))
then
    echo "-->Multi GPU mode..."
    python ../preprocess_keypoints_multi_gpu.py --gpu=$(IFS=, ; echo "${GPU[*]}")  # keypoins
fi

echo "->Done!"

# save data
apt-get update && \
apt-get install -y pv && \
tar cf - ../data -P | pv -s $(du -sb ../data | awk '{print $1}') | gzip > /mnt/hdd/GaitData/data.tar.gz
