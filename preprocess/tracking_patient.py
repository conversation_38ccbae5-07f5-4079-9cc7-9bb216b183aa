#!/usr/bin/env python
# coding: utf-8

# In[ ]:


import numpy as np
import matplotlib.pyplot as plt
import os
import pandas as pd
from collections import defaultdict
from IPython.core.debugger import set_trace
import cv2
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from IPython.display import clear_output
from tqdm import tqdm
from itertools import combinations
import math
from numpy.linalg import norm

plt.ion()

def get_corner_points(coord, H=480, W=640):
    x,y,w,h = coord
    return ( max(0,x-w/2), max(0,y-h/2),   # (xmin,ymin)
             min(W, x+w/2), min(H,y+h/2) )  # (xmax,ymax)
    
def bb_intersection_over_union(boxA, boxB):
    # determine the (x, y)-coordinates of the intersection rectangle
    xA = max(boxA[0], boxB[0])
    yA = max(boxA[1], boxB[1])
    xB = min(boxA[2], boxB[2])
    yB = min(boxA[3], boxB[3])

    # compute the area of intersection rectangle
    interArea = max(0, xB - xA + 1) * max(0, yB - yA + 1)

    # compute the area of both the prediction and ground-truth
    # rectangles
    boxAArea = (boxA[2] - boxA[0] + 1) * (boxA[3] - boxA[1] + 1)
    boxBArea = (boxB[2] - boxB[0] + 1) * (boxB[3] - boxB[1] + 1)

    # compute the intersection over union by taking the intersection
    # area and dividing it by the sum of prediction + ground-truth
    # areas - the interesection area
    iou = interArea / float(boxAArea + boxBArea - interArea)

    # return the intersection over union value
    return iou


# In[ ]:


class Box:
    def __init__(self, pos, lab):
        self.pos = pos
        self.lab = lab
        
    def set_label(self, lab):
        self.lab = lab
        
def init_box(pos, lab):
    return [ Box(pos=get_corner_points(e), lab=v) for e,v in zip(pos,lab) ]


# In[ ]:


def stack_tracking_log(localizer, vid, video, tracking_log):

    detection_generator = localizer.detect_from_video(video=video)

    try:
        # initial box positions & frame idx
        initial_ = next(detection_generator)
        initial_pos, initial_idx = initial_.person_pos, initial_.idx
        initial_pos = init_box(pos=initial_pos, lab=range(len(initial_pos)))
    except:
        set_trace()

    lab2history = {k:[(v,initial_idx)] for k,v in zip(range(len(initial_pos)), initial_pos)}
    tracking_log[vid] = lab2history

    def calculate_distance(boxA, boxB):
        center_A = np.array([(boxA[0]+boxA[2])/2, (boxA[1]+boxA[3])/2])
        center_B = np.array([(boxB[0]+boxB[2])/2, (boxB[1]+boxB[3])/2])

        return norm(center_A-center_B)

    for res in detection_generator:
        pos, idx = res.person_pos, res.idx
        cur_pos = init_box(pos, lab=[None]*len(pos))
        for cur_box in cur_pos:
            log = []
            for prev_box in list(lab2history.values()):
                last_prev_box, _ = prev_box[-1]           
                dist = calculate_distance(last_prev_box.pos, cur_box.pos)
                log.append((last_prev_box,cur_box,dist))

            sorted_log = sorted(log, key=lambda x: x[2])

            # nearest prev box
            nearest_prev_box, target_cur_box, dist_min = sorted_log[0]

            if dist_min < 50:
                # assign prev_box label 
                cur_pos[cur_pos.index(target_cur_box)].set_label(nearest_prev_box.lab)
                tracking_log[vid][nearest_prev_box.lab].append((target_cur_box, idx))
            else:
                # add new label
                new_lab = max(lab2history) + 1
                cur_pos[cur_pos.index(target_cur_box)].set_label(new_lab)
                tracking_log[vid][new_lab] = [(target_cur_box, idx)]


    # pop label histories that have length less than half of entire sequence
    for lab in list(lab2history.keys()):
        if len(tracking_log[vid][lab]) < len(localizer)*0.5:
            tracking_log[vid].pop(lab)

def get_XY_traces(vid, tracking_log, plot=False):
    if plot:
        fig, ax = plt.subplots(nrows=2, ncols=1, figsize=(11,11))
        
    lab_center_dist = defaultdict(list)

    for lab in tracking_log[vid].keys():
        for box, c in tracking_log[vid][lab]:
            xmin,ymin,xmax,ymax = box.pos
            xc, yc = xmin + (xmax-xmin)/2, ymin + (ymax-ymin)/2
            lab_center_dist[lab].append((xc,yc))
        dist_arr = np.array(lab_center_dist[lab])
        
        if plot:
            ax[0].plot(dist_arr[:,0], label=str(lab)+'-'+'xpos')
            ax[0].legend()
            ax[1].plot(dist_arr[:,1], label=str(lab)+'-'+'ypos')
            ax[1].legend()
        
    return lab_center_dist

def signaltonoise(a, axis=0, ddof=0):
    a = np.asanyarray(a)
    m = a.mean(axis)
    sd = a.std(axis=axis, ddof=ddof)
    return np.where(sd == 0, 0, m/sd)

def select_target_trace(lab_center_dist):
    # sorted based on length of y-trace (descending order)
    # also, if SNR(Signal to noise) is too big, it is considered as noise data
    sorted_dist = sorted({i: np.array(lab_center_dist[i])[:, 1].std() / np.array(lab_center_dist[i])[:, 0].std() / signaltonoise(np.array(lab_center_dist[i])[:, 1]) for i in lab_center_dist.keys()}.items(), key=lambda x: -x[1])
    target_trace = sorted_dist[0]

    return target_trace

def analize_XY_traces(vid, tracking_log):
    lab_center_dist = defaultdict(list)
    
    tmp = defaultdict(list)
    for lab in tracking_log[vid].keys():
        for box, c in tracking_log[vid][lab]:
            xmin,ymin,xmax,ymax = box.pos
            xc, yc = xmin+(xmax-xmin)/2, ymin+(ymax-ymin)/2
            lab_center_dist[lab].append((xc,yc))
        dist_arr = np.array(lab_center_dist[lab])
        x_traces, y_traces = dist_arr[:,0], dist_arr[:,1]
        tmp[lab].append(( abs(x_traces[0]-x_traces[-1]), abs(y_traces[0]-y_traces[-1])) )

    
    sorted_tmp = sorted(tmp.items(), key=lambda x: x[0])
    sorted_tmp = {k:v for k,v in sorted_tmp}
    xvals, yvals = np.array(list(sorted_tmp.values()))[:,:,:2].transpose(2,0,1)
    fig, ax = plt.subplots(1, figsize=(11,9))
    
    ind = np.arange(len(xvals.flatten()))
    width = 0.35
    
    rects1 = ax.bar(ind - width/2, xvals.flatten(), width, label='X-axis')
    rects2 = ax.bar(ind + width/2, yvals.flatten(), width, label='Y-axis')        
    
    # Add some text for labels, title and custom x-axis tick labels, etc.
    ax.set_ylabel('Length of traces')
    ax.set_title('Length of traces for each label')
    ax.set_xticks(ind)
    ax.set_xticklabels([ f'Label-{x}' for x in tracking_log[vid].keys() ])
    
    ax.legend()
    
    for ix, (xval, yval) in enumerate(zip(xvals.flatten(), yvals.flatten())):
        height = max(xval, yval)
        ratio = yval/xval
        ax.annotate('y/x={:.3f}'.format(ratio),
                    xy=(ind[ix], height),
                    xytext=(0*3, 3),  # use 3 points offset
                    textcoords="offset points",  # in both directions
                    ha='center', va='bottom')
    
    plt.tight_layout()
    plt.show()

def _video_path(video):
    video_home, vid = os.path.split(video)
    vid = os.path.splitext(vid)[0]  # pure-name of video file

    return video_home, vid

def interpolate(p1, p2):
    x1,y1 = p1
    x2,y2 = p2

    intervals = np.arange(x1+1, x2)
    if intervals.size==0:
        return None

    x1 = np.array(x1)[None]; y1 = np.array(y1.pos)
    x2 = np.array(x2)[None]; y2 = np.array(y2.pos)

    interpol_res = []
    for xs in intervals:
        interpolated_pos = tuple((y1 + (xs-x1)*(y2-y1)/(x2-x1)).tolist())  # simple linear interpolation
        interpol_res.append((Box(pos=interpolated_pos, lab=-1), xs))  # dummy tracking label

    return interpol_res

def GetShiftedWindows(mylist, size):
    return [ mylist[x:x+size] for x in range( (len(mylist)-size+1) ) ]


def run_tracker(localizer, video, tracking_log, maxlen, save_dir=None, input_lines=None, start_end=None, analize=False, plot_dist=False):

    video_home, vid = _video_path(video)
    stack_tracking_log(localizer, vid, video, tracking_log)
    if analize:
        analize_XY_traces(vid, tracking_log)
    
    # get lab_center dist
    lab_center_dist = get_XY_traces(vid, tracking_log, plot=plot_dist)

    # select target trace (walker)
    lab, _ = select_target_trace(lab_center_dist)

    hist = tracking_log[vid][lab]

    interpolated_hist = []

    shifted_hist = GetShiftedWindows(hist, size=2)

    for ix, (p1, p2) in enumerate(shifted_hist):
        cur_interpol = interpolate(p1[::-1],p2[::-1])
        interpolated_hist.append(p1)
        if cur_interpol is not None:
            interpolated_hist += cur_interpol

        if ix == len(shifted_hist)-1:
            interpolated_hist.append(p2)  # append last point

    cropped_frames = crop_frames(video, interpolated_hist, save_dir=save_dir, start_end=start_end, input_lines=input_lines, maxlen=maxlen)

    return cropped_frames

def crop_frames(video, hist,
                save_dir, start_end, input_lines, maxlen):

    video_home, vid = _video_path(video)

    if save_dir:
        os.system(f'mkdir -p {save_dir}')

    start_ix, end_ix = (0, len(hist)-1)
    if start_end:
        start_ix, end_ix = start_end
    ix2box = {ix:box for (box,ix) in hist}
    video_path = os.path.join(video_home, vid + '.avi')

    cap = cv2.VideoCapture(video_path)
    ret, frame = cap.read()

    c = 0
    stacked_arr = []
    while True:
        ret, frame = cap.read()
        if not ret:
            break

        if c in ix2box and c in range(start_ix, end_ix):
            xmin,ymin,xmax,ymax = ix2box[c].pos
            xmin,ymin,xmax,ymax = [ int(e) for e in [xmin,ymin,xmax,ymax] ]
            
            crop = frame[ymin:ymax, xmin:xmax]
            stacked_arr.append(crop)

            if input_lines:
                line = vid + '\t' + str(c) + '\t' + ','.join(str(x) for x in ix2box[c].pos) + '\n'
                input_lines[0] += line

        c += 1
        
    stacked_arr = np.array(stacked_arr)

    sample_ixs = np.arange(0, len(stacked_arr))
    if len(stacked_arr) > maxlen:
        sample_ixs = np.linspace(0, len(stacked_arr), maxlen, endpoint=False).astype(np.int32)

    stacked_arr = stacked_arr[sample_ixs]

    if save_dir:
        frame_save_path = os.path.join(save_dir, vid)
        np.save(frame_save_path, stacked_arr)

    return stacked_arr