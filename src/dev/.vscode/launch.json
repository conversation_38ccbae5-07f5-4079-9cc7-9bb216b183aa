{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Python: Data preprocessing (frameArray generation) scripts",
      "type": "python",
      "request": "launch",
      "program": "${workspaceFolder}/generate_dataset.py",
      "console": "integratedTerminal",
      "args": [
        "--data_gen",
        "--mode",
        "preprocess__frame",
        "--input_file",
        "../../preprocess/data/person_detection_and_tracking_results_drop.pkl",
        "--target_file",
        "../../preprocess/data/targets_dataframe.pkl",
        "--darknet_api_home",
        "../../preprocess/darknet",
        "--meta_home",
        "/data/GaitData/MetaData_converted",
        "--fps",
        "24",
        "--device_yolo",
        "0"
      ]
    },
    {
      "name": "Python: Data preprocessing (featureArray generation) scripts",
      "type": "python",
      "request": "launch",
      "program": "${workspaceFolder}/generate_dataset.py",
      "console": "integratedTerminal",
      "args": [
        "--mode",
        "preprocess__feature",
        "--mean_dataset",
        "imagenet",
        "--input_file",
        "../../preprocess/data/person_detection_and_tracking_results_drop-merged.pkl",
        "--target_file",
        "../../preprocess/data/targets_dataframe.pkl",
        "--meta_home",
        "/data/GaitData/MetaData_converted",
        "--backbone",
        "2D-resnet",
        "--model_depth",
        "152",
        "--multi_gpu",
        "--device_ids",
        "0,1,2,3,4,5,6,7",
        "--batch_size",
        "8",
        "--delta",
        "1",
        "--sample_duration",
        "300"
      ]
    },
    {
      "name": "Python: Test script (3D)",
      "type": "python",
      "request": "launch",
      "program": "${workspaceFolder}/main.py",
      "console": "integratedTerminal",
      "env": {
        "CUDA_VISIBLE_DEVICES": "1,2,3,4,5,6,7,8"
      },
      "args": [
        "--backbone",
        "r2plus1d_18",
        "--model_depth",
        "18",
        "--pretrained_path",
        "",
        "--data_root",
        "/data/GaitData/RawFrames",
        "--multi_gpu",
        "--with_segmentation",
        "--device_ids",
        "0,1,2,3,4,5,6,7",
        "--batch_size",
        "32",
        "--n_threads",
        "8",
        "--mode",
        "test",
        "--pretrain_epoch",
        "160",
        "--test_epoch",
        "160",
        "--model_arch",
        "AGNet"
      ]
    },
    {
      "name": "Python: Train script (3D)",
      "type": "python",
      "request": "launch",
      "program": "${workspaceFolder}/main.py",
      "console": "integratedTerminal",
      "env": {
        "CUDA_VISIBLE_DEVICES": "1,2,3,4,5,6,7,8"
      },
      "args": [
        "--backbone",
        "r2plus1d_18",
        "--model_depth",
        "18",
        "--pretrained_path",
        "",
        "--data_root",
        "/data/GaitData/RawFrames",
        "--multi_gpu",
        "--with_segmentation",
        "--device_ids",
        "0,1,2,3,4,5,6,7",
        "--batch_size",
        "32",
        "--learning_rate",
        "1e-4",
        "--n_threads",
        "8",
        "--mode",
        "train",
        "--pretrain_epoch",
        "160",
        "--model_arch",
        "AGNet",
        "--n_iter",
        "101",
        "--CV",
        "5"
      ]
    },
    {
      "name": "Python: Run REST API server (3D)",
      "type": "python",
      "request": "launch",
      "program": "${workspaceFolder}/main.py",
      "console": "integratedTerminal",
      "args": [
        "--backbone",
        "r2plus1d_18",
        "--model_depth",
        "18",
        "--pretrained_path",
        "",
        "--with_segmentation",
        "--multi_gpu",
        "--device_ids",
        "0",
        "--mode",
        "demo",
        "--pretrain_epoch",
        "160",
        "--test_epoch",
        "160",
        "--model_arch",
        "AGNet",
        "--interval_sel",
        "",
        "--port",
        "5001"
      ]
    },
    {
      "name": "Python: Train script (2D)",
      "type": "python",
      "request": "launch",
      "program": "${workspaceFolder}/main.py",
      "console": "integratedTerminal",
      "env": {
        "CUDA_VISIBLE_DEVICES": "8,9"
      },
      "args": [
        "--backbone",
        "2D-resnet",
        "--model_depth",
        "50",
        "--mean_dataset",
        "imagenet",
        "--multi_gpu",
        "--device_ids",
        "0,1",
        "--batch_size",
        "300",
        "--learning_rate",
        "1e-2",
        "--n_threads",
        "16",
        "--mode",
        "train",
        "--model_arch",
        "DeepFFT",
        "--n_iter",
        "21",
        "--CV",
        "5",
        "--drop_rate",
        "0.3",
        "--delta",
        "2",
        "--sample_duration",
        "150",
        "--load_pretrained"
      ]
    },
    {
      "name": "Python: Test script (2D)",
      "type": "python",
      "request": "launch",
      "program": "${workspaceFolder}/main.py",
      "console": "integratedTerminal",
      "args": [
        "--backbone",
        "2D-resnet",
        "--model_depth",
        "50",
        "--mean_dataset",
        "imagenet",
        "--multi_gpu",
        "--device_ids",
        "0,1,2",
        "--batch_size",
        "300",
        "--n_threads",
        "16",
        "--mode",
        "test",
        "--test_epoch",
        "0",
        "--model_arch",
        "DeepFFT",
        "--delta",
        "2",
        "--sample_duration",
        "150",
        "--load_pretrained"
      ]
    },
    {
      "name": "Python: Current File",
      "type": "python",
      "request": "launch",
      "program": "${file}",
      "console": "integratedTerminal"
    }
  ]
}
