{
    // See https://go.microsoft.com/fwlink/?LinkId=733558
    // for the documentation about the tasks.json format
    "version": "2.0.0",
    "command": "bash",
    "args": ["-c"],
    "tasks": [{
            "label": "[Experiment scripts] Other models; Naive(or baseline) and SPP",
            "type": "shell",
            "args": ["${workspaceFolder}/scripts/training/OtherModels-Exp.sh"],
        },
        {
            "label": "[Experiment scripts] GroupsNbrs effect in HPP",
            "type": "shell",
            "args": ["${workspaceFolder}/scripts/training/HPP_GroupsNbrs-Exp.sh"],
        },
        {
            "label": "[Experiment scripts] MergeType effect in HPP",
            "type": "shell",
            "args": ["${workspaceFolder}/scripts/training/HPP_MergeType-Exp.sh"],
        },
    ]
}