{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "sys.path.append('/home/<USER>/gaitanalysis/src/dev/')\n", "os.environ['CUDA_VISIBLE_DEVICES'] = \"1\"\n", "import json\n", "import collections\n", "with open('../commandline_args.txt', 'r') as f:\n", "    dd = json.load(f)\n", "\n", "import easydict \n", "opt = easydict.EasyDict(dd)\n", "opt.input_file = '../../../preprocess/data/person_detection_and_tracking_results_drop.pkl'\n", "opt.target_file = '../../../preprocess/data/targets_dataframe.pkl'\n", "opt.sample_size = 128\n", "opt.sample_duration = 128\n", "opt.img_size = 128\n", "opt.delta = 1\n", "opt.batch_size = 8"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "import torchvision.transforms as TF\n", "from torch.utils.data import DataLoader\n", "import numpy as np\n", "from datasets import gaitregression\n", "from utils import target_columns, visualization\n", "import random\n", "import matplotlib.pyplot as plt\n", "from sklearn.preprocessing import QuantileTransformer, MinMaxScaler\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics.regression import r2_score, mean_absolute_error, mean_squared_log_error"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["target_columns = target_columns.get_target_columns(opt)\n", "\n", "opt.mean = 0.5\n", "opt.std = 0.5"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["spatial_transform = TF.Compose([\n", "            <PERSON><PERSON><PERSON>(),\n", "            TF.Normalize((0.5, 0.5, 0.5), (0.5, 0.5, 0.5)),\n", "        ])\n", "\n", "temporal_transform = None\n", "\n", "# # target transform\n", "# from sklearn.preprocessing import FunctionTransformer\n", "\n", "# target_transform = FunctionTransformer(lambda x: x, validate=False)\n", "\n", "# target_transform = MinMaxScaler()\n", "target_transform = QuantileTransformer(\n", "    random_state=0, output_distribution=\"normal\"\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Prepare Dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# prepare dataset  (train/test split)\n", "data = gaitregression.prepare_dataset(\n", "    input_file=opt.input_file,\n", "    target_file=opt.target_file,\n", "    target_columns=target_columns,\n", "    chunk_parts=opt.chunk_parts,\n", "    target_transform=target_transform,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## split train/valid\n", "train_vids, valid_vids = train_test_split(np.array(list(set(data[\"train_X\"].vids))), test_size=0.2, random_state=42)\n", "\n", "X_train, y_train = (gaitregression.filter_input_df_with_vids(data[\"train_X\"], train_vids), \n", "                    gaitregression.filter_target_df_with_vids(data[\"train_y\"], train_vids))\n", "X_valid, y_valid = (gaitregression.filter_input_df_with_vids(data[\"train_X\"], valid_vids),\n", "                    gaitregression.filter_target_df_with_vids(data[\"train_y\"], valid_vids))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if opt.with_segmentation:\n", "    ds_class = gaitregression.GAITSegRegDataset\n", "else:\n", "    ds_class = gaitregression.GAITDataset\n", "\n", "\n", "train_ds = ds_class(\n", "    X=X_train, y=y_train, opt=opt, phase='train', spatial_transform=spatial_transform,\n", "                                        temporal_transform=temporal_transform\n", ")\n", "valid_ds = ds_class(\n", "    X=X_valid, y=y_valid, opt=opt, phase='valid',spatial_transform=spatial_transform,\n", "                                    temporal_transform=temporal_transform,\n", ")\n", "test_ds = ds_class(\n", "    X=data[\"test_X\"], y=data[\"test_y\"], opt=opt, phase='test',spatial_transform=spatial_transform,\n", "                                    temporal_transform=temporal_transform,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Dataloaders\n", "train_loader = DataLoader(train_ds, batch_size=opt.batch_size, shuffle=True, num_workers=opt.n_threads, drop_last=True)\n", "valid_loader = DataLoader(valid_ds, batch_size=opt.batch_size, shuffle=False, num_workers=opt.n_threads, drop_last=True)\n", "test_loader = DataLoader(test_ds, batch_size=opt.batch_size, shuffle=False, num_workers=opt.n_threads, drop_last=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Define GAN"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class Generator(nn.Mo<PERSON>le):\n", "    \n", "    def __init__(self):\n", "        super(Generator, self).__init__()\n", "        self.linear = nn.Linear(in_features=100+15, out_features=768)\n", "        \n", "        self.upconvs = nn.Sequential(\n", "            nn.ConvTranspose3d(in_channels=768, out_channels=384, \n", "                               kernel_size=5, stride=2, padding=0, \n", "                               bias=False),\n", "            nn.<PERSON>chNorm3d(num_features=384),\n", "            nn.ReLU(inplace=True),\n", "\n", "            nn.ConvTranspose3d(in_channels=384, out_channels=256, \n", "                               kernel_size=5, stride=2, padding=0, \n", "                               bias=False),\n", "            nn.BatchNorm3d(num_features=256),\n", "            nn.<PERSON><PERSON><PERSON>(True),\n", "            \n", "            nn.ConvTranspose3d(in_channels=256, out_channels=192, \n", "                               kernel_size=5, stride=2, padding=0, \n", "                               bias=False),\n", "            nn.BatchNorm3d(num_features=192),\n", "            nn.<PERSON><PERSON><PERSON>(True),\n", "\n", "            nn.ConvTranspose3d(in_channels=192, out_channels=64, \n", "                               kernel_size=5, stride=2, padding=0, \n", "                               bias=False),\n", "            nn.BatchNorm3d(num_features=64),\n", "            nn.<PERSON><PERSON><PERSON>(True),\n", "            \n", "            nn.ConvTranspose3d(in_channels=64, out_channels=3, \n", "                               kernel_size=8, stride=2, padding=0, \n", "                               bias=False),            \n", "            nn.<PERSON>())\n", "        \n", "    def forward(self, z, c):\n", "        z = torch.cat([z,c], dim=1)\n", "        z = self.linear(z).view(z.size(0), -1, 1, 1, 1)\n", "        \n", "        return self.upconvs(z)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class Discriminator(nn.Module):\n", "    \n", "    def __init__(self, num_classes=15):\n", "        super(Discriminator, self).__init__()\n", "\n", "        self.main = nn.Sequential(\n", "            # Convolution 1\n", "            nn.Conv3d(3, 16, 3, 2, 1, bias=False),\n", "            nn.LeakyReLU(0.2, inplace=True),\n", "            nn.Dropout(0.5, inplace=False),\n", "\n", "            # Convolution 2\n", "            nn.Conv3d(16, 32, 3, 1, 0, bias=False),\n", "            nn.<PERSON><PERSON><PERSON><PERSON>3d(32),\n", "            nn.LeakyReLU(0.2, inplace=True),\n", "            nn.Dropout(0.5, inplace=False),\n", "            \n", "            # Convolution 3\n", "            nn.Conv3d(32, 64, 3, 2, 1, bias=False),\n", "            nn.<PERSON><PERSON><PERSON><PERSON>3d(64),\n", "            nn.LeakyReLU(0.2, inplace=True),\n", "            nn.Dropout(0.5, inplace=False),\n", "            \n", "            # Convolution 4\n", "            nn.Conv3d(64, 128, 3, 1, 0, bias=False),\n", "            nn.<PERSON><PERSON><PERSON><PERSON>3d(128),\n", "            nn.LeakyReLU(0.2, inplace=True),\n", "            nn.Dropout(0.5, inplace=False),\n", "            \n", "            # Convolution 5\n", "            nn.Conv3d(128, 256, 3, 2, 1, bias=False),\n", "            nn.<PERSON><PERSON><PERSON><PERSON>3d(256),\n", "            nn.LeakyReLU(0.2, inplace=True),\n", "            nn.Dropout(0.5, inplace=False),\n", "            \n", "            # Convolution 6\n", "            nn.Conv3d(256, 512, 3, 1, 0, bias=False),\n", "            nn.<PERSON>ch<PERSON>orm3d(512),\n", "            nn.LeakyReLU(0.2, inplace=True),\n", "            nn.Dropout(0.5, inplace=False))\n", "            \n", "        # discriminator fc\n", "        self.fc_dis = nn.<PERSON>ar(13*13*13*512, 1)\n", "\n", "        # aux-classifier fc\n", "        self.fc_aux = nn.Linear(13*13*13*512, num_classes)\n", "                \n", "    def forward(self, x):\n", "        x = self.main(x)\n", "        x = x.view(x.size(0), -1)\n", "        dis_out = torch.sigmoid(self.fc_dis(x))\n", "        aux_out = self.fc_aux(x)\n", "        \n", "        return dis_out, aux_out"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["G = Generator().cuda()\n", "D = Discriminator().cuda()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def weights_init(m):\n", "    classname = m.__class__.__name__\n", "    if classname.find('Conv') != -1:\n", "        m.weight.data.normal_(0.0, 0.02)\n", "    elif classname.find('BatchN<PERSON>') != -1:\n", "        m.weight.data.normal_(1.0, 0.02)\n", "        m.bias.data.fill_(0)\n", "\n", "G.apply(weights_init)\n", "<PERSON>.apply(weights_init)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dis_criterion = nn.BCELoss()\n", "aux_criterion = nn.MSELoss()\n", "rec_criterion = nn.MSELoss()\n", "\n", "G_optimizer = optim.Adam(G.parameters(), lr=0.0002, betas=(0.5, 0.999))\n", "D_optimizer = optim.<PERSON>(D.parameters(), lr=0.0002, betas=(0.5, 0.999))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# training loop"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["plotter = visualization.VisdomPlotter(env_name='ACGAN_with_reconstructionLoss')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def compute_scores(y_pred, y_true, target_transform, scores=['r2', 'mae']):\n", "    score_dict = {\n", "        'r2': r2_score,\n", "        'mae': mean_absolute_error,\n", "        'mse': mean_squared_log_error\n", "    }\n", "    \n", "    res = []\n", "    for name in scores:\n", "        score_func = score_dict.get(name)\n", "        scores = score_func(\n", "            target_transform.inverse_transform(y_true),\n", "            target_transform.inverse_transform(y_pred),\n", "            multioutput='raw_values',\n", "        )\n", "        \n", "        res.append(scores)\n", "    \n", "    return res"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print('Start training...')\n", "\n", "label_real = torch.ones([opt.batch_size, 1]).cuda()\n", "label_fake = torch.zeros([opt.batch_size, 1]).cuda()\n", "\n", "# D losses\n", "# 0. total\n", "running_D_loss_tot = 0.0\n", "# 1. real\n", "running_D_loss_real = 0.0\n", "running_D_aux_loss_real = 0.0\n", "# 2. fake\n", "running_D_loss_fake = 0.0\n", "running_D_aux_loss_fake = 0.0\n", "\n", "# G losses\n", "# 0. total\n", "running_G_loss_tot = 0.0\n", "# 1. fake\n", "running_G_loss_fake = 0.0\n", "running_G_aux_loss_fake = 0.0\n", "running_G_rec_loss = 0.0   # reconstruction loss\n", "\n", "# scores\n", "# 1. real\n", "running_r2_real = [ 0.0 for _ in range(len(target_columns)) ]\n", "running_mae_real = [ 0.0 for _ in range(len(target_columns)) ]\n", "\n", "\n", "# 2. fake\n", "running_r2_fake = [ 0.0 for _ in range(len(target_columns)) ]\n", "running_mae_fake = [ 0.0 for _ in range(len(target_columns)) ]\n", "\n", "\n", "interval = 10\n", "\n", "global_step = 0\n", "\n", "for epoch in range(opt.n_iter):\n", "    # training loop\n", "    for step_ix, (inputs, targets, vids) in enumerate(train_loader):\n", "        ### Train D ###\n", "        D.zero_grad()\n", "        \n", "        # for real data...\n", "        real_data = inputs.cuda()\n", "        dis_out_real, aux_out_real = D(real_data)\n", "        D_loss_real = dis_criterion(dis_out_real, label_real)\n", "        D_aux_loss_real = aux_criterion(aux_out_real, targets.cuda())\n", "        D_loss_realpart = D_loss_real + D_aux_loss_real\n", "        D_loss_realpart.backward()\n", "        \n", "        # latent vector z\n", "        z = torch.randn((opt.batch_size, 100))\n", "        \n", "        # for fake data from G...\n", "        fake_data = G(z.cuda(), targets.cuda())\n", "        dis_out_fake, aux_out_fake = D(fake_data.detach())\n", "        D_loss_fake = dis_criterion(dis_out_fake, label_fake)\n", "        D_aux_loss_fake = aux_criterion(aux_out_fake, targets.cuda())\n", "        D_loss_fakepart = D_loss_fake + D_aux_loss_fake\n", "        D_loss_fakepart.backward()\n", "        \n", "        # D_loss : Discriminator's loss\n", "        D_loss = D_loss_realpart + D_loss_fakepart\n", "        \n", "        # Update D\n", "        D_optimizer.step()\n", "\n", "        # 0. total\n", "        running_D_loss_tot += D_loss.item()\n", "        # 1. real\n", "        running_D_loss_real += D_loss_real.item()\n", "        running_D_aux_loss_real += D_aux_loss_real.item()\n", "        # 2. fake\n", "        running_D_loss_fake += D_loss_fake.item()\n", "        running_D_aux_loss_fake += D_aux_loss_fake.item()\n", "                        \n", "                \n", "        ### Train G ###\n", "        G.zero_grad()\n", "        dis_out_fake, aux_out_fake = D(fake_data)\n", "        \n", "        # loss of G for deceiving D\n", "        G_loss_fake = dis_criterion(dis_out_fake, label_real)\n", "        \n", "        G_aux_loss_fake = aux_criterion(aux_out_fake, targets.cuda())\n", "        G_rec_loss = rec_criterion(fake_data, real_data)  # reconstruction loss \n", "        G_loss = G_loss_fake + G_aux_loss_fake + G_rec_loss\n", "        \n", "        # Update G\n", "        G_loss.backward()\n", "        G_optimizer.step()\n", "        \n", "        # 0. total\n", "        running_G_loss_tot += G_loss.item()\n", "        # 1. fake\n", "        running_G_loss_fake += G_loss_fake.item()\n", "        running_G_aux_loss_fake += G_aux_loss_fake.item()\n", "        running_G_rec_loss += G_rec_loss.item()\n", "        \n", "        # compute scores for real data\n", "        r2_real, mae_real = compute_scores(aux_out_real.detach().cpu().numpy(),\n", "                                           targets.numpy(), target_transform,\n", "                                           scores=['r2', 'mae'])\n", "        # scores (real)\n", "        for n in range(len(target_columns)):\n", "            running_r2_real[n] += r2_real[n]\n", "        for n in range(len(target_columns)):\n", "            running_mae_real[n] += mae_real[n]\n", "\n", "        \n", "        # compute scores for fake data\n", "        r2_fake, mae_fake = compute_scores(aux_out_fake.detach().cpu().numpy(),\n", "                                           targets.numpy(), target_transform,\n", "                                           scores=['r2', 'mae'])\n", "        \n", "        # scores (real)\n", "        for n in range(len(target_columns)):\n", "            running_r2_fake[n] += r2_fake[n]\n", "        for n in range(len(target_columns)):\n", "            running_mae_fake[n] += mae_fake[n]\n", "            \n", "        global_step += 1\n", "        \n", "        if step_ix % interval == 0:\n", "            for win,e in zip(['Fake', 'Real'],[fake_data, real_data]):\n", "                e = e.detach().cpu().numpy()[0][:,::2]\n", "                e = np.clip(e.transpose(1,2,3,0)*opt.std + opt.mean, 0.0, 1.0)\n", "                plotter.viz.images(\n", "                        e.transpose(0,3,1,2),\n", "                        win=win)\n", "\n", "            print('Epoch : ', epoch, 'Step :', step_ix, \n", "                  'D_loss_tot : ', running_D_loss_tot/interval,\n", "                  'D_loss_real : ', running_D_loss_real/interval,\n", "                  'D_aux_loss_real : ', running_D_aux_loss_real/interval,\n", "                  'D_loss_fake : ', running_D_loss_fake/interval,\n", "                  'D_aux_loss_fake : ', running_D_aux_loss_fake/interval,\n", "                  'G_loss_tot : ', running_G_loss_tot/interval,\n", "                  'G_loss_fake : ', running_G_loss_fake/interval,\n", "                  'G_aux_loss_fake : ', running_G_aux_loss_fake/interval,\n", "                  'G_rec_loss : ', running_G_rec_loss/interval,\n", "                  'r2_real : ', np.mean(running_r2_real)/interval,\n", "                  'mae_real : ', np.mean(running_mae_real)/interval,\n", "                  'r2_fake : ', np.mean(running_r2_fake)/interval,\n", "                  'mae_fake : ', np.mean(running_mae_fake)/interval                 \n", "                 )\n", "            \n", "                        \n", "            plotter.plot('D_loss_tot', 'train', 'D_loss_tot__trace',\n", "             global_step, running_D_loss_tot/interval)\n", "            plotter.plot('D_loss_real', 'train', 'D_loss_real__trace',\n", "             global_step, running_D_loss_real/interval)\n", "            plotter.plot('D_aux_loss_real', 'train', 'D_aux_loss_real__trace',\n", "             global_step, running_D_aux_loss_real/interval)\n", "            plotter.plot('D_loss_fake', 'train', 'D_loss_fake__trace',\n", "             global_step, running_D_loss_fake/interval)\n", "            plotter.plot('D_aux_loss_fake', 'train', 'D_aux_loss_fake__trace',\n", "             global_step, running_D_aux_loss_fake/interval)\n", "            \n", "            plotter.plot('G_loss_tot', 'train', 'G_loss_tot__trace',\n", "             global_step, running_G_loss_tot/interval)\n", "            plotter.plot('G_loss_fake', 'train', 'G_loss_fake__trace',\n", "             global_step, running_G_loss_fake/interval)\n", "            plotter.plot('G_aux_loss_fake', 'train', 'G_aux_loss_fake__trace',\n", "             global_step, running_G_aux_loss_fake/interval)\n", "            plotter.plot('G_rec_loss', 'train', 'G_rec_loss__trace',\n", "             global_step, running_G_rec_loss/interval)\n", "            plotter.plot('G_aux_loss_fake', 'train', 'G_aux_loss_fake__trace',\n", "             global_step, running_G_aux_loss_fake/interval)\n", "            \n", "            plotter.plot('r2_real', 'train', 'r2_real_avg__trace',\n", "             global_step, np.mean(running_r2_real)/interval)\n", "            plotter.plot('mae_real', 'train', 'mae_real_avg__trace',\n", "             global_step, np.mean(running_mae_real)/interval)\n", "            plotter.plot('r2_fake', 'train', 'r2_fake_avg__trace',\n", "             global_step, np.mean(running_r2_fake)/interval)\n", "            plotter.plot('mae_fake', 'train', 'mae_fake_avg__trace',\n", "             global_step, np.mean(running_mae_fake)/interval)\n", "                        \n", "                        \n", "            # D losses\n", "            # 0. total\n", "            running_D_loss_tot = 0.0\n", "            # 1. real\n", "            running_D_loss_real = 0.0\n", "            running_D_aux_loss_real = 0.0\n", "            # 2. fake\n", "            running_D_loss_fake = 0.0\n", "            running_D_aux_loss_fake = 0.0\n", "\n", "            # G losses\n", "            # 0. total\n", "            running_G_loss_tot = 0.0\n", "            # 1. fake\n", "            running_G_loss_fake = 0.0\n", "            running_G_aux_loss_fake = 0.0\n", "            running_G_rec_loss = 0.0   # reconstruction loss\n", "            \n", "            # scores\n", "            # 1. real\n", "            running_r2_real = [ 0.0 for _ in range(len(target_columns)) ]\n", "            running_mae_real = [ 0.0 for _ in range(len(target_columns)) ]\n", "\n", "\n", "            # 2. fake\n", "            running_r2_fake = [ 0.0 for _ in range(len(target_columns)) ]\n", "            running_mae_fake = [ 0.0 for _ in range(len(target_columns)) ]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.8"}}, "nbformat": 4, "nbformat_minor": 2}