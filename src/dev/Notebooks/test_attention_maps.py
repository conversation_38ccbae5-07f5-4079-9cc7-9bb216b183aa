from functools import partial
import sys, os
sys.path.append(os.path.dirname(os.getcwd())) 

from utils.generate_model import init_state, load_trained_ckpt
from utils.transforms import (
    Compose, ToTensor, MultiScaleRandomCrop, MultiScaleCornerCrop, Normalize,
    TemporalRandomCrop, TemporalCenterCrop, LoopPadding)
from utils.target_columns import get_target_columns
from utils.mean import get_mean, get_std
from utils.preprocessing import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    COPAnalyizer,
    HumanScaleAnalyizer,
    Worker,
)
import datasets.gaitregression
from collections import defaultdict
from tqdm import tqdm
import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import sklearn
import visdom
import torch.nn.functional as F
import torchvision.transforms as TF
import json
from types import SimpleNamespace
import re
import cv2
from PIL import Image
import copy
from torch.utils.data import Dataset, DataLoader
import math
import matplotlib.pyplot as plt
import seaborn as sns
import statsmodels.api as sm
import collections
from dateutil.parser import parse
import datetime
from IPython.core.debugger import set_trace
from opts import parse_opts
import imgviz

# load config file for demo
opt = parse_opts()
opt.input_file = "/data/GAITRite-dataset/v2/person_detection_and_tracking_results.csv"
opt.target_file = "/data/GAITRite-dataset/v2/gaitrite_full_dataset.xlsx"
opt.backbone = "r2plus1d_18"
opt.model_depth = 18
opt.pretrained_path = ""
opt.data_root = "/data/GAITRite-dataset/v2/frame/"
opt.ckpt_dir = "../ckpt_repos"
opt.multi_gpu = True
opt.device_ids = "0,1,2,3,4,5,6,7"
opt.model_arch = "AGNet-pretrain"
opt.bop_refinement = True
opt.with_segmentation = True
opt.training_from_zero = True
opt.sample_size = opt.img_size
if opt.model_arch == "R2Plus1D-pretrain":
    opt.test_epoch = 100
elif opt.model_arch == "AGNet-pretrain":
    opt.test_epoch = 100
    if opt.bop_refinement:
        opt.test_epoch = 100

class_names = ['_background_', 'Patient']

# attention indicator
opt.bop_str = 'BOP' if opt.bop_refinement else 'NonBOP'
opt.group_str = f"G{opt.n_groups}" if opt.n_groups > 0 else ''
opt.arch = "{}-{}".format(opt.backbone, opt.model_depth)

target_columns = get_target_columns(opt)

# define regression model
net, criterion1, criterion2, optimizer, scheduler = init_state(opt)
net = load_trained_ckpt(opt, net)
net.eval()

opt.mean = get_mean(opt.norm_value, dataset=opt.mean_dataset)
opt.std = get_std(opt.norm_value, dataset=opt.mean_dataset)

if opt.no_mean_norm and not opt.std_norm:
    norm_method = Normalize([0, 0, 0], [1, 1, 1])
else:
    norm_method = Normalize(opt.mean, opt.std)

if opt.train_crop == "random":
    crop_method = MultiScaleRandomCrop(opt.scales, opt.sample_size)
elif opt.train_crop == "corner":
    crop_method = MultiScaleCornerCrop(opt.scales, opt.sample_size)
elif opt.train_crop == "center":
    crop_method = MultiScaleCornerCrop(
        opt.scales, opt.sample_size, crop_positions=["c"]
    )

spatial_transform = {
    "train": Compose(
        [
            TF.RandomRotation(degrees=(0, 0)),
            TF.RandomResizedCrop(size=opt.sample_size,
                                    scale=(opt.sample_size/opt.img_size, 1.0)),
            ToTensor(opt.norm_value),
            norm_method,
        ]
    ),
    "test": Compose(
        [
            TF.CenterCrop(opt.sample_size),
            ToTensor(opt.norm_value),
            norm_method,
        ]
    ),
}

temporal_transform = {
    "train": None,  # TemporalRandomCrop(opt.sample_duration),
    "test": None,  # TemporalCenterCrop(opt.sample_duration),
}

from sklearn.preprocessing import MinMaxScaler, FunctionTransformer, StandardScaler, QuantileTransformer
from sklearn.pipeline import Pipeline

target_transform = StandardScaler()

model_indicator = '_'.join(filter(lambda x: x != '', [opt.bop_str,
                                                        opt.model_arch,
                                                        opt.merge_type,
                                                        opt.arch,
                                                        opt.group_str]))

# prepare dataset  (train/test split)
data = datasets.gaitregression.prepare_dataset(
    input_file=opt.input_file,
    target_file=opt.target_file,
    target_columns=target_columns,
    chunk_parts=opt.chunk_parts,
    target_transform=target_transform,
    legacy_mode=opt.legacy_mode,
    clinical_test_file=opt.clinical_test_file,
)

outlier_vids_train = [
    '1537141_test_1_trial_1',
    '2074951_test_1_trial_1',
    '2172065_test_2_trial_5',
    '2201260_test_1_trial_2',
    '2307352_test_2_trial_4'

]

outlier_vids_test = [
    '1926319_test_1_trial_0',
    '2074951_test_1_trial_2',
    '7133167_test_2_trial_7'
]

outlier_vids = outlier_vids_test
outlier_pids = [ datasets.gaitregression.vid2pid(vid) for vid in outlier_vids ]
outlier_mask_for_X = data["test_X"].vids.isin(outlier_vids)
outlier_mask_for_y = data["test_y"].index.isin(outlier_pids)

ds_class = datasets.gaitregression.GAITDataset

test_ds = ds_class(X=data["test_X"].loc[outlier_mask_for_X], y=data["test_y"].loc[outlier_mask_for_y], opt=opt, phase='test', spatial_transform=spatial_transform['test'])

def denormalize_img(img, mean=torch.tensor(opt.mean).view(-1, 1, 1), std=torch.tensor(opt.std).view(-1, 1, 1), to_gray=True):
    img = std * img + mean
    img = torch.clamp(img, 0, 1).permute(1,2,0)
    img = cv2.normalize(img.detach().cpu().numpy(), None, 0, 255,
                        cv2.NORM_MINMAX, dtype=cv2.CV_8UC1)
    img = cv2.cvtColor(img, cv2.COLOR_RGB2BGR)
    if to_gray:
        gray  = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        img = np.zeros_like(img)
        img[..., 0] = gray
        img[..., 1] = gray
        img[..., 2] = gray

    return img

def get_featuremaps(net, input_imgs, layer_names):
    feature_maps = {}
    counter = 0

    def hook_fn(m, i, o, name):
        feature_maps[name] = o 

    def get_all_layers(net):
        nonlocal counter
        for name, layer in net._modules.items():
            #If it is a sequential, don't register a hook on it
            # but recursively register hook on all it's module children
            if len(layer._modules) > 0:
                get_all_layers(layer)
            else:
                # it's a non sequential. Register a hook
                if isinstance(layer, nn.Conv3d):
                    layer.register_forward_hook(partial(hook_fn, name=f'conv{counter}'))
                    counter += 1

    get_all_layers(net)

    _ = net(input_imgs)
    # filter by layer_names
    activation_list = [ v for k,v in feature_maps.items() if k in layer_names ]
    return activation_list

def save_activation_maps(input_imgs, activations_pred, mask_gt, vid, save_path="/data/GAITRite-dataset/v2/attention_results"):
    save_path = os.path.join(save_path, opt.bop_str + "_" + opt.model_arch)
    mask_gt = mask_gt[0].detach().cpu().numpy()
    n_scales = len(activations_pred)

    subdir_path = os.path.join(save_path, vid)
    os.makedirs(subdir_path, exist_ok=True)
    mask_gt_dir = os.path.join(subdir_path, 'mask_gt')
    os.makedirs(mask_gt_dir, exist_ok=True)

    for j in range(len(mask_gt)):
        input_img_j, mask_gt_j = input_imgs[:, j], mask_gt[j]
        input_img_j = denormalize_img(input_img_j)

        mask_gt_j = cv2.resize(mask_gt_j, (input_img_j.shape[1], input_img_j.shape[1]))

        masks = (mask_gt_j == 1).astype(int)
        #maskviz = imgviz.instances2rgb(input_img_j, masks=masks, labels=labels, line_width=1)
        maskviz = imgviz.label2rgb(masks, image=input_img_j)
        maskviz = cv2.cvtColor(maskviz, cv2.COLOR_RGB2BGR)
        cv2.imwrite(os.path.join(mask_gt_dir, f'thumb{j:04d}.jpg'), maskviz)

    steps = [1, 2, 4, 8]

    for i in range(n_scales):
        activation_pred_avg = activations_pred[i][0].max(0)[0].detach().cpu().numpy() # average across channel dimension

        for j in range(len(activation_pred_avg)):
            input_img_j, attn_pred_j = input_imgs[:, j * steps[i]], activation_pred_avg[j]
            input_img_j = denormalize_img(input_img_j)

            frame_ix = j * steps[i]

            # Normalize the values between 0 and 1
            attn_pred_j = (attn_pred_j - np.min(attn_pred_j)) / (np.max(attn_pred_j) - np.min(attn_pred_j))

            attn_pred_j = cv2.resize(attn_pred_j, (input_img_j.shape[1], input_img_j.shape[1]))
            attn_pred_j = cv2.applyColorMap(np.uint8(255 * attn_pred_j), cv2.COLORMAP_JET)

            overlay_image = cv2.addWeighted(input_img_j, 0.4, attn_pred_j, 0.6, 0)
            attn_dir = os.path.join(subdir_path, 'activation_pred', f's{i}')
            os.makedirs(attn_dir, exist_ok=True)
            cv2.imwrite(os.path.join(attn_dir, f'thumb{frame_ix:04d}.jpg'), overlay_image)

from tqdm import tqdm
for sample in tqdm(test_ds):
    input_imgs, mask_imgs, targets, vid, valid_lengths = sample
    if "AGNet" in opt.model_arch:
        activation_list = net.module.agnet.get_activations(input_imgs.unsqueeze(0).cuda())
    else:
        #activation_list = get_featuremaps(net.module.backbone, input_imgs.unsqueeze(0).cuda(), layer_names=['conv9', 'conv18', 'conv27', 'conv36'])
        activation_list = net.module.get_activations(input_imgs.unsqueeze(0).cuda())
    
    save_activation_maps(input_imgs, activation_list, mask_imgs, vid)