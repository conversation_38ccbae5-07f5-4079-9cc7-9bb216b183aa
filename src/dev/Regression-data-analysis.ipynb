{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%matplotlib inline\n", "\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import json\n", "from IPython.core.debugger import set_trace"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torchvision\n", "import torch\n", "from torch import nn\n", "import cv2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model = torch.load('./resnext-101-kinetics-ucf101_split1.pth')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from params import *\n", "path = '/data/GaitData/CroppedFrameArrays/7026976_test_0_trial_0.npy'\n", "frames = np.load(path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["frame = frames[10]\n", "model_conv = torchvision.models.resnet101(pretrained=True)\n", "modules = nn.Sequential(*list(model_conv.children())[:-2])\n", "res = modules(torch.from_numpy(cv2.resize(frame, (128,384)).transpose(2,0,1)[None,:,:,:].astype(np.float32)/255))\n", "\n", "plt.imshow(cv2.resize(frame, (128,384))); plt.show()\n", "plt.imshow(cv2.resize(res[0].mean(0).detach().numpy(),(128,384) )); plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": false}, "outputs": [], "source": ["import cv2\n", "import os\n", "for i in range(len(os.listdir('./feats_viz'))): \n", "    plt.imshow(np.mean([cv2.cvtColor(cv2.imread(os.path.join('feats_viz',f)),cv2.COLOR_BGR2RGB) for f in os.listdir('./feats_viz')[:i+1]],0)/255); \n", "    plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from collections import defaultdict\n", "traj_data = json.load(open('./UDysRS_UPDRS_Export/LA_split_all_export.txt'))\n", "traj_data_dict = defaultdict(list)\n", "\n", "maxlen = 0\n", "for key, val in traj_data.items():\n", "    body_parts = sorted(val['position'].keys())\n", "    body_parts = list(x for x in body_parts if '_' in x)\n", "    \n", "    \n", "    body_dict = defaultdict(list)\n", "    for b in body_parts:\n", "        pure_name = b.split('_')[0]\n", "        body_dict[pure_name] += val['position'][b]\n", "    \n", "    position_vec = np.array(list(body_dict.values())).flatten()\n", "    if len(position_vec) > maxlen:\n", "        maxlen = len(position_vec)\n", "        \n", "    traj_data_dict['position'].append(  position_vec  )\n", "\n", "for i in range(len(traj_data_dict['position'])):\n", "    pos = traj_data_dict['position'][i].tolist()\n", "    traj_data_dict['position'][i] = np.array(pos + [ 0.0 ] * (maxlen-len(traj_data_dict['position'][i])))\n", "\n", "traj_df = pd.DataFrame(data=traj_data_dict, index=traj_data.keys())\n", "traj_df.index\n", "\n", "score_data = json.load(open('./UDysRS_UPDRS_Export/UPDRS.txt'))['Total']\n", "score_data_dict = {}\n", "score_data_dict['score'] = list(score_data.values())\n", "\n", "score_df = pd.DataFrame(data=score_data_dict, index=score_data.keys())\n", "len(score_df)\n", "\n", "traj_df = traj_df.loc[score_df.index].dropna()\n", "score_df = score_df.loc[traj_df.index].dropna()\n", "\n", "X =np.array(traj_df.position.values.tolist())\n", "y = score_df.score.values\n", "\n", "from sklearn.linear_model import LinearRegression\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.ensemble import RandomForestRegressor\n", "\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.33, random_state=42)\n", "lm = RandomForestRegressor(n_jobs=-1)\n", "lm.fit(X_train, y_train)\n", "y_pred = lm.predict(X_test)\n", "\n", "def MAE(y_true, y_pred):\n", "    return np.mean(np.abs(y_true-y_pred))\n", "def MSE(y_true, y_pred):\n", "    return np.mean( np.square(y_true-y_pred)  )\n", "def RMSLE(y_true, y_pred):\n", "    return np.mean( np.square(np.log(y_true+1)-np.log(y_pred+1)))\n", "\n", "print('MAE : ', MAE(y_test, y_pred))\n", "print('MSE : ', MSE(y_test, y_pred))\n", "print('RMSLE : ', RMSLE(y_test, y_pred))\n", "\n", "y_pred\n", "\n", "y_test\n", "\n", "np.abs(y_pred-y_test).mean()\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import os\n", "plt.ion()\n", "%matplotlib inline"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["file = '../../preprocess/data/targets_dataframe.pkl'"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = pd.read_pickle(file).iloc[:,:-2]\n", "df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import seaborn as sns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fig, ax = plt.subplots(1, figsize=(15,15))\n", "corr = df.corr()\n", "sns.heatmap(corr, xticklabels=corr.columns, yticklabels=corr.columns, ax=ax, annot=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["g = sns.PairGrid(df)\n", "g.map(plt.scatter)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["plt.plot(sorted(np.abs(corr[\"Velocity\"].values)))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["corr.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.ticker as plticker"]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": false}, "outputs": [], "source": ["fig, axes = plt.subplots(len(corr.columns), figsize=(25,25))\n", "\n", "step = 1\n", "for ax,col in zip(axes,corr.columns):\n", "    values = np.abs(corr[col].values)\n", "    ixs = np.argsort(-values)\n", "    \n", "    names = corr.columns[ixs].values\n", "    values = values[ixs]\n", "    ax.plot(np.arange(step*len(ixs), step=step), values, '*-', linewidth=1)\n", "    loc = plticker.MultipleLocator(base=1.0) # this locator puts ticks at regular intervals\n", "    ax.xaxis.set_major_locator(loc)\n", "    xticks = list(ax.get_xticks())\n", "    for i in range(len(names)):\n", "        xticks[i] = names[i]\n", "        \n", "    xticks = [''] + xticks\n", "    ax.set_xticklabels(xticks,fontsize=8, rotation=15)\n", "\n", "    ax.set_title(col)\n", "    ax.set_ylabel('|correlation|')\n", "\n", "plt.tight_layout()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_trans = df.T\n", "df_summary = df_trans.iloc[:3]\n", "df_pair = df_trans.iloc[3:]\n", "\n", "columns = [ s.split('/')[0] for s in df_pair.index.values ]\n", "\n", "new_colums = []\n", "for col in columns:\n", "    if col not in new_colums:\n", "        new_colums.append(col.strip())\n", "\n", "avged_data = {}\n", "odds, evens = np.arange(start=1, stop=len(df_pair),step=2), np.arange(start=0, stop=len(df_pair),step=2)\n", "for ix, (odd, even) in enumerate(zip(odds, evens)):\n", "    avged = np.mean([df_pair.iloc[odd], df_pair.iloc[even]], axis=0).T\n", "    name=df_pair.iloc[2*ix].name.split('/')[0].strip()\n", "    avged_data[name] = avged\n", "    \n", "df_pair_avg = pd.DataFrame(avged_data, index=df_pair.columns).T"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_summary.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_pair_avg.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_trans = pd.concat([df_summary, df_pair_avg])\n", "df_trans"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_trans.T.corr()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["measurements = df_trans.values\n", "measurements.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["C = np.cov(df_trans.values)\n", "C.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["target_factors = df_trans.index.values.tolist()\n", "target_factors"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["target_factors = df_trans.index.values.tolist()\n", "target_factors.pop(target_factors.index('Toe In'))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["np.random.seed(16); rand_ixs = np.random.permutation(len(target_factors))\n", "target_factors = np.array(target_factors)[rand_ixs].tolist()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["target_factors"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_trans = df_trans.reindex(target_factors)\n", "df_trans"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["corr = df_trans.T.corr()\n", "sns.heatmap(corr, xticklabels=corr.columns, yticklabels=corr.columns, annot=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "from matplotlib import pyplot as plt\n", "import math\n", "\n", "n_clusters = 3\n", "\n", "# (n_variables x n_sample)\n", "measurements = df_trans.values\n", "\n", "# This generates 100 variables that could possibly be assigned to 5 clusters\n", "n_variables, n_samples = measurements.shape\n", "\n", "\n", "# To keep this example simple, each cluster will have a fixed size\n", "cluster_size = n_variables // n_clusters\n", "\n", "C = df_trans.T.corr().values\n", "\n", "def score(C):\n", "    '''\n", "    Function to assign a score to an ordered covariance matrix.\n", "    High correlations within a cluster improve the score.\n", "    High correlations between clusters decease the score.\n", "    '''\n", "    score = 0\n", "    for cluster in range(n_clusters):\n", "        inside_cluster = np.arange(cluster_size) + cluster * cluster_size\n", "        outside_cluster = np.setdiff1d(range(n_variables), inside_cluster)\n", "\n", "        # Belonging to the same cluster\n", "        score += np.sum(C[inside_cluster, :][:, inside_cluster])\n", "\n", "        # Belonging to different clusters\n", "        score -= np.sum(C[inside_cluster, :][:, outside_cluster])\n", "        score -= np.sum(C[outside_cluster, :][:, inside_cluster])\n", "\n", "    return score\n", "\n", "\n", "initial_C = C\n", "initial_score = score(C)\n", "initial_ordering = np.arange(n_variables)\n", "\n", "plt.figure()\n", "plt.imshow(C, interpolation='nearest')\n", "plt.title('Initial C')\n", "print('Initial ordering:', [df_trans.index.values[i] for i in initial_ordering])\n", "print('Initial covariance matrix score:', initial_score)\n", "\n", "# Pretty dumb greedy optimization algorithm that continuously\n", "# swaps rows to improve the score\n", "def swap_rows(C, var1, var2):\n", "    '''\n", "    Function to swap two rows in a covariance matrix,\n", "    updating the appropriate columns as well.\n", "    '''\n", "    D = C.copy()\n", "    D[var2, :] = C[var1, :]\n", "    D[var1, :] = C[var2, :]\n", "\n", "    E = D.copy()\n", "    E[:, var2] = D[:, var1]\n", "    E[:, var1] = D[:, var2]\n", "\n", "    return E\n", "\n", "current_C = C\n", "current_ordering = initial_ordering\n", "current_score = initial_score\n", "\n", "max_iter = 2000\n", "for i in range(max_iter):\n", "    # Find the best row swap to make\n", "    best_C = current_C\n", "    best_ordering = current_ordering\n", "    best_score = current_score\n", "    for row1 in range(n_variables):\n", "        for row2 in range(n_variables):\n", "            if row1 == row2:\n", "                continue\n", "            option_ordering = best_ordering.copy()\n", "            option_ordering[row1] = best_ordering[row2]\n", "            option_ordering[row2] = best_ordering[row1]\n", "            option_C = swap_rows(best_C, row1, row2)\n", "            option_score = score(option_C)\n", "\n", "            if option_score > best_score:\n", "                best_C = option_C\n", "                best_ordering = option_ordering\n", "                best_score = option_score\n", "\n", "    if best_score > current_score:\n", "        # Perform the best row swap\n", "        current_C = best_C\n", "        current_ordering = best_ordering\n", "        current_score = best_score\n", "    else:\n", "        # No row swap found that improves the solution, we're done\n", "        break\n", "        \n", "# Output the result\n", "plt.figure()\n", "plt.imshow(current_C, interpolation='nearest')\n", "plt.title('Best C')\n", "print('Best ordering:', [df_trans.index.values[i] for i in current_ordering])\n", "print('Best score:', current_score)\n", "print()\n", "print('Cluster     [variables assigned to this cluster]')\n", "print('------------------------------------------------')\n", "\n", "import copy\n", "current_ordering = current_ordering.tolist()\n", "tmp = copy.copy(current_ordering)\n", "\n", "cluster = 0\n", "while 1:\n", "    start, end = cluster*cluster_size, (cluster+1)*cluster_size\n", "    print('Cluster %02d  %s' % (cluster + 1, [ df_trans.index.values[i] for i in current_ordering[start:end]]))\n", "    for e in current_ordering[start:end]:\n", "        tmp.pop(tmp.index(e))\n", "        \n", "    cluster += 1\n", "    if not tmp:\n", "        break"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "from matplotlib import pyplot as plt\n", "\n", "# This generates 100 variables that could possibly be assigned to 5 clusters\n", "n_variables = 100\n", "n_clusters = 5\n", "n_samples = 1000\n", "\n", "# To keep this example simple, each cluster will have a fixed size\n", "cluster_size = n_variables // n_clusters\n", "\n", "# Assign each variable to a cluster\n", "belongs_to_cluster = np.repeat(range(n_clusters), cluster_size)\n", "np.random.shuffle(belongs_to_cluster)\n", "\n", "# This latent data is used to make variables that belong\n", "# to the same cluster correlated.\n", "latent = np.random.randn(n_clusters, n_samples)\n", "\n", "variables = []\n", "for i in range(n_variables):\n", "    variables.append(\n", "        np.random.randn(n_samples) + latent[belongs_to_cluster[i], :]\n", "    )\n", "\n", "variables = np.array(variables)\n", "\n", "C = np.cov(variables)\n", "\n", "def score(C):\n", "    '''\n", "    Function to assign a score to an ordered covariance matrix.\n", "    High correlations within a cluster improve the score.\n", "    High correlations between clusters decease the score.\n", "    '''\n", "    score = 0\n", "    for cluster in range(n_clusters):\n", "        inside_cluster = np.arange(cluster_size) + cluster * cluster_size\n", "        outside_cluster = np.setdiff1d(range(n_variables), inside_cluster)\n", "\n", "        # Belonging to the same cluster\n", "        score += np.sum(C[inside_cluster, :][:, inside_cluster])\n", "\n", "        # Belonging to different clusters\n", "        score -= np.sum(C[inside_cluster, :][:, outside_cluster])\n", "        score -= np.sum(C[outside_cluster, :][:, inside_cluster])\n", "\n", "    return score\n", "\n", "\n", "initial_C = C\n", "initial_score = score(C)\n", "initial_ordering = np.arange(n_variables)\n", "\n", "plt.figure()\n", "plt.imshow(C, interpolation='nearest')\n", "plt.title('Initial C')\n", "print('Initial ordering:', initial_ordering)\n", "print('Initial covariance matrix score:', initial_score)\n", "\n", "# Pretty dumb greedy optimization algorithm that continuously\n", "# swaps rows to improve the score\n", "def swap_rows(C, var1, var2):\n", "    '''\n", "    Function to swap two rows in a covariance matrix,\n", "    updating the appropriate columns as well.\n", "    '''\n", "    D = C.copy()\n", "    D[var2, :] = C[var1, :]\n", "    D[var1, :] = C[var2, :]\n", "\n", "    E = D.copy()\n", "    E[:, var2] = D[:, var1]\n", "    E[:, var1] = D[:, var2]\n", "\n", "    return E\n", "\n", "current_C = C\n", "current_ordering = initial_ordering\n", "current_score = initial_score\n", "\n", "max_iter = 1000\n", "for i in range(max_iter):\n", "    # Find the best row swap to make\n", "    best_C = current_C\n", "    best_ordering = current_ordering\n", "    best_score = current_score\n", "    for row1 in range(n_variables):\n", "        for row2 in range(n_variables):\n", "            if row1 == row2:\n", "                continue\n", "            option_ordering = best_ordering.copy()\n", "            option_ordering[row1] = best_ordering[row2]\n", "            option_ordering[row2] = best_ordering[row1]\n", "            option_C = swap_rows(best_C, row1, row2)\n", "            option_score = score(option_C)\n", "\n", "            if option_score > best_score:\n", "                best_C = option_C\n", "                best_ordering = option_ordering\n", "                best_score = option_score\n", "\n", "    if best_score > current_score:\n", "        # Perform the best row swap\n", "        current_C = best_C\n", "        current_ordering = best_ordering\n", "        current_score = best_score\n", "    else:\n", "        # No row swap found that improves the solution, we're done\n", "        break\n", "\n", "# Output the result\n", "plt.figure()\n", "plt.imshow(current_C, interpolation='nearest')\n", "plt.title('Best C')\n", "print('Best ordering:', current_ordering)\n", "print('Best score:', current_score)\n", "print()\n", "print('Cluster     [variables assigned to this cluster]')\n", "print('------------------------------------------------')\n", "for cluster in range(n_clusters):\n", "    print('Cluster %02d  %s' % (cluster + 1, current_ordering[cluster*cluster_size:(cluster+1)*cluster_size]))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["a = A()\n", "a.fuck()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import os\n", "import cv2\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["path = '/data/GaitData/CroppedFrameArrays/7026976_test_0_trial_3.npy'"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["arr = np.load(path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["arr.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for e in arr:\n", "    plt.imshow(cv2.cvtColor(e,cv2.COLOR_BGR2RGB))\n", "    plt.show()\n", "    "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "file = '../../preprocess/data/targets_dataframe.pkl'"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = pd.read_pickle(file)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "from sklearn.preprocessing import StandardScaler, QuantileTransformer"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["scaler = QuantileTransformer(random_state=0, output_distribution='normal')\n", "scaled_X1 = scaler.fit_transform((df['Toe In / Out/R']).values[:, None])\n", "plt.hist(scaled_X1, bins=100)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["scaler = QuantileTransformer(random_state=0, output_distribution='normal')\n", "scaled_X2 = scaler.fit_transform((df['Toe In / Out/R']+90).values[:, None])\n", "plt.hist(scaled_X2, bins=100)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "np.where(scaled_X1 != scaled_X2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["scaled_X1[8]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["scaled_X2[8]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df['Toe In / Out/R'] = df['Toe In / Out/R']+90"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["plt.hist(df['CV Stride Length/R'], bins=100)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["plt.hist(df['Swing % of Cycle/L'], bins=100)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df['Stance % of Cycle/L'][1]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df['Stance Time(sec)/L'][1]/df['Cycle Time(sec)/L'][1]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df['Stance % of Cycle/L'][0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df['Stance Time(sec)/L'][0]/df['Cycle Time(sec)/L'][0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df['Double Supp % Cycle/L'][1]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df['Double Supp. Time(sec)/L'][0]/df['Cycle Time(sec)/L'][0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df['Stride Length Std Dev/L'][0]/df['Stride Length(cm)/L'][0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["plt.hist(df['Stride Length(cm)/L'], bins=100)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["plt.hist(df['Stride Length Std Dev/L'], bins=100)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for i in range(10):\n", "    print(df['Stride Length(cm)/L'][i], df['Stride Length Std Dev/L'][i])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["plt.scatter(df['Stride Length(cm)/L'].values, df['Stride Length Std Dev/L'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df['Stride Length(cm)/L'][0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_X2 = df['Stride Length Std Dev/L']**2 + df['Stride Length(cm)/L']**2\n", "scaler = QuantileTransformer(random_state=0, output_distribution='normal')\n", "plt.hist(scaler.fit_transform(df_X2[:,None]), bins=100)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_X2"]}, {"cell_type": "code", "execution_count": 115, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.00028900000000000003"]}, "execution_count": 115, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "file = '../../preprocess/data/targets_dataframe.pkl'\n", "df = pd.read_pickle(file)\n", "df = df[~(df == 0).any(axis=1)]\n", "import numpy as np\n", "np.abs(df.values).min()"]}, {"cell_type": "code", "execution_count": 128, "metadata": {}, "outputs": [], "source": ["from sklearn.preprocessing import QuantileTransformer, MinMaxScaler, StandardScaler\n", "from sklearn.pipeline import Pipeline\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 134, "metadata": {}, "outputs": [], "source": ["scaler = StandardScaler()\n", "\n", "# scaler = Pipeline([\n", "#     ('qt', scaler),\n", "#     ('minmax', MinMaxScaler())\n", "# ])\n", "\n", "scaled_vals = scaler.fit_transform(df)"]}, {"cell_type": "code", "execution_count": 135, "metadata": {}, "outputs": [{"data": {"text/plain": ["(array([  1.,   0.,   0.,   0.,   0.,   0.,   0.,   0.,   0.,   2.,   1.,\n", "          1.,   1.,   0.,   0.,   1.,   1.,   0.,   0.,   0.,   0.,   0.,\n", "          0.,   0.,   1.,   5.,   0.,   2.,   1.,   6.,   4.,  17.,  15.,\n", "         29.,  24.,  25.,  44.,  22.,  65.,  63.,  77., 101.,  94., 102.,\n", "        117., 141., 127., 131., 169., 172., 165., 141., 148., 154., 157.,\n", "        123., 132., 141., 120., 121., 104.,  83.,  80.,  96.,  75.,  51.,\n", "         62.,  31.,  20.,  23.,  22.,  13.,  17.,  11.,   8.,  10.,   5.,\n", "          9.,   4.,   4.,   6.,   3.,   1.,   4.,   3.,   4.,   1.,   2.,\n", "          0.,   2.,   0.,   1.,   0.,   0.,   1.,   0.,   0.,   1.,   1.,\n", "          1.]),\n", " array([-30.3  , -29.489, -28.678, -27.867, -27.056, -26.245, -25.434,\n", "        -24.623, -23.812, -23.001, -22.19 , -21.379, -20.568, -19.757,\n", "        -18.946, -18.135, -17.324, -16.513, -15.702, -14.891, -14.08 ,\n", "        -13.269, -12.458, -11.647, -10.836, -10.025,  -9.214,  -8.403,\n", "         -7.592,  -6.781,  -5.97 ,  -5.159,  -4.348,  -3.537,  -2.726,\n", "         -1.915,  -1.104,  -0.293,   0.518,   1.329,   2.14 ,   2.951,\n", "          3.762,   4.573,   5.384,   6.195,   7.006,   7.817,   8.628,\n", "          9.439,  10.25 ,  11.061,  11.872,  12.683,  13.494,  14.305,\n", "         15.116,  15.927,  16.738,  17.549,  18.36 ,  19.171,  19.982,\n", "         20.793,  21.604,  22.415,  23.226,  24.037,  24.848,  25.659,\n", "         26.47 ,  27.281,  28.092,  28.903,  29.714,  30.525,  31.336,\n", "         32.147,  32.958,  33.769,  34.58 ,  35.391,  36.202,  37.013,\n", "         37.824,  38.635,  39.446,  40.257,  41.068,  41.879,  42.69 ,\n", "         43.501,  44.312,  45.123,  45.934,  46.745,  47.556,  48.367,\n", "         49.178,  49.989,  50.8  ]),\n", " <a list of 100 Patch objects>)"]}, "execution_count": 135, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.hist(df.values[:, 16], bins=100)"]}, {"cell_type": "code", "execution_count": 136, "metadata": {}, "outputs": [{"data": {"text/plain": ["(array([  1.,   0.,   0.,   0.,   0.,   0.,   0.,   0.,   0.,   2.,   1.,\n", "          1.,   1.,   0.,   0.,   1.,   1.,   0.,   0.,   0.,   0.,   0.,\n", "          0.,   0.,   1.,   5.,   0.,   2.,   1.,   6.,   4.,  17.,  15.,\n", "         29.,  24.,  25.,  44.,  22.,  65.,  63.,  77., 101.,  94., 102.,\n", "        117., 141., 127., 131., 169., 172., 165., 141., 148., 154., 157.,\n", "        123., 132., 141., 120., 121., 104.,  83.,  80.,  96.,  75.,  51.,\n", "         62.,  31.,  20.,  23.,  22.,  13.,  17.,  11.,   8.,  10.,   5.,\n", "          9.,   4.,   4.,   6.,   3.,   1.,   4.,   3.,   4.,   1.,   2.,\n", "          0.,   2.,   0.,   1.,   0.,   0.,   1.,   0.,   0.,   1.,   1.,\n", "          1.]),\n", " array([-5.36264722, -5.26017612, -5.15770502, -5.05523392, -4.95276282,\n", "        -4.85029172, -4.74782062, -4.64534952, -4.54287842, -4.44040732,\n", "        -4.33793622, -4.23546512, -4.13299402, -4.03052292, -3.92805182,\n", "        -3.82558072, -3.72310962, -3.62063852, -3.51816742, -3.41569632,\n", "        -3.31322522, -3.21075412, -3.10828302, -3.00581192, -2.90334082,\n", "        -2.80086972, -2.69839862, -2.59592752, -2.49345641, -2.39098531,\n", "        -2.28851421, -2.18604311, -2.08357201, -1.98110091, -1.87862981,\n", "        -1.77615871, -1.67368761, -1.57121651, -1.46874541, -1.36627431,\n", "        -1.26380321, -1.16133211, -1.05886101, -0.95638991, -0.85391881,\n", "        -0.75144771, -0.64897661, -0.54650551, -0.44403441, -0.34156331,\n", "        -0.23909221, -0.13662111, -0.03415001,  0.06832109,  0.17079219,\n", "         0.27326329,  0.37573439,  0.47820549,  0.58067659,  0.68314769,\n", "         0.78561879,  0.88808989,  0.99056099,  1.09303209,  1.1955032 ,\n", "         1.2979743 ,  1.4004454 ,  1.5029165 ,  1.6053876 ,  1.7078587 ,\n", "         1.8103298 ,  1.9128009 ,  2.015272  ,  2.1177431 ,  2.2202142 ,\n", "         2.3226853 ,  2.4251564 ,  2.5276275 ,  2.6300986 ,  2.7325697 ,\n", "         2.8350408 ,  2.9375119 ,  3.039983  ,  3.1424541 ,  3.2449252 ,\n", "         3.3473963 ,  3.4498674 ,  3.5523385 ,  3.6548096 ,  3.7572807 ,\n", "         3.8597518 ,  3.9622229 ,  4.064694  ,  4.1671651 ,  4.2696362 ,\n", "         4.3721073 ,  4.4745784 ,  4.5770495 ,  4.6795206 ,  4.7819917 ,\n", "         4.88446281]),\n", " <a list of 100 Patch objects>)"]}, "execution_count": 136, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.hist(scaled_vals[:, 16], bins=100)"]}, {"cell_type": "code", "execution_count": 107, "metadata": {}, "outputs": [{"data": {"text/plain": ["16"]}, "execution_count": 107, "metadata": {}, "output_type": "execute_result"}], "source": ["list(df.columns).index('Toe In / Out/L')"]}, {"cell_type": "code", "execution_count": 139, "metadata": {}, "outputs": [{"data": {"text/plain": ["count    3723.000000\n", "mean       12.142278\n", "std         7.915490\n", "min       -30.300000\n", "25%         7.000000\n", "50%        11.800000\n", "75%        17.200000\n", "max        50.800000\n", "Name: Toe In / Out/L, dtype: float64"]}, "execution_count": 139, "metadata": {}, "output_type": "execute_result"}], "source": ["df['Toe In / Out/L'].describe()"]}, {"cell_type": "code", "execution_count": 141, "metadata": {}, "outputs": [{"data": {"text/plain": ["count    3723.000000\n", "mean       71.108515\n", "std        27.139210\n", "min         7.000000\n", "25%        51.800000\n", "50%        71.500000\n", "75%        90.200000\n", "max       192.300000\n", "Name: Velocity, dtype: float64"]}, "execution_count": 141, "metadata": {}, "output_type": "execute_result"}], "source": ["df['Velocity'].describe()"]}, {"cell_type": "code", "execution_count": 142, "metadata": {}, "outputs": [{"data": {"text/plain": ["count    3723.000000\n", "mean        0.067861\n", "std         0.119826\n", "min         0.001000\n", "25%         0.026000\n", "50%         0.042000\n", "75%         0.068000\n", "max         2.350000\n", "Name: Stride Time Std Dev/L, dtype: float64"]}, "execution_count": 142, "metadata": {}, "output_type": "execute_result"}], "source": ["df['Stride Time Std Dev/L'].describe()"]}, {"cell_type": "code", "execution_count": 144, "metadata": {}, "outputs": [{"data": {"text/plain": ["count    3723.000000\n", "mean        1.161489\n", "std         0.253123\n", "min         0.716000\n", "25%         1.033000\n", "50%         1.119000\n", "75%         1.223000\n", "max         5.669000\n", "Name: Cycle Time(sec)/L, dtype: float64"]}, "execution_count": 144, "metadata": {}, "output_type": "execute_result"}], "source": ["df['Cycle Time(sec)/L'].describe()"]}, {"cell_type": "code", "execution_count": 145, "metadata": {}, "outputs": [], "source": ["from sklearn.datasets import fetch_olivetti_faces"]}, {"cell_type": "code", "execution_count": 146, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["downloading <PERSON><PERSON> faces from https://ndownloader.figshare.com/files/5976027 to /root/scikit_learn_data\n"]}], "source": ["data = fetch_olivetti_faces()"]}, {"cell_type": "code", "execution_count": 154, "metadata": {}, "outputs": [{"data": {"text/plain": ["1.0"]}, "execution_count": 154, "metadata": {}, "output_type": "execute_result"}], "source": ["data.images.max()"]}, {"cell_type": "code", "execution_count": 155, "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": 156, "metadata": {}, "outputs": [], "source": ["df = pd.read_pickle('../../preprocess/data/targets_dataframe.pkl')"]}, {"cell_type": "code", "execution_count": 168, "metadata": {}, "outputs": [{"data": {"text/plain": ["(1.918, 128.717)"]}, "execution_count": 168, "metadata": {}, "output_type": "execute_result"}], "source": ["df['Stride Length Std Dev/L'][0], df['Stride Length(cm)/R'][0]"]}, {"cell_type": "code", "execution_count": 172, "metadata": {}, "outputs": [{"data": {"text/plain": ["(array([7.160e+02, 1.786e+03, 8.810e+02, 2.650e+02, 7.900e+01, 2.400e+01,\n", "        7.000e+00, 0.000e+00, 1.000e+00, 1.000e+00]),\n", " array([ 0.    ,  2.9705,  5.941 ,  8.9115, 11.882 , 14.8525, 17.823 ,\n", "        20.7935, 23.764 , 26.7345, 29.705 ]),\n", " <a list of 10 Patch objects>)"]}, "execution_count": 172, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.hist(df['Stride Length Std Dev/L'])"]}, {"cell_type": "code", "execution_count": 170, "metadata": {}, "outputs": [{"data": {"text/plain": ["(array([7.660e+02, 1.368e+03, 7.720e+02, 3.210e+02, 1.530e+02, 9.400e+01,\n", "        6.600e+01, 4.000e+01, 3.200e+01, 2.000e+01, 1.600e+01, 1.500e+01,\n", "        1.200e+01, 1.100e+01, 4.000e+00, 2.000e+00, 1.000e+00, 8.000e+00,\n", "        1.000e+00, 7.000e+00, 6.000e+00, 5.000e+00, 2.000e+00, 2.000e+00,\n", "        1.000e+00, 2.000e+00, 1.000e+00, 0.000e+00, 5.000e+00, 1.000e+00,\n", "        0.000e+00, 0.000e+00, 1.000e+00, 3.000e+00, 1.000e+00, 0.000e+00,\n", "        3.000e+00, 3.000e+00, 0.000e+00, 0.000e+00, 1.000e+00, 1.000e+00,\n", "        0.000e+00, 0.000e+00, 0.000e+00, 1.000e+00, 1.000e+00, 1.000e+00,\n", "        0.000e+00, 1.000e+00, 0.000e+00, 1.000e+00, 1.000e+00, 1.000e+00,\n", "        0.000e+00, 0.000e+00, 2.000e+00, 0.000e+00, 0.000e+00, 0.000e+00,\n", "        0.000e+00, 0.000e+00, 0.000e+00, 0.000e+00, 0.000e+00, 0.000e+00,\n", "        0.000e+00, 0.000e+00, 0.000e+00, 0.000e+00, 1.000e+00, 0.000e+00,\n", "        0.000e+00, 0.000e+00, 0.000e+00, 0.000e+00, 0.000e+00, 0.000e+00,\n", "        0.000e+00, 0.000e+00, 1.000e+00, 0.000e+00, 0.000e+00, 0.000e+00,\n", "        0.000e+00, 0.000e+00, 0.000e+00, 0.000e+00, 0.000e+00, 0.000e+00,\n", "        0.000e+00, 0.000e+00, 0.000e+00, 0.000e+00, 0.000e+00, 0.000e+00,\n", "        0.000e+00, 1.000e+00, 0.000e+00, 1.000e+00]),\n", " array([0.    , 0.0235, 0.047 , 0.0705, 0.094 , 0.1175, 0.141 , 0.1645,\n", "        0.188 , 0.2115, 0.235 , 0.2585, 0.282 , 0.3055, 0.329 , 0.3525,\n", "        0.376 , 0.3995, 0.423 , 0.4465, 0.47  , 0.4935, 0.517 , 0.5405,\n", "        0.564 , 0.5875, 0.611 , 0.6345, 0.658 , 0.6815, 0.705 , 0.7285,\n", "        0.752 , 0.7755, 0.799 , 0.8225, 0.846 , 0.8695, 0.893 , 0.9165,\n", "        0.94  , 0.9635, 0.987 , 1.0105, 1.034 , 1.0575, 1.081 , 1.1045,\n", "        1.128 , 1.1515, 1.175 , 1.1985, 1.222 , 1.2455, 1.269 , 1.2925,\n", "        1.316 , 1.3395, 1.363 , 1.3865, 1.41  , 1.4335, 1.457 , 1.4805,\n", "        1.504 , 1.5275, 1.551 , 1.5745, 1.598 , 1.6215, 1.645 , 1.6685,\n", "        1.692 , 1.7155, 1.739 , 1.7625, 1.786 , 1.8095, 1.833 , 1.8565,\n", "        1.88  , 1.9035, 1.927 , 1.9505, 1.974 , 1.9975, 2.021 , 2.0445,\n", "        2.068 , 2.0915, 2.115 , 2.1385, 2.162 , 2.1855, 2.209 , 2.2325,\n", "        2.256 , 2.2795, 2.303 , 2.3265, 2.35  ]),\n", " <a list of 100 Patch objects>)"]}, "execution_count": 170, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.hist(df['Stride Time Std Dev/L'], bins=100)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.8"}}, "nbformat": 4, "nbformat_minor": 2}