from __future__ import print_function, division

import copy
from sklearn.model_selection import train_test_split
import pandas as pd
import numpy as np
import cv2
import os
import torch
from PIL import Image
from torch.utils.data import Dataset
from natsort import natsorted
import sys
import random
import torchvision.transforms as TF
import torchvision.transforms.functional as tf_func
import torch.nn.functional as F
import collections
from IPython.core.debugger import set_trace

def get_direction(patient_positions):
    start_pos, end_pos = [patient_positions[0], patient_positions[-1]]
    s_xmin, s_ymin, s_xmax, s_ymax = eval(start_pos)
    e_xmin, e_ymin, e_xmax, e_ymax = eval(end_pos)
    s_c = (s_xmin + s_xmax) / 2, (s_ymin + s_ymax) / 2
    e_c = (e_xmin + e_xmax) / 2, (e_ymin + e_ymax) / 2
    res = 'approaching' if e_c[1] > s_c[1] else 'leaving'

    return res


def pid2vid(pid):
    num, test_id, trial_id = pid.split('_')
    return '_'.join([num, 'test', test_id, 'trial', trial_id])


def vid2pid(vid):
    split = vid.split('_')
    return '_'.join([split[0], split[2], split[4]])


def arrange_vids(vids, seed=0):
    np.random.seed(seed)
    np.random.shuffle(vids)
    return vids


def filter_input_df_with_vids(df, vids):
    return df[df['vids'].isin(vids)]


def filter_target_df_with_vids(df, vids):
    target_ids = [vid2pid(vid) for vid in vids]
    return df.loc[target_ids]


def split_dataset_with_vids(input_df, target_df, vids,
                            test_size=0.2, random_state=42):
    train_vids, test_vids = train_test_split(
        vids, test_size=test_size, random_state=random_state)

    train_X, train_y = filter_input_df_with_vids(
        input_df, train_vids), filter_target_df_with_vids(target_df,
                                                          train_vids)

    test_X, test_y = filter_input_df_with_vids(
        input_df, test_vids), filter_target_df_with_vids(target_df,
                                                         test_vids)

    return train_X, train_y, train_vids, test_X, test_y, test_vids


def prepare_dataset(input_file, target_file,
                    target_columns, chunk_parts, target_transform=None, legacy_mode=True, clinical_test_file=""):
    
    if legacy_mode:
        # if data generation is not the case..
        prefix, ext = os.path.splitext(input_file)

        data_frames = []
        for ix in range(chunk_parts):
            partial = prefix + '-' + str(ix) + ext
            data_frames.append(pd.read_pickle(partial))

        # concat all df
        input_df = pd.concat(data_frames)
        input_df.to_pickle(prefix + '-' + 'merged' + ext)  # save input file
        full_df = pd.read_pickle(target_file)

    else:
        input_df = pd.read_pickle(input_file)
        full_df = pd.read_excel(target_file)
        full_df.drop_duplicates("VideoFile", keep=False, inplace=True)
        full_df.set_index('index', inplace=True)
        full_df = full_df.loc[~full_df.index.duplicated()]
        full_df.index.name = ""

        # Rename columns 
        full_df = full_df.rename(
            columns={
                'D_Supp_PercL': 'D_Supp_Perc_L', 'D_Supp_PercR': 'D_Supp_Perc_R',
                'ToeInOutL': 'ToeInOut_L', 'ToeInOutR': 'ToeInOut_R'
            }
        )

    possible_vids = sorted(list(set(input_df.vids[~input_df.vids.isna()])))

    # reindex tgt data (to filter-out valid vids)
    full_df = full_df.reindex([vid2pid(vid) for vid in possible_vids])
    full_df = full_df.dropna()

    # drop rows with any zero value
    full_df = full_df[~(full_df == 0).any(axis=1)]

    # Target gait paramters
    target_df = full_df[target_columns]
    possible_vids = [pid2vid(pid) for pid in target_df.index]
    if target_transform:
        scaled_values = target_transform.fit_transform(target_df)
        target_df.loc[:, :] = scaled_values

    if clinical_test_file != "":
        # import ipdb; ipdb.set_trace()
        assert os.path.splitext(clinical_test_file)[1] == ".xlsx", "clinical_test_file format has to be .xlsx."
        test_data = pd.read_excel(clinical_test_file)

        # dtype/format 맞추기
        full_df["Pt_number"] = full_df["Pt_number"].astype(str)
        test_data["ID"] = test_data["ID"].astype(str)
        test_data["Pre"] = pd.to_datetime(test_data["Pre"]).dt.date     # 날짜 까지만 표기
        test_data["Post"] = pd.to_datetime(test_data["Post"]).dt.date   # 날짜 까지만 표기
        full_df["Time"] = pd.to_datetime(full_df["Time"]).dt.date # 날짜만 추출

        # test_data에 속한 모든 환자를 학습 데이터에서 제외
        vids_to_exclude = full_df[full_df["Pt_number"].isin(test_data["ID"])].index.map(pid2vid)
        possible_vids = list(set(possible_vids)-set(vids_to_exclude))

        # Pre/Post 시점과 일치하는 데이터를 테스트셋으로 사용
        pre = pd.merge(full_df[["VideoFile", "Pt_number", "Time"]], test_data[["ID", "Pre", "No"]], 
                  left_on=['Pt_number', 'Time'], 
                  right_on=['ID', 'Pre'])
        post = pd.merge(full_df[["VideoFile", "Pt_number", "Time"]], test_data[["ID", "Post", "No"]], 
                  left_on=['Pt_number', 'Time'], 
                  right_on=['ID', 'Post'])

        pre_vids = pre["VideoFile"].str.split('/').str[-1].str.split(r"\.avi", expand=True).values[:, 0].tolist()
        post_vids = post["VideoFile"].str.split('/').str[-1].str.split(r"\.avi", expand=True).values[:, 0].tolist()

        pre_pids = [vid2pid(i) for i in pre_vids]
        post_pids = [vid2pid(i) for i in post_vids]

        pre_index = [ f"Pre_{n}" for n in pre["No"] ]
        post_index = [ f"Post_{n}" for n in post["No"] ]

        test_vids = pre_vids + post_vids
        train_vids = list(set(possible_vids) - set(test_vids))

        train_X, train_y = filter_input_df_with_vids(input_df, train_vids), filter_target_df_with_vids(target_df, train_vids)
        test_X, test_y = filter_input_df_with_vids(input_df, test_vids), filter_target_df_with_vids(target_df, test_vids)

        # Index 컬럼 추가
        test_y.loc[pre_pids + post_pids, "Index"] = pre_index + post_index
        # import ipdb; ipdb.set_trace()
    else:
        # split dataset (typical random split)
        train_X, train_y, train_vids, test_X, test_y, test_vids =\
            split_dataset_with_vids(
                input_df, target_df, possible_vids,
                test_size=0.2, random_state=42)
        
    return dict(train_X=train_X, train_y=train_y, train_vids=train_vids,
                test_X=test_X, test_y=test_y, test_vids=test_vids,
                input_df=input_df, target_df=target_df)


def generate_dataloader_for_crossvalidation(opt, ds, vids,
                                            ds_class,
                                            phase=None,
                                            spatial_transform=None,
                                            temporal_transform=None,
                                            shuffle=True):

    from torch.utils.data import DataLoader
    X, y = filter_input_df_with_vids(
        ds.X, vids), filter_target_df_with_vids(ds.y, vids)

    X = X.groupby('vids').apply(lambda x: x.drop_duplicates('idx').sort_values('idx'))

    ds = ds_class(X, y,
                  opt=opt, phase=phase,
                  spatial_transform=spatial_transform,
                  temporal_transform=temporal_transform)
    #import time
    #t = time.time()
    #for i in range(16):
    #    ds[i]
    #print('elapsed: ', (time.time()-t)/16)
    #import pdb; pdb.set_trace()

    # Check if we're using DDP
    use_ddp = hasattr(opt, 'use_ddp') and opt.use_ddp
    sampler = None

    if use_ddp and phase == 'train':
        from torch.utils.data.distributed import DistributedSampler
        sampler = DistributedSampler(ds, shuffle=shuffle)
        shuffle = False  # Don't shuffle when using sampler

        # Adjust batch size and workers for DDP
        import os
        world_size = len(os.environ.get("CUDA_VISIBLE_DEVICES", "0").split(","))
        batch_size = opt.batch_size // world_size

        # Further reduce batch size for validation to prevent OOM
        if phase == 'valid':
            batch_size = max(1, batch_size // 2)  # Half the batch size for validation

        num_workers = max(1, opt.n_threads // world_size)
    else:
        batch_size = opt.batch_size
        num_workers = opt.n_threads

    # Memory-optimized dataloader settings
    # Reduce memory usage for DDP to prevent OOM
    if use_ddp:
        pin_memory = False  # Disable pin_memory for DDP to save memory
        prefetch_factor = 1  # Reduce prefetch to save memory
        persistent_workers = False  # Disable persistent workers to save memory
        num_workers = min(num_workers, 4)  # Limit workers to prevent too many processes
    else:
        pin_memory = True
        prefetch_factor = 2
        persistent_workers = True

    # define dataloader
    loader = DataLoader(ds,
                        batch_size=batch_size,
                        shuffle=shuffle,
                        sampler=sampler,
                        num_workers=num_workers,
                        drop_last=True if phase=='train' else False, # multi-gpu needs at least 2 samples for training BN layer
                        pin_memory=pin_memory,
                        prefetch_factor=prefetch_factor,
                        persistent_workers=persistent_workers)

    return loader


def get_video_frame_count(data_root, vid):
    """Get frame count for a video, supporting both video files and frame directories."""
    # Try video file first
    video_path = os.path.join(data_root, vid + '.avi')
    if os.path.exists(video_path):
        from decord import VideoReader
        vr = VideoReader(video_path)
        return len(vr)

    # Fallback to frame directory
    frame_dir = os.path.join(data_root, vid)
    if os.path.exists(frame_dir):
        return len(os.listdir(frame_dir))

    raise FileNotFoundError(f"Neither video file {video_path} nor frame directory {frame_dir} found")


def video_loader(data_root, vid, frame_indices, size, mode='PIL'):
    assert type(size) in [tuple, int], 'size should be tuple or int'

    if type(size) == int:
        size = (size, size)

    if mode == 'numpy':
        res = np.load(os.path.join(data_root, vid) + '.npy')
    elif mode == 'PIL':
        res = []
        subdir = os.path.join(data_root, vid)
        for i in frame_indices:
            f = os.path.join(subdir, f"thumb{int(i):04d}.jpg")
            img = Image.open(f).resize(size)
            res.append(img)
    elif mode == 'decord':
        # Optimized video loading using decord
        from decord import VideoReader

        # Construct video file path
        video_path = os.path.join(data_root, vid + '.avi')

        if not os.path.exists(video_path):
            raise FileNotFoundError(f"Video file not found: {video_path}")

        # Load video using decord
        vr = VideoReader(video_path)

        # Convert frame indices to 0-based (decord uses 0-based indexing)
        # Assuming frame_indices are 1-based from the original implementation
        decord_indices = [max(0, min(int(i) - 1, len(vr) - 1)) for i in frame_indices]

        # Load frames efficiently
        frames = vr.get_batch(decord_indices).asnumpy()  # Shape: (N, H, W, C)

        # Convert to PIL Images and resize
        res = []
        for frame in frames:
            # Convert from numpy array to PIL Image
            img = Image.fromarray(frame)
            # Resize to target size
            img = img.resize(size)
            res.append(img)

    return res


def process_as_tensor_image(imgs, padding, pad_mode):
    imgs = torch.stack([img
                        for img in imgs])

    imgs = imgs.permute(
        1, 0, 2, 3)     # (C,D,H,W)

    if pad_mode == 'replicate':
        # replicated-padding
        imgs = F.pad(imgs.permute(0, 2, 3, 1), padding,
                     mode=pad_mode).permute(0, 3, 1, 2)
    elif pad_mode == 'zeropad':
        # zero padding
        imgs = F.pad(imgs, padding)

    return imgs


def get_input(video_data, spatial_transform, padding, pad_mode, seed):
    input_ = []

    for idx in range(len(video_data)):
        random.seed(seed)
        img = video_data[idx]
        t_img = spatial_transform(img)   # Tensor image (C,H,W)
        input_.append(t_img)

    input_ = process_as_tensor_image(input_, padding, pad_mode)

    return input_


def get_flows(input_imgs,
              mean=[0.43216, 0.394666, 0.37645],
              std=[0.22803, 0.22145, 0.216989]):

    np_imgs = input_imgs.permute(1, 2, 3, 0).numpy()

    def denormalize_img(img):
        img = std * img + mean
        img = np.clip(img, 0, 1)
        img = cv2.normalize(img, None, 0, 255,
                            cv2.NORM_MINMAX, dtype=cv2.CV_8UC1)

        return img

    prev = denormalize_img(np_imgs[0])
    prev = cv2.cvtColor(prev, cv2.COLOR_BGR2GRAY)

    flows = []
    for i in range(1, len(np_imgs)):
        nxt = denormalize_img(np_imgs[i])
        nxt = cv2.cvtColor(nxt, cv2.COLOR_BGR2GRAY)
        flows.append(
            torch.from_numpy(
                cv2.calcOpticalFlowFarneback(
                    prev, nxt, None, 0.5, 3, 15, 3, 5, 1.2, 0)
            )
        )
        prev = nxt

    return torch.stack(flows).permute(3, 0, 1, 2)


def get_mask(patient_positions, crop_position, angle, padding, pad_mode, opt):
    ratio = {
        'height': opt.img_size/opt.raw_h,
        'width': opt.img_size/opt.raw_w
    }

    mask_ = []

    for idx in range(len(patient_positions)):
        pos = patient_positions[idx]
        xmin, ymin, xmax, ymax = eval(pos)

        xmin, xmax = [round(ratio['width']*v) for v in [xmin, xmax]]
        ymin, ymax = [round(ratio['height']*v) for v in [ymin, ymax]]

        mask = np.zeros((opt.img_size, opt.img_size))
        mask[ymin:ymax, xmin:xmax] = 1.0

        mask_.append(
            tf_func.to_tensor(
                tf_func.resized_crop(
                    tf_func.rotate(
                        Image.fromarray(mask), angle),
                    *crop_position, size=(opt.sample_size, opt.sample_size)
                )
            )
        )

    mask_ = process_as_tensor_image(mask_, padding, pad_mode)

    return mask_

class GAITDataset(Dataset):
    def __init__(self,
                 X,
                 y,
                 opt,
                 phase,
                 spatial_transform=None, temporal_transform=None):

        self.X = X
        self.y = y
        self.vids = arrange_vids(natsorted(list(set(X.vids))), seed=0)
        self.load_pretrained = opt.load_pretrained

        self.sample_duration = opt.sample_duration

        self.spatial_transform = spatial_transform
        self.temporal_transform = temporal_transform

        self.opt = opt
        self.phase = phase
        self.return_mask  = opt.with_segmentation
        self.clinical_test = (opt.clinical_test_file != "")

    def __len__(self):
        return len(self.vids)

    def process_sampled_data(self, cur_X, vid):
        # Determine video loading mode based on data_root structure or file existence
        video_mode = 'decord' if (self.opt.data_root.endswith('videos/video_formated_trim') or
                                 os.path.exists(os.path.join(self.opt.data_root, vid + '.avi'))) else 'PIL'

        # Get frame count using the helper function
        frame_count = get_video_frame_count(self.opt.data_root, vid)

        cur_X.idx = np.clip(cur_X.idx - cur_X.idx.min() + 1, 1, frame_count)

        indices_sampled = np.linspace(
            0, len(cur_X), self.opt.sample_duration, endpoint=False).astype(np.int)

        if self.temporal_transform:
            indices_sampled = self.temporal_transform(indices_sampled)

        cur_X = cur_X.iloc[indices_sampled]

        frame_indices, patient_positions = cur_X.idx.values, cur_X.pos.values

        video_data = video_loader(
            self.opt.data_root, vid, frame_indices,
            size=self.opt.img_size, mode=video_mode)

        seed = random.randint(-sys.maxsize, sys.maxsize)

        if self.phase == 'train':
            # @ train; fixed rotation angle for entire video frames
            rotation_method, crop_method = self.spatial_transform.transforms[:2]

            random.seed(seed)

            angle = rotation_method.get_params(
                rotation_method.degrees
            )

            random.seed(seed)

            # for fixed cropping for entire video frames
            crop_position = crop_method.get_params(
                video_data[0], crop_method.scale, crop_method.ratio)

        else:
            # @ test; without tilt and croping
            _start = (self.opt.img_size-self.opt.sample_size)//2
            angle = 0.0
            crop_position = (
                _start, _start, self.opt.sample_size, self.opt.sample_size)

        input_imgs = get_input(video_data,
                               self.spatial_transform,
                               padding=(
                                   0, 0,
                                   0, 0,
                                   0, self.sample_duration - len(frame_indices)),
                               pad_mode='zeropad',
                               seed=seed)

        # # optical flow imgs
        # flows = get_flows(input_imgs)

        # # merge rgb img & optical flow through channel dims
        # input_imgs = torch.cat([input_imgs[:, :-1], flows])

        mask_imgs = get_mask(patient_positions,
                             crop_position, angle,
                             padding=(
                                 0, 0,
                                 0, 0,
                                 0, self.sample_duration - len(frame_indices)),
                             pad_mode='zeropad',
                             opt=self.opt)

        return input_imgs, mask_imgs, len(frame_indices)

    def __getitem__(self, idx):
        vid = self.vids[idx]

        cur_X = self.X[self.X.vids == vid]
        cur_y = self.y.loc[vid2pid(vid)]

        input_imgs, mask_imgs, valid_lengths = self.process_sampled_data(
            cur_X, vid)

        # target is always same!
        targets = cur_y
        if self.phase == 'test' and self.clinical_test:
            targets = cur_y.drop('Index')
            index   = str(cur_y["Index"])

        targets = torch.tensor(targets, dtype=torch.float32)

        if self.phase == 'test' and self.clinical_test:
            if self.return_mask:
                return input_imgs, mask_imgs, targets, vid, valid_lengths, index
            else:
                return input_imgs, targets, vid, valid_lengths, index
        else:
            if self.return_mask:
                return input_imgs, mask_imgs, targets, vid, valid_lengths
            else:
                return input_imgs, targets, vid, valid_lengths
