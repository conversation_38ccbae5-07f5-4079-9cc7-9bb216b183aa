<!--suppress ALL -->
<head>
<style>
    main {
        font-size: 30px;
        padding: 10px;
        border: 5px solid gray;
    }
</style>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js">
</script>
</head>
<title>Medical GaitAnalysis Demo</title>

<body>
<script src="static/app.js" type="text/javascript"></script>

<h1>Medical GaitAnalysis Demo</h1>
<!-- <main>
    <p>Steps are as below...</p>
    <p>1. Select a video. </p>
    <p>2. Submit the selected video to our computing server. </p>
    <p>3. Check the result. </p>
</main> -->
<video id="vid" controls width="480" height="360">
    <source src="{{ data.filename }}" type="video/mp4"/>
</video>
<br />

<form action="{{ url_for('upload_file') }}" method=post enctype=multipart/form-data>
    <p><input type=file name=file id="filename"></p>
    <p><input type=submit value=submit>  {{ status }}</p>
</form>
<button onclick="getStartTime()">Get start time</button>
<span>Start : </span><span id="startTime"></span>
<br />
<button onclick="getEndTime()">Get end time</button>
<span>End : </span><span id="endTime"></span>
<br />
<button onclick="run_api()">Run</button>

</body>