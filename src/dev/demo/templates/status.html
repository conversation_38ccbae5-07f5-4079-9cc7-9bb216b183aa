<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Medical GaitAnalysis Demo</title>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
    <script>
        var data_str = "{{ data }}";
        var data_obj = JSON.parse(data_str.replace(/&#34;/g,'"'));

        window.onload = function () {
        	var pos = data_obj.pos;
            var val = data_obj.val;
            var data_points = [];
            for(var i=0;i<pos.length;i++){
                data_points[i] = {y:val[i], label:pos[i]};
            }

            var chart = new CanvasJS.Chart("chartContainer", {
            animationEnabled: true,

            title:{
                text:"Gaitparameters"
            },
            axisX:{
                interval: 1
            },
            axisY2:{
                interlacedColor: "rgba(1,77,101,.2)",
                gridColor: "rgba(1,77,101,.1)",
                title: "Summary of results"
            },
            data: [{
                type: "bar",
                name: "gaitparams",
                axisYType: "secondary",
                color: "#014D65",
                dataPoints: data_points
            }]
        });
        chart.render();

        }
</script>
</head>

<body>
<div id="chartContainer" style="height: 370px; max-width: 920px; margin: 0px auto;"></div>
<script src="https://canvasjs.com/assets/script/canvasjs.min.js"></script>
</body>

</html>


