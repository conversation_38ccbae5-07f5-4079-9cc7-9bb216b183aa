from functools import partial
import sys, os
sys.path.append(os.path.dirname(os.getcwd())) 

from utils.generate_model import init_state, load_trained_ckpt
from utils.transforms import (
    Compose, ToTensor, MultiScaleRandomCrop, MultiScaleCornerCrop, Normalize,
    TemporalRandomCrop, TemporalCenterCrop, LoopPadding)
from utils.target_columns import get_target_columns
from utils.mean import get_mean, get_std
from utils.preprocessing import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    COPAnalyizer,
    HumanScaleAnalyizer,
    Worker,
)
import datasets.gaitregression
from collections import defaultdict
from tqdm import tqdm
import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import sklearn
import visdom
import torch.nn.functional as F
import torchvision.transforms as TF
import json
from types import SimpleNamespace
import re
import cv2
from PIL import Image
import copy
from torch.utils.data import Dataset, DataLoader
import math
import matplotlib.pyplot as plt
import seaborn as sns
import statsmodels.api as sm
import collections
from dateutil.parser import parse
import datetime
from IPython.core.debugger import set_trace
from opts import parse_opts
import imgviz

# load config file for demo
opt = parse_opts()
opt.input_file = "/data/hossay/GAITRite-dataset/v2/person_detection_and_tracking_results.csv"
opt.target_file = "/data/hossay/GAITRite-dataset/v2/gaitrite_full_dataset.xlsx"
opt.backbone = "r2plus1d_18"
opt.model_depth = 18
opt.pretrained_path = ""
opt.data_root = "/data/hossay/GAITRite-dataset/v2/frame/"
opt.ckpt_dir = "../ckpt_repos"
opt.multi_gpu = True
opt.device_ids = "0,"
opt.model_arch = "AGNet"
#opt.model_arch = "R2Plus1D"
opt.bop_refinement = False
opt.with_segmentation = True
opt.training_from_zero = True
opt.sample_size = 112
if opt.model_arch == "R2Plus1D-pretrain":
    opt.test_epoch = 100
elif opt.model_arch == "AGNet-pretrain":
    opt.test_epoch = 100
elif opt.model_arch == "R2Plus1D":
    opt.pretrain_epoch = 100
    opt.test_epoch = 80
elif opt.model_arch == "AGNet":
    opt.pretrain_epoch = 140
    opt.test_epoch = 60
class_names = ['_background_', 'Patient']

# attention indicator
opt.bop_str = 'BOP' if opt.bop_refinement else 'NonBOP'
opt.group_str = f"G{opt.n_groups}" if opt.n_groups > 0 else ''
opt.arch = "{}-{}".format(opt.backbone, opt.model_depth)

target_columns = get_target_columns(opt)

def load_model():
    # define regression model
    net, criterion1, criterion2, optimizer, scheduler = init_state(opt)
    net = load_trained_ckpt(opt, net)
    net.eval()

    return net

def get_transforms_for_inference():

    opt.mean = get_mean(opt.norm_value, dataset=opt.mean_dataset)
    opt.std = get_std(opt.norm_value, dataset=opt.mean_dataset)

    if opt.no_mean_norm and not opt.std_norm:
        norm_method = Normalize([0, 0, 0], [1, 1, 1])
    else:
        norm_method = Normalize(opt.mean, opt.std)

    if opt.train_crop == "random":
        crop_method = MultiScaleRandomCrop(opt.scales, opt.sample_size)
    elif opt.train_crop == "corner":
        crop_method = MultiScaleCornerCrop(opt.scales, opt.sample_size)
    elif opt.train_crop == "center":
        crop_method = MultiScaleCornerCrop(
            opt.scales, opt.sample_size, crop_positions=["c"]
        )

    spatial_transform = {
        "train": Compose(
            [
                TF.RandomRotation(degrees=(0, 0)),
                TF.RandomResizedCrop(size=opt.sample_size,
                                        scale=(opt.sample_size/opt.img_size, 1.0)),
                ToTensor(opt.norm_value),
                norm_method,
            ]
        ),
        "test": Compose(
            [
                TF.CenterCrop(opt.sample_size),
                ToTensor(opt.norm_value),
                norm_method,
            ]
        ),
    }

    temporal_transform = {
        "train": None,  # TemporalRandomCrop(opt.sample_duration),
        "test": None,  # TemporalCenterCrop(opt.sample_duration),
    }

    from sklearn.preprocessing import MinMaxScaler, FunctionTransformer, StandardScaler, QuantileTransformer
    from sklearn.pipeline import Pipeline

    target_transform = StandardScaler()

    model_indicator = '_'.join(filter(lambda x: x != '', [opt.bop_str,
                                                            opt.model_arch,
                                                            opt.merge_type,
                                                            opt.arch,
                                                            opt.group_str]))

    # prepare dataset  (train/test split)
    data = datasets.gaitregression.prepare_dataset(
        input_file=opt.input_file,
        target_file=opt.target_file,
        target_columns=target_columns,
        chunk_parts=opt.chunk_parts,
        target_transform=target_transform,
        legacy_mode=opt.legacy_mode,
        clinical_test_file=opt.clinical_test_file,
    )

    return spatial_transform['test'], target_transform
