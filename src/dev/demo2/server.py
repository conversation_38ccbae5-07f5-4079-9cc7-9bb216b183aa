import json
import pickle

import numpy as np
from flask import Flask, request
from PIL import Image
import torch
from moviepy.editor import Video<PERSON>ileClip
from infer_utils import load_model, get_transforms_for_inference, target_columns
import cv2
from torchvision import transforms

IMG_SIZE = 144
SAMPLE_SIZE = 112
SAMPLE_DURATION = 64

app = Flask(__name__)

net = load_model()
spatial_transforms, target_transform = get_transforms_for_inference()

spatial_transforms.transforms.insert(0, transforms.Resize((IMG_SIZE, IMG_SIZE)))

def center_crop_tensor(image_tensor, target_size=(112, 112)):
    """
    토치 텐서 이미지를 가로세로 크기가 target_size인 중앙으로 crop합니다.

    Parameters:
        image_tensor (torch.Tensor): 입력 이미지 텐서.
        target_size (tuple): (height, width) 형태의 타겟 크기.

    Returns:
        torch.Tensor: center crop된 이미지 텐서.
    """
    # 입력 이미지의 크기를 가져옵니다.
    _, height, width = image_tensor.shape

    # crop할 좌상단 좌표를 계산합니다.
    top = (height - target_size[0]) // 2
    left = (width - target_size[1]) // 2

    # 중앙 crop을 수행합니다.
    cropped_image_tensor = image_tensor[:, top:top + target_size[0], left:left + target_size[1]]

    return cropped_image_tensor

from decord import VideoReader, cpu

def sample_frames(video_path, num_samples=64):
    vr = VideoReader(video_path, ctx=cpu(0))
    total_frames = len(vr)

    # 균일하게 샘플링할 프레임 시간 계산
    sample_times = np.linspace(0, total_frames, num_samples, endpoint=False).astype(int)

    sampled_frames = []
    for t in sample_times:
        frame = vr[t].asnumpy()
        img = Image.fromarray(frame)
        img_t = spatial_transforms(img)
        img_t = center_crop_tensor(img_t, target_size=(SAMPLE_SIZE, SAMPLE_SIZE))
        sampled_frames.append(img_t)

    return sampled_frames

def json_serialize(obj):
    if isinstance(obj, (np.float32, np.float64, np.int32, np.int64)):
        return obj.item()
    raise TypeError(f"Type {type(obj)} not serializable")

@app.route('/api/v1/gait', methods=['POST'])
def api():
    video_file = request.files['video_file']
    imgs = sample_frames(video_file, SAMPLE_DURATION)

    imgs = torch.stack([img for img in imgs])
    imgs = imgs.permute(1, 0, 2, 3).unsqueeze(0).cuda()  # (1,C,D,H,W)
    with torch.no_grad():
        outputs, *_ = net(imgs)
    outputs = target_transform.inverse_transform(outputs.detach().cpu().numpy())
    
    outputs = { "gait_parameters": {k: v for k, v in zip(target_columns, outputs[0])} }
    res = json.dumps(outputs, default=json_serialize)
    
    return res

if __name__ == '__main__':
    app.run(port=5002, host="0.0.0.0")
