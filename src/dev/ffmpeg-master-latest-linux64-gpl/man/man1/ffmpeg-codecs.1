.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "FFMPEG-CODECS 1"
.TH FFMPEG-CODECS 1 " " " " " "
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
ffmpeg\-codecs \- FFmpeg codecs
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This document describes the codecs (decoders and encoders) provided by
the libavcodec library.
.SH "CODEC OPTIONS"
.IX Header "CODEC OPTIONS"
libavcodec provides some generic global options, which can be set on
all the encoders and decoders. In addition, each codec may support
so-called private options, which are specific for a given codec.
.PP
Sometimes, a global option may only affect a specific kind of codec,
and may be nonsensical or ignored by another, so you need to be aware
of the meaning of the specified options. Also some options are
meant only for decoding or encoding.
.PP
Options may be set by specifying \-\fIoption\fR \fIvalue\fR in the
FFmpeg tools, or by setting the value explicitly in the
\&\f(CW\*(C`AVCodecContext\*(C'\fR options or using the \fIlibavutil/opt.h\fR API
for programmatic use.
.PP
The list of supported options follow:
.IP "\fBb\fR \fIinteger\fR \fB(\fR\fIencoding,audio,video\fR\fB)\fR" 4
.IX Item "b integer (encoding,audio,video)"
Set bitrate in bits/s. Default value is 200K.
.IP "\fBab\fR \fIinteger\fR \fB(\fR\fIencoding,audio\fR\fB)\fR" 4
.IX Item "ab integer (encoding,audio)"
Set audio bitrate (in bits/s). Default value is 128K.
.IP "\fBbt\fR \fIinteger\fR \fB(\fR\fIencoding,video\fR\fB)\fR" 4
.IX Item "bt integer (encoding,video)"
Set video bitrate tolerance (in bits/s). In 1\-pass mode, bitrate
tolerance specifies how far ratecontrol is willing to deviate from the
target average bitrate value. This is not related to min/max
bitrate. Lowering tolerance too much has an adverse effect on quality.
.IP "\fBflags\fR \fIflags\fR \fB(\fR\fIdecoding/encoding,audio,video,subtitles\fR\fB)\fR" 4
.IX Item "flags flags (decoding/encoding,audio,video,subtitles)"
Set generic flags.
.Sp
Possible values:
.RS 4
.IP \fBmv4\fR 4
.IX Item "mv4"
Use four motion vector by macroblock (mpeg4).
.IP \fBqpel\fR 4
.IX Item "qpel"
Use 1/4 pel motion compensation.
.IP \fBloop\fR 4
.IX Item "loop"
Use loop filter.
.IP \fBqscale\fR 4
.IX Item "qscale"
Use fixed qscale.
.IP \fBpass1\fR 4
.IX Item "pass1"
Use internal 2pass ratecontrol in first pass mode.
.IP \fBpass2\fR 4
.IX Item "pass2"
Use internal 2pass ratecontrol in second pass mode.
.IP \fBgray\fR 4
.IX Item "gray"
Only decode/encode grayscale.
.IP \fBpsnr\fR 4
.IX Item "psnr"
Set error[?] variables during encoding.
.IP \fBtruncated\fR 4
.IX Item "truncated"
Input bitstream might be randomly truncated.
.IP \fBdrop_changed\fR 4
.IX Item "drop_changed"
Don't output frames whose parameters differ from first decoded frame in stream.
Error AVERROR_INPUT_CHANGED is returned when a frame is dropped.
.IP \fBildct\fR 4
.IX Item "ildct"
Use interlaced DCT.
.IP \fBlow_delay\fR 4
.IX Item "low_delay"
Force low delay.
.IP \fBglobal_header\fR 4
.IX Item "global_header"
Place global headers in extradata instead of every keyframe.
.IP \fBbitexact\fR 4
.IX Item "bitexact"
Only write platform\-, build\- and time-independent data. (except (I)DCT).
This ensures that file and data checksums are reproducible and match between
platforms. Its primary use is for regression testing.
.IP \fBaic\fR 4
.IX Item "aic"
Apply H263 advanced intra coding / mpeg4 ac prediction.
.IP \fBilme\fR 4
.IX Item "ilme"
Apply interlaced motion estimation.
.IP \fBcgop\fR 4
.IX Item "cgop"
Use closed gop.
.IP \fBoutput_corrupt\fR 4
.IX Item "output_corrupt"
Output even potentially corrupted frames.
.RE
.RS 4
.RE
.IP "\fBtime_base\fR \fIrational number\fR" 4
.IX Item "time_base rational number"
Set codec time base.
.Sp
It is the fundamental unit of time (in seconds) in terms of which
frame timestamps are represented. For fixed-fps content, timebase
should be \f(CW\*(C`1 / frame_rate\*(C'\fR and timestamp increments should be
identically 1.
.IP "\fBg\fR \fIinteger\fR \fB(\fR\fIencoding,video\fR\fB)\fR" 4
.IX Item "g integer (encoding,video)"
Set the group of picture (GOP) size. Default value is 12.
.IP "\fBar\fR \fIinteger\fR \fB(\fR\fIdecoding/encoding,audio\fR\fB)\fR" 4
.IX Item "ar integer (decoding/encoding,audio)"
Set audio sampling rate (in Hz).
.IP "\fBac\fR \fIinteger\fR \fB(\fR\fIdecoding/encoding,audio\fR\fB)\fR" 4
.IX Item "ac integer (decoding/encoding,audio)"
Set number of audio channels.
.IP "\fBcutoff\fR \fIinteger\fR \fB(\fR\fIencoding,audio\fR\fB)\fR" 4
.IX Item "cutoff integer (encoding,audio)"
Set cutoff bandwidth. (Supported only by selected encoders, see
their respective documentation sections.)
.IP "\fBframe_size\fR \fIinteger\fR \fB(\fR\fIencoding,audio\fR\fB)\fR" 4
.IX Item "frame_size integer (encoding,audio)"
Set audio frame size.
.Sp
Each submitted frame except the last must contain exactly frame_size
samples per channel. May be 0 when the codec has
CODEC_CAP_VARIABLE_FRAME_SIZE set, in that case the frame size is not
restricted. It is set by some decoders to indicate constant frame
size.
.IP "\fBframe_number\fR \fIinteger\fR" 4
.IX Item "frame_number integer"
Set the frame number.
.IP "\fBdelay\fR \fIinteger\fR" 4
.IX Item "delay integer"
.PD 0
.IP "\fBqcomp\fR \fIfloat\fR \fB(\fR\fIencoding,video\fR\fB)\fR" 4
.IX Item "qcomp float (encoding,video)"
.PD
Set video quantizer scale compression (VBR). It is used as a constant
in the ratecontrol equation. Recommended range for default rc_eq:
0.0\-1.0.
.IP "\fBqblur\fR \fIfloat\fR \fB(\fR\fIencoding,video\fR\fB)\fR" 4
.IX Item "qblur float (encoding,video)"
Set video quantizer scale blur (VBR).
.IP "\fBqmin\fR \fIinteger\fR \fB(\fR\fIencoding,video\fR\fB)\fR" 4
.IX Item "qmin integer (encoding,video)"
Set min video quantizer scale (VBR). Must be included between \-1 and
69, default value is 2.
.IP "\fBqmax\fR \fIinteger\fR \fB(\fR\fIencoding,video\fR\fB)\fR" 4
.IX Item "qmax integer (encoding,video)"
Set max video quantizer scale (VBR). Must be included between \-1 and
1024, default value is 31.
.IP "\fBqdiff\fR \fIinteger\fR \fB(\fR\fIencoding,video\fR\fB)\fR" 4
.IX Item "qdiff integer (encoding,video)"
Set max difference between the quantizer scale (VBR).
.IP "\fBbf\fR \fIinteger\fR \fB(\fR\fIencoding,video\fR\fB)\fR" 4
.IX Item "bf integer (encoding,video)"
Set max number of B frames between non-B-frames.
.Sp
Must be an integer between \-1 and 16. 0 means that B\-frames are
disabled. If a value of \-1 is used, it will choose an automatic value
depending on the encoder.
.Sp
Default value is 0.
.IP "\fBb_qfactor\fR \fIfloat\fR \fB(\fR\fIencoding,video\fR\fB)\fR" 4
.IX Item "b_qfactor float (encoding,video)"
Set qp factor between P and B frames.
.IP "\fBcodec_tag\fR \fIinteger\fR" 4
.IX Item "codec_tag integer"
.PD 0
.IP "\fBbug\fR \fIflags\fR \fB(\fR\fIdecoding,video\fR\fB)\fR" 4
.IX Item "bug flags (decoding,video)"
.PD
Workaround not auto detected encoder bugs.
.Sp
Possible values:
.RS 4
.IP \fBautodetect\fR 4
.IX Item "autodetect"
.PD 0
.IP \fBxvid_ilace\fR 4
.IX Item "xvid_ilace"
.PD
Xvid interlacing bug (autodetected if fourcc==XVIX)
.IP \fBump4\fR 4
.IX Item "ump4"
(autodetected if fourcc==UMP4)
.IP \fBno_padding\fR 4
.IX Item "no_padding"
padding bug (autodetected)
.IP \fBamv\fR 4
.IX Item "amv"
.PD 0
.IP \fBqpel_chroma\fR 4
.IX Item "qpel_chroma"
.IP \fBstd_qpel\fR 4
.IX Item "std_qpel"
.PD
old standard qpel (autodetected per fourcc/version)
.IP \fBqpel_chroma2\fR 4
.IX Item "qpel_chroma2"
.PD 0
.IP \fBdirect_blocksize\fR 4
.IX Item "direct_blocksize"
.PD
direct-qpel-blocksize bug (autodetected per fourcc/version)
.IP \fBedge\fR 4
.IX Item "edge"
edge padding bug (autodetected per fourcc/version)
.IP \fBhpel_chroma\fR 4
.IX Item "hpel_chroma"
.PD 0
.IP \fBdc_clip\fR 4
.IX Item "dc_clip"
.IP \fBms\fR 4
.IX Item "ms"
.PD
Workaround various bugs in microsoft broken decoders.
.IP \fBtrunc\fR 4
.IX Item "trunc"
trancated frames
.RE
.RS 4
.RE
.IP "\fBstrict\fR \fIinteger\fR \fB(\fR\fIdecoding/encoding,audio,video\fR\fB)\fR" 4
.IX Item "strict integer (decoding/encoding,audio,video)"
Specify how strictly to follow the standards.
.Sp
Possible values:
.RS 4
.IP \fBvery\fR 4
.IX Item "very"
strictly conform to an older more strict version of the spec or reference software
.IP \fBstrict\fR 4
.IX Item "strict"
strictly conform to all the things in the spec no matter what consequences
.IP \fBnormal\fR 4
.IX Item "normal"
.PD 0
.IP \fBunofficial\fR 4
.IX Item "unofficial"
.PD
allow unofficial extensions
.IP \fBexperimental\fR 4
.IX Item "experimental"
allow non standardized experimental things, experimental
(unfinished/work in progress/not well tested) decoders and encoders.
Note: experimental decoders can pose a security risk, do not use this for
decoding untrusted input.
.RE
.RS 4
.RE
.IP "\fBb_qoffset\fR \fIfloat\fR \fB(\fR\fIencoding,video\fR\fB)\fR" 4
.IX Item "b_qoffset float (encoding,video)"
Set QP offset between P and B frames.
.IP "\fBerr_detect\fR \fIflags\fR \fB(\fR\fIdecoding,audio,video\fR\fB)\fR" 4
.IX Item "err_detect flags (decoding,audio,video)"
Set error detection flags.
.Sp
Possible values:
.RS 4
.IP \fBcrccheck\fR 4
.IX Item "crccheck"
verify embedded CRCs
.IP \fBbitstream\fR 4
.IX Item "bitstream"
detect bitstream specification deviations
.IP \fBbuffer\fR 4
.IX Item "buffer"
detect improper bitstream length
.IP \fBexplode\fR 4
.IX Item "explode"
abort decoding on minor error detection
.IP \fBignore_err\fR 4
.IX Item "ignore_err"
ignore decoding errors, and continue decoding.
This is useful if you want to analyze the content of a video and thus want
everything to be decoded no matter what. This option will not result in a video
that is pleasing to watch in case of errors.
.IP \fBcareful\fR 4
.IX Item "careful"
consider things that violate the spec and have not been seen in the wild as errors
.IP \fBcompliant\fR 4
.IX Item "compliant"
consider all spec non compliancies as errors
.IP \fBaggressive\fR 4
.IX Item "aggressive"
consider things that a sane encoder should not do as an error
.RE
.RS 4
.RE
.IP "\fBhas_b_frames\fR \fIinteger\fR" 4
.IX Item "has_b_frames integer"
.PD 0
.IP "\fBblock_align\fR \fIinteger\fR" 4
.IX Item "block_align integer"
.IP "\fBrc_override_count\fR \fIinteger\fR" 4
.IX Item "rc_override_count integer"
.IP "\fBmaxrate\fR \fIinteger\fR \fB(\fR\fIencoding,audio,video\fR\fB)\fR" 4
.IX Item "maxrate integer (encoding,audio,video)"
.PD
Set max bitrate tolerance (in bits/s). Requires bufsize to be set.
.IP "\fBminrate\fR \fIinteger\fR \fB(\fR\fIencoding,audio,video\fR\fB)\fR" 4
.IX Item "minrate integer (encoding,audio,video)"
Set min bitrate tolerance (in bits/s). Most useful in setting up a CBR
encode. It is of little use elsewise.
.IP "\fBbufsize\fR \fIinteger\fR \fB(\fR\fIencoding,audio,video\fR\fB)\fR" 4
.IX Item "bufsize integer (encoding,audio,video)"
Set ratecontrol buffer size (in bits).
.IP "\fBi_qfactor\fR \fIfloat\fR \fB(\fR\fIencoding,video\fR\fB)\fR" 4
.IX Item "i_qfactor float (encoding,video)"
Set QP factor between P and I frames.
.IP "\fBi_qoffset\fR \fIfloat\fR \fB(\fR\fIencoding,video\fR\fB)\fR" 4
.IX Item "i_qoffset float (encoding,video)"
Set QP offset between P and I frames.
.IP "\fBdct\fR \fIinteger\fR \fB(\fR\fIencoding,video\fR\fB)\fR" 4
.IX Item "dct integer (encoding,video)"
Set DCT algorithm.
.Sp
Possible values:
.RS 4
.IP \fBauto\fR 4
.IX Item "auto"
autoselect a good one (default)
.IP \fBfastint\fR 4
.IX Item "fastint"
fast integer
.IP \fBint\fR 4
.IX Item "int"
accurate integer
.IP \fBmmx\fR 4
.IX Item "mmx"
.PD 0
.IP \fBaltivec\fR 4
.IX Item "altivec"
.IP \fBfaan\fR 4
.IX Item "faan"
.PD
floating point AAN DCT
.RE
.RS 4
.RE
.IP "\fBlumi_mask\fR \fIfloat\fR \fB(\fR\fIencoding,video\fR\fB)\fR" 4
.IX Item "lumi_mask float (encoding,video)"
Compress bright areas stronger than medium ones.
.IP "\fBtcplx_mask\fR \fIfloat\fR \fB(\fR\fIencoding,video\fR\fB)\fR" 4
.IX Item "tcplx_mask float (encoding,video)"
Set temporal complexity masking.
.IP "\fBscplx_mask\fR \fIfloat\fR \fB(\fR\fIencoding,video\fR\fB)\fR" 4
.IX Item "scplx_mask float (encoding,video)"
Set spatial complexity masking.
.IP "\fBp_mask\fR \fIfloat\fR \fB(\fR\fIencoding,video\fR\fB)\fR" 4
.IX Item "p_mask float (encoding,video)"
Set inter masking.
.IP "\fBdark_mask\fR \fIfloat\fR \fB(\fR\fIencoding,video\fR\fB)\fR" 4
.IX Item "dark_mask float (encoding,video)"
Compress dark areas stronger than medium ones.
.IP "\fBidct\fR \fIinteger\fR \fB(\fR\fIdecoding/encoding,video\fR\fB)\fR" 4
.IX Item "idct integer (decoding/encoding,video)"
Select IDCT implementation.
.Sp
Possible values:
.RS 4
.IP \fBauto\fR 4
.IX Item "auto"
.PD 0
.IP \fBint\fR 4
.IX Item "int"
.IP \fBsimple\fR 4
.IX Item "simple"
.IP \fBsimplemmx\fR 4
.IX Item "simplemmx"
.IP \fBsimpleauto\fR 4
.IX Item "simpleauto"
.PD
Automatically pick a IDCT compatible with the simple one
.IP \fBarm\fR 4
.IX Item "arm"
.PD 0
.IP \fBaltivec\fR 4
.IX Item "altivec"
.IP \fBsh4\fR 4
.IX Item "sh4"
.IP \fBsimplearm\fR 4
.IX Item "simplearm"
.IP \fBsimplearmv5te\fR 4
.IX Item "simplearmv5te"
.IP \fBsimplearmv6\fR 4
.IX Item "simplearmv6"
.IP \fBsimpleneon\fR 4
.IX Item "simpleneon"
.IP \fBxvid\fR 4
.IX Item "xvid"
.IP \fBfaani\fR 4
.IX Item "faani"
.PD
floating point AAN IDCT
.RE
.RS 4
.RE
.IP "\fBslice_count\fR \fIinteger\fR" 4
.IX Item "slice_count integer"
.PD 0
.IP "\fBec\fR \fIflags\fR \fB(\fR\fIdecoding,video\fR\fB)\fR" 4
.IX Item "ec flags (decoding,video)"
.PD
Set error concealment strategy.
.Sp
Possible values:
.RS 4
.IP \fBguess_mvs\fR 4
.IX Item "guess_mvs"
iterative motion vector (MV) search (slow)
.IP \fBdeblock\fR 4
.IX Item "deblock"
use strong deblock filter for damaged MBs
.IP \fBfavor_inter\fR 4
.IX Item "favor_inter"
favor predicting from the previous frame instead of the current
.RE
.RS 4
.RE
.IP "\fBbits_per_coded_sample\fR \fIinteger\fR" 4
.IX Item "bits_per_coded_sample integer"
.PD 0
.IP "\fBaspect\fR \fIrational number\fR \fB(\fR\fIencoding,video\fR\fB)\fR" 4
.IX Item "aspect rational number (encoding,video)"
.PD
Set sample aspect ratio.
.IP "\fBsar\fR \fIrational number\fR \fB(\fR\fIencoding,video\fR\fB)\fR" 4
.IX Item "sar rational number (encoding,video)"
Set sample aspect ratio. Alias to \fIaspect\fR.
.IP "\fBdebug\fR \fIflags\fR \fB(\fR\fIdecoding/encoding,audio,video,subtitles\fR\fB)\fR" 4
.IX Item "debug flags (decoding/encoding,audio,video,subtitles)"
Print specific debug info.
.Sp
Possible values:
.RS 4
.IP \fBpict\fR 4
.IX Item "pict"
picture info
.IP \fBrc\fR 4
.IX Item "rc"
rate control
.IP \fBbitstream\fR 4
.IX Item "bitstream"
.PD 0
.IP \fBmb_type\fR 4
.IX Item "mb_type"
.PD
macroblock (MB) type
.IP \fBqp\fR 4
.IX Item "qp"
per-block quantization parameter (QP)
.IP \fBdct_coeff\fR 4
.IX Item "dct_coeff"
.PD 0
.IP \fBgreen_metadata\fR 4
.IX Item "green_metadata"
.PD
display complexity metadata for the upcoming frame, GoP or for a given duration.
.IP \fBskip\fR 4
.IX Item "skip"
.PD 0
.IP \fBstartcode\fR 4
.IX Item "startcode"
.IP \fBer\fR 4
.IX Item "er"
.PD
error recognition
.IP \fBmmco\fR 4
.IX Item "mmco"
memory management control operations (H.264)
.IP \fBbugs\fR 4
.IX Item "bugs"
.PD 0
.IP \fBbuffers\fR 4
.IX Item "buffers"
.PD
picture buffer allocations
.IP \fBthread_ops\fR 4
.IX Item "thread_ops"
threading operations
.IP \fBnomc\fR 4
.IX Item "nomc"
skip motion compensation
.RE
.RS 4
.RE
.IP "\fBcmp\fR \fIinteger\fR \fB(\fR\fIencoding,video\fR\fB)\fR" 4
.IX Item "cmp integer (encoding,video)"
Set full pel me compare function.
.Sp
Possible values:
.RS 4
.IP \fBsad\fR 4
.IX Item "sad"
sum of absolute differences, fast (default)
.IP \fBsse\fR 4
.IX Item "sse"
sum of squared errors
.IP \fBsatd\fR 4
.IX Item "satd"
sum of absolute Hadamard transformed differences
.IP \fBdct\fR 4
.IX Item "dct"
sum of absolute DCT transformed differences
.IP \fBpsnr\fR 4
.IX Item "psnr"
sum of squared quantization errors (avoid, low quality)
.IP \fBbit\fR 4
.IX Item "bit"
number of bits needed for the block
.IP \fBrd\fR 4
.IX Item "rd"
rate distortion optimal, slow
.IP \fBzero\fR 4
.IX Item "zero"
0
.IP \fBvsad\fR 4
.IX Item "vsad"
sum of absolute vertical differences
.IP \fBvsse\fR 4
.IX Item "vsse"
sum of squared vertical differences
.IP \fBnsse\fR 4
.IX Item "nsse"
noise preserving sum of squared differences
.IP \fBw53\fR 4
.IX Item "w53"
5/3 wavelet, only used in snow
.IP \fBw97\fR 4
.IX Item "w97"
9/7 wavelet, only used in snow
.IP \fBdctmax\fR 4
.IX Item "dctmax"
.PD 0
.IP \fBchroma\fR 4
.IX Item "chroma"
.RE
.RS 4
.RE
.IP "\fBsubcmp\fR \fIinteger\fR \fB(\fR\fIencoding,video\fR\fB)\fR" 4
.IX Item "subcmp integer (encoding,video)"
.PD
Set sub pel me compare function.
.Sp
Possible values:
.RS 4
.IP \fBsad\fR 4
.IX Item "sad"
sum of absolute differences, fast (default)
.IP \fBsse\fR 4
.IX Item "sse"
sum of squared errors
.IP \fBsatd\fR 4
.IX Item "satd"
sum of absolute Hadamard transformed differences
.IP \fBdct\fR 4
.IX Item "dct"
sum of absolute DCT transformed differences
.IP \fBpsnr\fR 4
.IX Item "psnr"
sum of squared quantization errors (avoid, low quality)
.IP \fBbit\fR 4
.IX Item "bit"
number of bits needed for the block
.IP \fBrd\fR 4
.IX Item "rd"
rate distortion optimal, slow
.IP \fBzero\fR 4
.IX Item "zero"
0
.IP \fBvsad\fR 4
.IX Item "vsad"
sum of absolute vertical differences
.IP \fBvsse\fR 4
.IX Item "vsse"
sum of squared vertical differences
.IP \fBnsse\fR 4
.IX Item "nsse"
noise preserving sum of squared differences
.IP \fBw53\fR 4
.IX Item "w53"
5/3 wavelet, only used in snow
.IP \fBw97\fR 4
.IX Item "w97"
9/7 wavelet, only used in snow
.IP \fBdctmax\fR 4
.IX Item "dctmax"
.PD 0
.IP \fBchroma\fR 4
.IX Item "chroma"
.RE
.RS 4
.RE
.IP "\fBmbcmp\fR \fIinteger\fR \fB(\fR\fIencoding,video\fR\fB)\fR" 4
.IX Item "mbcmp integer (encoding,video)"
.PD
Set macroblock compare function.
.Sp
Possible values:
.RS 4
.IP \fBsad\fR 4
.IX Item "sad"
sum of absolute differences, fast (default)
.IP \fBsse\fR 4
.IX Item "sse"
sum of squared errors
.IP \fBsatd\fR 4
.IX Item "satd"
sum of absolute Hadamard transformed differences
.IP \fBdct\fR 4
.IX Item "dct"
sum of absolute DCT transformed differences
.IP \fBpsnr\fR 4
.IX Item "psnr"
sum of squared quantization errors (avoid, low quality)
.IP \fBbit\fR 4
.IX Item "bit"
number of bits needed for the block
.IP \fBrd\fR 4
.IX Item "rd"
rate distortion optimal, slow
.IP \fBzero\fR 4
.IX Item "zero"
0
.IP \fBvsad\fR 4
.IX Item "vsad"
sum of absolute vertical differences
.IP \fBvsse\fR 4
.IX Item "vsse"
sum of squared vertical differences
.IP \fBnsse\fR 4
.IX Item "nsse"
noise preserving sum of squared differences
.IP \fBw53\fR 4
.IX Item "w53"
5/3 wavelet, only used in snow
.IP \fBw97\fR 4
.IX Item "w97"
9/7 wavelet, only used in snow
.IP \fBdctmax\fR 4
.IX Item "dctmax"
.PD 0
.IP \fBchroma\fR 4
.IX Item "chroma"
.RE
.RS 4
.RE
.IP "\fBildctcmp\fR \fIinteger\fR \fB(\fR\fIencoding,video\fR\fB)\fR" 4
.IX Item "ildctcmp integer (encoding,video)"
.PD
Set interlaced dct compare function.
.Sp
Possible values:
.RS 4
.IP \fBsad\fR 4
.IX Item "sad"
sum of absolute differences, fast (default)
.IP \fBsse\fR 4
.IX Item "sse"
sum of squared errors
.IP \fBsatd\fR 4
.IX Item "satd"
sum of absolute Hadamard transformed differences
.IP \fBdct\fR 4
.IX Item "dct"
sum of absolute DCT transformed differences
.IP \fBpsnr\fR 4
.IX Item "psnr"
sum of squared quantization errors (avoid, low quality)
.IP \fBbit\fR 4
.IX Item "bit"
number of bits needed for the block
.IP \fBrd\fR 4
.IX Item "rd"
rate distortion optimal, slow
.IP \fBzero\fR 4
.IX Item "zero"
0
.IP \fBvsad\fR 4
.IX Item "vsad"
sum of absolute vertical differences
.IP \fBvsse\fR 4
.IX Item "vsse"
sum of squared vertical differences
.IP \fBnsse\fR 4
.IX Item "nsse"
noise preserving sum of squared differences
.IP \fBw53\fR 4
.IX Item "w53"
5/3 wavelet, only used in snow
.IP \fBw97\fR 4
.IX Item "w97"
9/7 wavelet, only used in snow
.IP \fBdctmax\fR 4
.IX Item "dctmax"
.PD 0
.IP \fBchroma\fR 4
.IX Item "chroma"
.RE
.RS 4
.RE
.IP "\fBdia_size\fR \fIinteger\fR \fB(\fR\fIencoding,video\fR\fB)\fR" 4
.IX Item "dia_size integer (encoding,video)"
.PD
Set diamond type & size for motion estimation.
.RS 4
.IP "\fB(1024, INT_MAX)\fR" 4
.IX Item "(1024, INT_MAX)"
full motion estimation(slowest)
.IP "\fB(768, 1024]\fR" 4
.IX Item "(768, 1024]"
umh motion estimation
.IP "\fB(512, 768]\fR" 4
.IX Item "(512, 768]"
hex motion estimation
.IP "\fB(256, 512]\fR" 4
.IX Item "(256, 512]"
l2s diamond motion estimation
.IP \fB[2,256]\fR 4
.IX Item "[2,256]"
var diamond motion estimation
.IP "\fB(\-1,  2)\fR" 4
.IX Item "(-1, 2)"
small diamond motion estimation
.IP \fB\-1\fR 4
.IX Item "-1"
funny diamond motion estimation
.IP "\fB(INT_MIN, \-1)\fR" 4
.IX Item "(INT_MIN, -1)"
sab diamond motion estimation
.RE
.RS 4
.RE
.IP "\fBlast_pred\fR \fIinteger\fR \fB(\fR\fIencoding,video\fR\fB)\fR" 4
.IX Item "last_pred integer (encoding,video)"
Set amount of motion predictors from the previous frame.
.IP "\fBprecmp\fR \fIinteger\fR \fB(\fR\fIencoding,video\fR\fB)\fR" 4
.IX Item "precmp integer (encoding,video)"
Set pre motion estimation compare function.
.Sp
Possible values:
.RS 4
.IP \fBsad\fR 4
.IX Item "sad"
sum of absolute differences, fast (default)
.IP \fBsse\fR 4
.IX Item "sse"
sum of squared errors
.IP \fBsatd\fR 4
.IX Item "satd"
sum of absolute Hadamard transformed differences
.IP \fBdct\fR 4
.IX Item "dct"
sum of absolute DCT transformed differences
.IP \fBpsnr\fR 4
.IX Item "psnr"
sum of squared quantization errors (avoid, low quality)
.IP \fBbit\fR 4
.IX Item "bit"
number of bits needed for the block
.IP \fBrd\fR 4
.IX Item "rd"
rate distortion optimal, slow
.IP \fBzero\fR 4
.IX Item "zero"
0
.IP \fBvsad\fR 4
.IX Item "vsad"
sum of absolute vertical differences
.IP \fBvsse\fR 4
.IX Item "vsse"
sum of squared vertical differences
.IP \fBnsse\fR 4
.IX Item "nsse"
noise preserving sum of squared differences
.IP \fBw53\fR 4
.IX Item "w53"
5/3 wavelet, only used in snow
.IP \fBw97\fR 4
.IX Item "w97"
9/7 wavelet, only used in snow
.IP \fBdctmax\fR 4
.IX Item "dctmax"
.PD 0
.IP \fBchroma\fR 4
.IX Item "chroma"
.RE
.RS 4
.RE
.IP "\fBpre_dia_size\fR \fIinteger\fR \fB(\fR\fIencoding,video\fR\fB)\fR" 4
.IX Item "pre_dia_size integer (encoding,video)"
.PD
Set diamond type & size for motion estimation pre-pass.
.IP "\fBsubq\fR \fIinteger\fR \fB(\fR\fIencoding,video\fR\fB)\fR" 4
.IX Item "subq integer (encoding,video)"
Set sub pel motion estimation quality.
.IP "\fBme_range\fR \fIinteger\fR \fB(\fR\fIencoding,video\fR\fB)\fR" 4
.IX Item "me_range integer (encoding,video)"
Set limit motion vectors range (1023 for DivX player).
.IP "\fBglobal_quality\fR \fIinteger\fR \fB(\fR\fIencoding,audio,video\fR\fB)\fR" 4
.IX Item "global_quality integer (encoding,audio,video)"
.PD 0
.IP "\fBslice_flags\fR \fIinteger\fR" 4
.IX Item "slice_flags integer"
.IP "\fBmbd\fR \fIinteger\fR \fB(\fR\fIencoding,video\fR\fB)\fR" 4
.IX Item "mbd integer (encoding,video)"
.PD
Set macroblock decision algorithm (high quality mode).
.Sp
Possible values:
.RS 4
.IP \fBsimple\fR 4
.IX Item "simple"
use mbcmp (default)
.IP \fBbits\fR 4
.IX Item "bits"
use fewest bits
.IP \fBrd\fR 4
.IX Item "rd"
use best rate distortion
.RE
.RS 4
.RE
.IP "\fBrc_init_occupancy\fR \fIinteger\fR \fB(\fR\fIencoding,video\fR\fB)\fR" 4
.IX Item "rc_init_occupancy integer (encoding,video)"
Set number of bits which should be loaded into the rc buffer before
decoding starts.
.IP "\fBflags2\fR \fIflags\fR \fB(\fR\fIdecoding/encoding,audio,video,subtitles\fR\fB)\fR" 4
.IX Item "flags2 flags (decoding/encoding,audio,video,subtitles)"
Possible values:
.RS 4
.IP \fBfast\fR 4
.IX Item "fast"
Allow non spec compliant speedup tricks.
.IP \fBnoout\fR 4
.IX Item "noout"
Skip bitstream encoding.
.IP \fBignorecrop\fR 4
.IX Item "ignorecrop"
Ignore cropping information from sps.
.IP \fBlocal_header\fR 4
.IX Item "local_header"
Place global headers at every keyframe instead of in extradata.
.IP \fBchunks\fR 4
.IX Item "chunks"
Frame data might be split into multiple chunks.
.IP \fBshowall\fR 4
.IX Item "showall"
Show all frames before the first keyframe.
.IP \fBexport_mvs\fR 4
.IX Item "export_mvs"
Export motion vectors into frame side-data (see \f(CW\*(C`AV_FRAME_DATA_MOTION_VECTORS\*(C'\fR)
for codecs that support it. See also \fIdoc/examples/export_mvs.c\fR.
.IP \fBskip_manual\fR 4
.IX Item "skip_manual"
Do not skip samples and export skip information as frame side data.
.IP \fBass_ro_flush_noop\fR 4
.IX Item "ass_ro_flush_noop"
Do not reset ASS ReadOrder field on flush.
.IP \fBicc_profiles\fR 4
.IX Item "icc_profiles"
Generate/parse embedded ICC profiles from/to colorimetry tags.
.RE
.RS 4
.RE
.IP "\fBexport_side_data\fR \fIflags\fR \fB(\fR\fIdecoding/encoding,audio,video,subtitles\fR\fB)\fR" 4
.IX Item "export_side_data flags (decoding/encoding,audio,video,subtitles)"
Possible values:
.RS 4
.IP \fBmvs\fR 4
.IX Item "mvs"
Export motion vectors into frame side-data (see \f(CW\*(C`AV_FRAME_DATA_MOTION_VECTORS\*(C'\fR)
for codecs that support it. See also \fIdoc/examples/export_mvs.c\fR.
.IP \fBprft\fR 4
.IX Item "prft"
Export encoder Producer Reference Time into packet side-data (see \f(CW\*(C`AV_PKT_DATA_PRFT\*(C'\fR)
for codecs that support it.
.IP \fBvenc_params\fR 4
.IX Item "venc_params"
Export video encoding parameters through frame side data (see \f(CW\*(C`AV_FRAME_DATA_VIDEO_ENC_PARAMS\*(C'\fR)
for codecs that support it. At present, those are H.264 and VP9.
.IP \fBfilm_grain\fR 4
.IX Item "film_grain"
Export film grain parameters through frame side data (see \f(CW\*(C`AV_FRAME_DATA_FILM_GRAIN_PARAMS\*(C'\fR).
Supported at present by AV1 decoders.
.IP \fBenhancements\fR 4
.IX Item "enhancements"
Export picture enhancement metadata through frame side data, e.g. LCEVC (see \f(CW\*(C`AV_FRAME_DATA_LCEVC\*(C'\fR).
.RE
.RS 4
.RE
.IP "\fBthreads\fR \fIinteger\fR \fB(\fR\fIdecoding/encoding,video\fR\fB)\fR" 4
.IX Item "threads integer (decoding/encoding,video)"
Set the number of threads to be used, in case the selected codec
implementation supports multi-threading.
.Sp
Possible values:
.RS 4
.IP "\fBauto, 0\fR" 4
.IX Item "auto, 0"
automatically select the number of threads to set
.RE
.RS 4
.Sp
Default value is \fBauto\fR.
.RE
.IP "\fBdc\fR \fIinteger\fR \fB(\fR\fIencoding,video\fR\fB)\fR" 4
.IX Item "dc integer (encoding,video)"
Set intra_dc_precision.
.IP "\fBnssew\fR \fIinteger\fR \fB(\fR\fIencoding,video\fR\fB)\fR" 4
.IX Item "nssew integer (encoding,video)"
Set nsse weight.
.IP "\fBskip_top\fR \fIinteger\fR \fB(\fR\fIdecoding,video\fR\fB)\fR" 4
.IX Item "skip_top integer (decoding,video)"
Set number of macroblock rows at the top which are skipped.
.IP "\fBskip_bottom\fR \fIinteger\fR \fB(\fR\fIdecoding,video\fR\fB)\fR" 4
.IX Item "skip_bottom integer (decoding,video)"
Set number of macroblock rows at the bottom which are skipped.
.IP "\fBprofile\fR \fIinteger\fR \fB(\fR\fIencoding,audio,video\fR\fB)\fR" 4
.IX Item "profile integer (encoding,audio,video)"
Set encoder codec profile. Default value is \fBunknown\fR. Encoder specific
profiles are documented in the relevant encoder documentation.
.IP "\fBlevel\fR \fIinteger\fR \fB(\fR\fIencoding,audio,video\fR\fB)\fR" 4
.IX Item "level integer (encoding,audio,video)"
Set the encoder level. This level depends on the specific codec, and
might correspond to the profile level. It is set by default to
\&\fBunknown\fR.
.Sp
Possible values:
.RS 4
.IP \fBunknown\fR 4
.IX Item "unknown"
.RE
.RS 4
.RE
.PD 0
.IP "\fBlowres\fR \fIinteger\fR \fB(\fR\fIdecoding,audio,video\fR\fB)\fR" 4
.IX Item "lowres integer (decoding,audio,video)"
.PD
Decode at 1= 1/2, 2=1/4, 3=1/8 resolutions.
.IP "\fBmblmin\fR \fIinteger\fR \fB(\fR\fIencoding,video\fR\fB)\fR" 4
.IX Item "mblmin integer (encoding,video)"
Set min macroblock lagrange factor (VBR).
.IP "\fBmblmax\fR \fIinteger\fR \fB(\fR\fIencoding,video\fR\fB)\fR" 4
.IX Item "mblmax integer (encoding,video)"
Set max macroblock lagrange factor (VBR).
.IP "\fBskip_loop_filter\fR \fIinteger\fR \fB(\fR\fIdecoding,video\fR\fB)\fR" 4
.IX Item "skip_loop_filter integer (decoding,video)"
.PD 0
.IP "\fBskip_idct\fR        \fIinteger\fR \fB(\fR\fIdecoding,video\fR\fB)\fR" 4
.IX Item "skip_idct integer (decoding,video)"
.IP "\fBskip_frame\fR       \fIinteger\fR \fB(\fR\fIdecoding,video\fR\fB)\fR" 4
.IX Item "skip_frame integer (decoding,video)"
.PD
Make decoder discard processing depending on the frame type selected
by the option value.
.Sp
\&\fBskip_loop_filter\fR skips frame loop filtering, \fBskip_idct\fR
skips frame IDCT/dequantization, \fBskip_frame\fR skips decoding.
.Sp
Possible values:
.RS 4
.IP \fBnone\fR 4
.IX Item "none"
Discard no frame.
.IP \fBdefault\fR 4
.IX Item "default"
Discard useless frames like 0\-sized frames.
.IP \fBnoref\fR 4
.IX Item "noref"
Discard all non-reference frames.
.IP \fBbidir\fR 4
.IX Item "bidir"
Discard all bidirectional frames.
.IP \fBnokey\fR 4
.IX Item "nokey"
Discard all frames excepts keyframes.
.IP \fBnointra\fR 4
.IX Item "nointra"
Discard all frames except I frames.
.IP \fBall\fR 4
.IX Item "all"
Discard all frames.
.RE
.RS 4
.Sp
Default value is \fBdefault\fR.
.RE
.IP "\fBbidir_refine\fR \fIinteger\fR \fB(\fR\fIencoding,video\fR\fB)\fR" 4
.IX Item "bidir_refine integer (encoding,video)"
Refine the two motion vectors used in bidirectional macroblocks.
.IP "\fBkeyint_min\fR \fIinteger\fR \fB(\fR\fIencoding,video\fR\fB)\fR" 4
.IX Item "keyint_min integer (encoding,video)"
Set minimum interval between IDR-frames.
.IP "\fBrefs\fR \fIinteger\fR \fB(\fR\fIencoding,video\fR\fB)\fR" 4
.IX Item "refs integer (encoding,video)"
Set reference frames to consider for motion compensation.
.IP "\fBtrellis\fR \fIinteger\fR \fB(\fR\fIencoding,audio,video\fR\fB)\fR" 4
.IX Item "trellis integer (encoding,audio,video)"
Set rate-distortion optimal quantization.
.IP "\fBmv0_threshold\fR \fIinteger\fR \fB(\fR\fIencoding,video\fR\fB)\fR" 4
.IX Item "mv0_threshold integer (encoding,video)"
.PD 0
.IP "\fBcompression_level\fR \fIinteger\fR \fB(\fR\fIencoding,audio,video\fR\fB)\fR" 4
.IX Item "compression_level integer (encoding,audio,video)"
.IP "\fBbits_per_raw_sample\fR \fIinteger\fR" 4
.IX Item "bits_per_raw_sample integer"
.IP "\fBchannel_layout\fR \fIinteger\fR \fB(\fR\fIdecoding/encoding,audio\fR\fB)\fR" 4
.IX Item "channel_layout integer (decoding/encoding,audio)"
.PD
See \fBthe Channel Layout section in the ffmpeg\-utils\|(1) manual\fR
for the required syntax.
.IP "\fBrc_max_vbv_use\fR \fIfloat\fR \fB(\fR\fIencoding,video\fR\fB)\fR" 4
.IX Item "rc_max_vbv_use float (encoding,video)"
.PD 0
.IP "\fBrc_min_vbv_use\fR \fIfloat\fR \fB(\fR\fIencoding,video\fR\fB)\fR" 4
.IX Item "rc_min_vbv_use float (encoding,video)"
.IP "\fBcolor_primaries\fR \fIinteger\fR \fB(\fR\fIdecoding/encoding,video\fR\fB)\fR" 4
.IX Item "color_primaries integer (decoding/encoding,video)"
.PD
Possible values:
.RS 4
.IP \fBbt709\fR 4
.IX Item "bt709"
BT.709
.IP \fBbt470m\fR 4
.IX Item "bt470m"
BT.470 M
.IP \fBbt470bg\fR 4
.IX Item "bt470bg"
BT.470 BG
.IP \fBsmpte170m\fR 4
.IX Item "smpte170m"
SMPTE 170 M
.IP \fBsmpte240m\fR 4
.IX Item "smpte240m"
SMPTE 240 M
.IP \fBfilm\fR 4
.IX Item "film"
Film
.IP \fBbt2020\fR 4
.IX Item "bt2020"
BT.2020
.IP \fBsmpte428\fR 4
.IX Item "smpte428"
.PD 0
.IP \fBsmpte428_1\fR 4
.IX Item "smpte428_1"
.PD
SMPTE ST 428\-1
.IP \fBsmpte431\fR 4
.IX Item "smpte431"
SMPTE 431\-2
.IP \fBsmpte432\fR 4
.IX Item "smpte432"
SMPTE 432\-1
.IP \fBjedec\-p22\fR 4
.IX Item "jedec-p22"
JEDEC P22
.RE
.RS 4
.RE
.IP "\fBcolor_trc\fR \fIinteger\fR \fB(\fR\fIdecoding/encoding,video\fR\fB)\fR" 4
.IX Item "color_trc integer (decoding/encoding,video)"
Possible values:
.RS 4
.IP \fBbt709\fR 4
.IX Item "bt709"
BT.709
.IP \fBgamma22\fR 4
.IX Item "gamma22"
BT.470 M
.IP \fBgamma28\fR 4
.IX Item "gamma28"
BT.470 BG
.IP \fBsmpte170m\fR 4
.IX Item "smpte170m"
SMPTE 170 M
.IP \fBsmpte240m\fR 4
.IX Item "smpte240m"
SMPTE 240 M
.IP \fBlinear\fR 4
.IX Item "linear"
Linear
.IP \fBlog\fR 4
.IX Item "log"
.PD 0
.IP \fBlog100\fR 4
.IX Item "log100"
.PD
Log
.IP \fBlog_sqrt\fR 4
.IX Item "log_sqrt"
.PD 0
.IP \fBlog316\fR 4
.IX Item "log316"
.PD
Log square root
.IP \fBiec61966_2_4\fR 4
.IX Item "iec61966_2_4"
.PD 0
.IP \fBiec61966\-2\-4\fR 4
.IX Item "iec61966-2-4"
.PD
IEC 61966\-2\-4
.IP \fBbt1361\fR 4
.IX Item "bt1361"
.PD 0
.IP \fBbt1361e\fR 4
.IX Item "bt1361e"
.PD
BT.1361
.IP \fBiec61966_2_1\fR 4
.IX Item "iec61966_2_1"
.PD 0
.IP \fBiec61966\-2\-1\fR 4
.IX Item "iec61966-2-1"
.PD
IEC 61966\-2\-1
.IP \fBbt2020_10\fR 4
.IX Item "bt2020_10"
.PD 0
.IP \fBbt2020_10bit\fR 4
.IX Item "bt2020_10bit"
.PD
BT.2020 \- 10 bit
.IP \fBbt2020_12\fR 4
.IX Item "bt2020_12"
.PD 0
.IP \fBbt2020_12bit\fR 4
.IX Item "bt2020_12bit"
.PD
BT.2020 \- 12 bit
.IP \fBsmpte2084\fR 4
.IX Item "smpte2084"
SMPTE ST 2084
.IP \fBsmpte428\fR 4
.IX Item "smpte428"
.PD 0
.IP \fBsmpte428_1\fR 4
.IX Item "smpte428_1"
.PD
SMPTE ST 428\-1
.IP \fBarib\-std\-b67\fR 4
.IX Item "arib-std-b67"
ARIB STD\-B67
.RE
.RS 4
.RE
.IP "\fBcolorspace\fR \fIinteger\fR \fB(\fR\fIdecoding/encoding,video\fR\fB)\fR" 4
.IX Item "colorspace integer (decoding/encoding,video)"
Possible values:
.RS 4
.IP \fBrgb\fR 4
.IX Item "rgb"
RGB
.IP \fBbt709\fR 4
.IX Item "bt709"
BT.709
.IP \fBfcc\fR 4
.IX Item "fcc"
FCC
.IP \fBbt470bg\fR 4
.IX Item "bt470bg"
BT.470 BG
.IP \fBsmpte170m\fR 4
.IX Item "smpte170m"
SMPTE 170 M
.IP \fBsmpte240m\fR 4
.IX Item "smpte240m"
SMPTE 240 M
.IP \fBycocg\fR 4
.IX Item "ycocg"
YCOCG
.IP \fBbt2020nc\fR 4
.IX Item "bt2020nc"
.PD 0
.IP \fBbt2020_ncl\fR 4
.IX Item "bt2020_ncl"
.PD
BT.2020 NCL
.IP \fBbt2020c\fR 4
.IX Item "bt2020c"
.PD 0
.IP \fBbt2020_cl\fR 4
.IX Item "bt2020_cl"
.PD
BT.2020 CL
.IP \fBsmpte2085\fR 4
.IX Item "smpte2085"
SMPTE 2085
.IP \fBchroma-derived-nc\fR 4
.IX Item "chroma-derived-nc"
Chroma-derived NCL
.IP \fBchroma-derived-c\fR 4
.IX Item "chroma-derived-c"
Chroma-derived CL
.IP \fBictcp\fR 4
.IX Item "ictcp"
ICtCp
.RE
.RS 4
.RE
.IP "\fBcolor_range\fR \fIinteger\fR \fB(\fR\fIdecoding/encoding,video\fR\fB)\fR" 4
.IX Item "color_range integer (decoding/encoding,video)"
If used as input parameter, it serves as a hint to the decoder, which
color_range the input has.
Possible values:
.RS 4
.IP \fBtv\fR 4
.IX Item "tv"
.PD 0
.IP \fBmpeg\fR 4
.IX Item "mpeg"
.IP \fBlimited\fR 4
.IX Item "limited"
.PD
MPEG (219*2^(n\-8))
.IP \fBpc\fR 4
.IX Item "pc"
.PD 0
.IP \fBjpeg\fR 4
.IX Item "jpeg"
.IP \fBfull\fR 4
.IX Item "full"
.PD
JPEG (2^n\-1)
.RE
.RS 4
.RE
.IP "\fBchroma_sample_location\fR \fIinteger\fR \fB(\fR\fIdecoding/encoding,video\fR\fB)\fR" 4
.IX Item "chroma_sample_location integer (decoding/encoding,video)"
Possible values:
.RS 4
.IP \fBleft\fR 4
.IX Item "left"
.PD 0
.IP \fBcenter\fR 4
.IX Item "center"
.IP \fBtopleft\fR 4
.IX Item "topleft"
.IP \fBtop\fR 4
.IX Item "top"
.IP \fBbottomleft\fR 4
.IX Item "bottomleft"
.IP \fBbottom\fR 4
.IX Item "bottom"
.RE
.RS 4
.RE
.IP "\fBlog_level_offset\fR \fIinteger\fR" 4
.IX Item "log_level_offset integer"
.PD
Set the log level offset.
.IP "\fBslices\fR \fIinteger\fR \fB(\fR\fIencoding,video\fR\fB)\fR" 4
.IX Item "slices integer (encoding,video)"
Number of slices, used in parallelized encoding.
.IP "\fBthread_type\fR \fIflags\fR \fB(\fR\fIdecoding/encoding,video\fR\fB)\fR" 4
.IX Item "thread_type flags (decoding/encoding,video)"
Select which multithreading methods to use.
.Sp
Use of \fBframe\fR will increase decoding delay by one frame per
thread, so clients which cannot provide future frames should not use
it.
.Sp
Possible values:
.RS 4
.IP \fBslice\fR 4
.IX Item "slice"
Decode more than one part of a single frame at once.
.Sp
Multithreading using slices works only when the video was encoded with
slices.
.IP \fBframe\fR 4
.IX Item "frame"
Decode more than one frame at once.
.RE
.RS 4
.Sp
Default value is \fBslice+frame\fR.
.RE
.IP "\fBaudio_service_type\fR \fIinteger\fR \fB(\fR\fIencoding,audio\fR\fB)\fR" 4
.IX Item "audio_service_type integer (encoding,audio)"
Set audio service type.
.Sp
Possible values:
.RS 4
.IP \fBma\fR 4
.IX Item "ma"
Main Audio Service
.IP \fBef\fR 4
.IX Item "ef"
Effects
.IP \fBvi\fR 4
.IX Item "vi"
Visually Impaired
.IP \fBhi\fR 4
.IX Item "hi"
Hearing Impaired
.IP \fBdi\fR 4
.IX Item "di"
Dialogue
.IP \fBco\fR 4
.IX Item "co"
Commentary
.IP \fBem\fR 4
.IX Item "em"
Emergency
.IP \fBvo\fR 4
.IX Item "vo"
Voice Over
.IP \fBka\fR 4
.IX Item "ka"
Karaoke
.RE
.RS 4
.RE
.IP "\fBrequest_sample_fmt\fR \fIsample_fmt\fR \fB(\fR\fIdecoding,audio\fR\fB)\fR" 4
.IX Item "request_sample_fmt sample_fmt (decoding,audio)"
Set sample format audio decoders should prefer. Default value is
\&\f(CW\*(C`none\*(C'\fR.
.IP "\fBpkt_timebase\fR \fIrational number\fR" 4
.IX Item "pkt_timebase rational number"
.PD 0
.IP "\fBsub_charenc\fR \fIencoding\fR \fB(\fR\fIdecoding,subtitles\fR\fB)\fR" 4
.IX Item "sub_charenc encoding (decoding,subtitles)"
.PD
Set the input subtitles character encoding.
.IP "\fBfield_order\fR  \fIfield_order\fR \fB(\fR\fIvideo\fR\fB)\fR" 4
.IX Item "field_order field_order (video)"
Set/override the field order of the video.
Possible values:
.RS 4
.IP \fBprogressive\fR 4
.IX Item "progressive"
Progressive video
.IP \fBtt\fR 4
.IX Item "tt"
Interlaced video, top field coded and displayed first
.IP \fBbb\fR 4
.IX Item "bb"
Interlaced video, bottom field coded and displayed first
.IP \fBtb\fR 4
.IX Item "tb"
Interlaced video, top coded first, bottom displayed first
.IP \fBbt\fR 4
.IX Item "bt"
Interlaced video, bottom coded first, top displayed first
.RE
.RS 4
.RE
.IP "\fBskip_alpha\fR \fIbool\fR \fB(\fR\fIdecoding,video\fR\fB)\fR" 4
.IX Item "skip_alpha bool (decoding,video)"
Set to 1 to disable processing alpha (transparency). This works like the
\&\fBgray\fR flag in the \fBflags\fR option which skips chroma information
instead of alpha. Default is 0.
.IP "\fBcodec_whitelist\fR \fIlist\fR \fB(\fR\fIinput\fR\fB)\fR" 4
.IX Item "codec_whitelist list (input)"
"," separated list of allowed decoders. By default all are allowed.
.IP "\fBdump_separator\fR \fIstring\fR \fB(\fR\fIinput\fR\fB)\fR" 4
.IX Item "dump_separator string (input)"
Separator used to separate the fields printed on the command line about the
Stream parameters.
For example, to separate the fields with newlines and indentation:
.Sp
.Vb 2
\&        ffprobe \-dump_separator "
\&                                  "  \-i ~/videos/matrixbench_mpeg2.mpg
.Ve
.IP "\fBmax_pixels\fR \fIinteger\fR \fB(\fR\fIdecoding/encoding,video\fR\fB)\fR" 4
.IX Item "max_pixels integer (decoding/encoding,video)"
Maximum number of pixels per image. This value can be used to avoid out of
memory failures due to large images.
.IP "\fBapply_cropping\fR \fIbool\fR \fB(\fR\fIdecoding,video\fR\fB)\fR" 4
.IX Item "apply_cropping bool (decoding,video)"
Enable cropping if cropping parameters are multiples of the required
alignment for the left and top parameters. If the alignment is not met the
cropping will be partially applied to maintain alignment.
Default is 1 (enabled).
Note: The required alignment depends on if \f(CW\*(C`AV_CODEC_FLAG_UNALIGNED\*(C'\fR is set and the
CPU. \f(CW\*(C`AV_CODEC_FLAG_UNALIGNED\*(C'\fR cannot be changed from the command line. Also hardware
decoders will not apply left/top Cropping.
.SH DECODERS
.IX Header "DECODERS"
Decoders are configured elements in FFmpeg which allow the decoding of
multimedia streams.
.PP
When you configure your FFmpeg build, all the supported native decoders
are enabled by default. Decoders requiring an external library must be enabled
manually via the corresponding \f(CW\*(C`\-\-enable\-lib\*(C'\fR option. You can list all
available decoders using the configure option \f(CW\*(C`\-\-list\-decoders\*(C'\fR.
.PP
You can disable all the decoders with the configure option
\&\f(CW\*(C`\-\-disable\-decoders\*(C'\fR and selectively enable / disable single decoders
with the options \f(CW\*(C`\-\-enable\-decoder=\fR\f(CIDECODER\fR\f(CW\*(C'\fR /
\&\f(CW\*(C`\-\-disable\-decoder=\fR\f(CIDECODER\fR\f(CW\*(C'\fR.
.PP
The option \f(CW\*(C`\-decoders\*(C'\fR of the ff* tools will display the list of
enabled decoders.
.SH "VIDEO DECODERS"
.IX Header "VIDEO DECODERS"
A description of some of the currently available video decoders
follows.
.SS av1
.IX Subsection "av1"
AOMedia Video 1 (AV1) decoder.
.PP
\fIOptions\fR
.IX Subsection "Options"
.IP \fBoperating_point\fR 4
.IX Item "operating_point"
Select an operating point of a scalable AV1 bitstream (0 \- 31). Default is 0.
.SS hevc
.IX Subsection "hevc"
HEVC (AKA ITU-T H.265 or ISO/IEC 23008\-2) decoder.
.PP
The decoder supports MV-HEVC multiview streams with at most two views. Views to
be output are selected by supplying a list of view IDs to the decoder (the
\&\fBview_ids\fR option). This option may be set either statically before
decoder init, or from the \f(CWget_format()\fR callback \- useful for the case
when the view count or IDs change dynamically during decoding.
.PP
Only the base layer is decoded by default.
.PP
Note that if you are using the \f(CW\*(C`ffmpeg\*(C'\fR CLI tool, you should be using view
specifiers as documented in its manual, rather than the options documented here.
.PP
\fIOptions\fR
.IX Subsection "Options"
.IP "\fBview_ids (MV-HEVC)\fR" 4
.IX Item "view_ids (MV-HEVC)"
Specify a list of view IDs that should be output. This option can also be set to
a single '\-1', which will cause all views defined in the VPS to be decoded and
output.
.IP "\fBview_ids_available (MV-HEVC)\fR" 4
.IX Item "view_ids_available (MV-HEVC)"
This option may be read by the caller to retrieve an array of view IDs available
in the active VPS. The array is empty for single-layer video.
.Sp
The value of this option is guaranteed to be accurate when read from the
\&\f(CWget_format()\fR callback. It may also be set at other times (e.g. after
opening the decoder), but the value is informational only and may be incorrect
(e.g. when the stream contains multiple distinct VPS NALUs).
.IP "\fBview_pos_available (MV-HEVC)\fR" 4
.IX Item "view_pos_available (MV-HEVC)"
This option may be read by the caller to retrieve an array of view positions
(left, right, or unspecified) available in the active VPS, as
\&\f(CW\*(C`AVStereo3DView\*(C'\fR values. When the array is available, its elements apply to
the corresponding elements of \fBview_ids_available\fR, i.e.
\&\f(CW\*(C`view_pos_available[i]\*(C'\fR contains the position of view with ID
\&\f(CW\*(C`view_ids_available[i]\*(C'\fR.
.Sp
Same validity restrictions as for \fBview_ids_available\fR apply to
this option.
.SS rawvideo
.IX Subsection "rawvideo"
Raw video decoder.
.PP
This decoder decodes rawvideo streams.
.PP
\fIOptions\fR
.IX Subsection "Options"
.IP "\fBtop\fR \fItop_field_first\fR" 4
.IX Item "top top_field_first"
Specify the assumed field type of the input video.
.RS 4
.IP \fB\-1\fR 4
.IX Item "-1"
the video is assumed to be progressive (default)
.IP \fB0\fR 4
.IX Item "0"
bottom-field-first is assumed
.IP \fB1\fR 4
.IX Item "1"
top-field-first is assumed
.RE
.RS 4
.RE
.SS libdav1d
.IX Subsection "libdav1d"
dav1d AV1 decoder.
.PP
libdav1d allows libavcodec to decode the AOMedia Video 1 (AV1) codec.
Requires the presence of the libdav1d headers and library during configuration.
You need to explicitly configure the build with \f(CW\*(C`\-\-enable\-libdav1d\*(C'\fR.
.PP
\fIOptions\fR
.IX Subsection "Options"
.PP
The following options are supported by the libdav1d wrapper.
.IP \fBframethreads\fR 4
.IX Item "framethreads"
Set amount of frame threads to use during decoding. The default value is 0 (autodetect).
This option is deprecated for libdav1d >= 1.0 and will be removed in the future. Use the
option \f(CW\*(C`max_frame_delay\*(C'\fR and the global option \f(CW\*(C`threads\*(C'\fR instead.
.IP \fBtilethreads\fR 4
.IX Item "tilethreads"
Set amount of tile threads to use during decoding. The default value is 0 (autodetect).
This option is deprecated for libdav1d >= 1.0 and will be removed in the future. Use the
global option \f(CW\*(C`threads\*(C'\fR instead.
.IP \fBmax_frame_delay\fR 4
.IX Item "max_frame_delay"
Set max amount of frames the decoder may buffer internally. The default value is 0
(autodetect).
.IP \fBfilmgrain\fR 4
.IX Item "filmgrain"
Apply film grain to the decoded video if present in the bitstream. Defaults to the
internal default of the library.
This option is deprecated and will be removed in the future. See the global option
\&\f(CW\*(C`export_side_data\*(C'\fR to export Film Grain parameters instead of applying it.
.IP \fBoppoint\fR 4
.IX Item "oppoint"
Select an operating point of a scalable AV1 bitstream (0 \- 31). Defaults to the
internal default of the library.
.IP \fBalllayers\fR 4
.IX Item "alllayers"
Output all spatial layers of a scalable AV1 bitstream. The default value is false.
.SS libdavs2
.IX Subsection "libdavs2"
AVS2\-P2/IEEE1857.4 video decoder wrapper.
.PP
This decoder allows libavcodec to decode AVS2 streams with davs2 library.
.SS libuavs3d
.IX Subsection "libuavs3d"
AVS3\-P2/IEEE1857.10 video decoder.
.PP
libuavs3d allows libavcodec to decode AVS3 streams.
Requires the presence of the libuavs3d headers and library during configuration.
You need to explicitly configure the build with \f(CW\*(C`\-\-enable\-libuavs3d\*(C'\fR.
.PP
\fIOptions\fR
.IX Subsection "Options"
.PP
The following option is supported by the libuavs3d wrapper.
.IP \fBframe_threads\fR 4
.IX Item "frame_threads"
Set amount of frame threads to use during decoding. The default value is 0 (autodetect).
.SS libxevd
.IX Subsection "libxevd"
eXtra-fast Essential Video Decoder (XEVD) MPEG\-5 EVC decoder wrapper.
.PP
This decoder requires the presence of the libxevd headers and library
during configuration. You need to explicitly configure the build with
\&\fB\-\-enable\-libxevd\fR.
.PP
The xevd project website is at <\fBhttps://github.com/mpeg5/xevd\fR>.
.PP
\fIOptions\fR
.IX Subsection "Options"
.PP
The following options are supported by the libxevd wrapper.
The xevd-equivalent options or values are listed in parentheses for easy migration.
.PP
To get a more accurate and extensive documentation of the libxevd options,
invoke the command  \f(CW\*(C`xevd_app \-\-help\*(C'\fR or consult the libxevd documentation.
.IP "\fBthreads (\fR\fIthreads\fR\fB)\fR" 4
.IX Item "threads (threads)"
Force to use a specific number of threads
.SS "QSV Decoders"
.IX Subsection "QSV Decoders"
The family of Intel QuickSync Video decoders (VC1, MPEG\-2, H.264, HEVC,
JPEG/MJPEG, VP8, VP9, AV1, VVC).
.PP
\fICommon Options\fR
.IX Subsection "Common Options"
.PP
The following options are supported by all qsv decoders.
.IP \fIasync_depth\fR 4
.IX Item "async_depth"
Internal parallelization depth, the higher the value the higher the latency.
.IP \fIgpu_copy\fR 4
.IX Item "gpu_copy"
A GPU-accelerated copy between video and system memory
.RS 4
.IP \fBdefault\fR 4
.IX Item "default"
.PD 0
.IP \fBon\fR 4
.IX Item "on"
.IP \fBoff\fR 4
.IX Item "off"
.RE
.RS 4
.RE
.PD
.PP
\fIHEVC Options\fR
.IX Subsection "HEVC Options"
.PP
Extra options for hevc_qsv.
.IP \fIload_plugin\fR 4
.IX Item "load_plugin"
A user plugin to load in an internal session
.RS 4
.IP \fBnone\fR 4
.IX Item "none"
.PD 0
.IP \fBhevc_sw\fR 4
.IX Item "hevc_sw"
.IP \fBhevc_hw\fR 4
.IX Item "hevc_hw"
.RE
.RS 4
.RE
.IP \fIload_plugins\fR 4
.IX Item "load_plugins"
.PD
A :\-separate list of hexadecimal plugin UIDs to load in an internal session
.SS v210
.IX Subsection "v210"
Uncompressed 4:2:2 10\-bit decoder.
.PP
\fIOptions\fR
.IX Subsection "Options"
.IP \fBcustom_stride\fR 4
.IX Item "custom_stride"
Set the line size of the v210 data in bytes. The default value is 0
(autodetect). You can use the special \-1 value for a strideless v210 as seen in
BOXX files.
.SH "AUDIO DECODERS"
.IX Header "AUDIO DECODERS"
A description of some of the currently available audio decoders
follows.
.SS ac3
.IX Subsection "ac3"
AC\-3 audio decoder.
.PP
This decoder implements part of ATSC A/52:2010 and ETSI TS 102 366, as well as
the undocumented RealAudio 3 (a.k.a. dnet).
.PP
\fIAC\-3 Decoder Options\fR
.IX Subsection "AC-3 Decoder Options"
.IP "\fB\-drc_scale\fR \fIvalue\fR" 4
.IX Item "-drc_scale value"
Dynamic Range Scale Factor. The factor to apply to dynamic range values
from the AC\-3 stream. This factor is applied exponentially. The default value is 1.
There are 3 notable scale factor ranges:
.RS 4
.IP "\fBdrc_scale == 0\fR" 4
.IX Item "drc_scale == 0"
DRC disabled. Produces full range audio.
.IP "\fB0 < drc_scale <= 1\fR" 4
.IX Item "0 < drc_scale <= 1"
DRC enabled.  Applies a fraction of the stream DRC value.
Audio reproduction is between full range and full compression.
.IP "\fBdrc_scale > 1\fR" 4
.IX Item "drc_scale > 1"
DRC enabled. Applies drc_scale asymmetrically.
Loud sounds are fully compressed.  Soft sounds are enhanced.
.RE
.RS 4
.RE
.SS flac
.IX Subsection "flac"
FLAC audio decoder.
.PP
This decoder aims to implement the complete FLAC specification from Xiph.
.PP
\fIFLAC Decoder options\fR
.IX Subsection "FLAC Decoder options"
.IP \fB\-use_buggy_lpc\fR 4
.IX Item "-use_buggy_lpc"
The lavc FLAC encoder used to produce buggy streams with high lpc values
(like the default value). This option makes it possible to decode such streams
correctly by using lavc's old buggy lpc logic for decoding.
.SS ffwavesynth
.IX Subsection "ffwavesynth"
Internal wave synthesizer.
.PP
This decoder generates wave patterns according to predefined sequences. Its
use is purely internal and the format of the data it accepts is not publicly
documented.
.SS libcelt
.IX Subsection "libcelt"
libcelt decoder wrapper.
.PP
libcelt allows libavcodec to decode the Xiph CELT ultra-low delay audio codec.
Requires the presence of the libcelt headers and library during configuration.
You need to explicitly configure the build with \f(CW\*(C`\-\-enable\-libcelt\*(C'\fR.
.SS libgsm
.IX Subsection "libgsm"
libgsm decoder wrapper.
.PP
libgsm allows libavcodec to decode the GSM full rate audio codec. Requires
the presence of the libgsm headers and library during configuration. You need
to explicitly configure the build with \f(CW\*(C`\-\-enable\-libgsm\*(C'\fR.
.PP
This decoder supports both the ordinary GSM and the Microsoft variant.
.SS libilbc
.IX Subsection "libilbc"
libilbc decoder wrapper.
.PP
libilbc allows libavcodec to decode the Internet Low Bitrate Codec (iLBC)
audio codec. Requires the presence of the libilbc headers and library during
configuration. You need to explicitly configure the build with
\&\f(CW\*(C`\-\-enable\-libilbc\*(C'\fR.
.PP
\fIOptions\fR
.IX Subsection "Options"
.PP
The following option is supported by the libilbc wrapper.
.IP \fBenhance\fR 4
.IX Item "enhance"
Enable the enhancement of the decoded audio when set to 1. The default
value is 0 (disabled).
.SS libopencore-amrnb
.IX Subsection "libopencore-amrnb"
libopencore-amrnb decoder wrapper.
.PP
libopencore-amrnb allows libavcodec to decode the Adaptive Multi-Rate
Narrowband audio codec. Using it requires the presence of the
libopencore-amrnb headers and library during configuration. You need to
explicitly configure the build with \f(CW\*(C`\-\-enable\-libopencore\-amrnb\*(C'\fR.
.PP
An FFmpeg native decoder for AMR-NB exists, so users can decode AMR-NB
without this library.
.SS libopencore-amrwb
.IX Subsection "libopencore-amrwb"
libopencore-amrwb decoder wrapper.
.PP
libopencore-amrwb allows libavcodec to decode the Adaptive Multi-Rate
Wideband audio codec. Using it requires the presence of the
libopencore-amrwb headers and library during configuration. You need to
explicitly configure the build with \f(CW\*(C`\-\-enable\-libopencore\-amrwb\*(C'\fR.
.PP
An FFmpeg native decoder for AMR-WB exists, so users can decode AMR-WB
without this library.
.SS libopus
.IX Subsection "libopus"
libopus decoder wrapper.
.PP
libopus allows libavcodec to decode the Opus Interactive Audio Codec.
Requires the presence of the libopus headers and library during
configuration. You need to explicitly configure the build with
\&\f(CW\*(C`\-\-enable\-libopus\*(C'\fR.
.PP
An FFmpeg native decoder for Opus exists, so users can decode Opus
without this library.
.SH "SUBTITLES DECODERS"
.IX Header "SUBTITLES DECODERS"
.SS libaribb24
.IX Subsection "libaribb24"
ARIB STD\-B24 caption decoder.
.PP
Implements profiles A and C of the ARIB STD\-B24 standard.
.PP
\fIlibaribb24 Decoder Options\fR
.IX Subsection "libaribb24 Decoder Options"
.IP "\fB\-aribb24\-base\-path\fR \fIpath\fR" 4
.IX Item "-aribb24-base-path path"
Sets the base path for the libaribb24 library. This is utilized for reading of
configuration files (for custom unicode conversions), and for dumping of
non-text symbols as images under that location.
.Sp
Unset by default.
.IP "\fB\-aribb24\-skip\-ruby\-text\fR \fIboolean\fR" 4
.IX Item "-aribb24-skip-ruby-text boolean"
Tells the decoder wrapper to skip text blocks that contain half-height ruby
text.
.Sp
Enabled by default.
.SS libaribcaption
.IX Subsection "libaribcaption"
Yet another ARIB STD\-B24 caption decoder using external \fIlibaribcaption\fR
library.
.PP
Implements profiles A and C of the Japanese ARIB STD\-B24 standard,
Brazilian ABNT NBR 15606\-1, and Philippines version of ISDB-T.
.PP
Requires the presence of the libaribcaption headers and library
(<\fBhttps://github.com/xqq/libaribcaption\fR>) during configuration.
You need to explicitly configure the build with \f(CW\*(C`\-\-enable\-libaribcaption\*(C'\fR.
If both \fIlibaribb24\fR and \fIlibaribcaption\fR are enabled, \fIlibaribcaption\fR
decoder precedes.
.PP
\fIlibaribcaption Decoder Options\fR
.IX Subsection "libaribcaption Decoder Options"
.IP "\fB\-sub_type\fR \fIsubtitle_type\fR" 4
.IX Item "-sub_type subtitle_type"
Specifies the format of the decoded subtitles.
.RS 4
.IP \fBbitmap\fR 4
.IX Item "bitmap"
Graphical image.
.IP \fBass\fR 4
.IX Item "ass"
ASS formatted text.
.IP \fBtext\fR 4
.IX Item "text"
Simple text based output without formatting.
.RE
.RS 4
.Sp
The default is \fIass\fR as same as \fIlibaribb24\fR decoder.
Some present players (e.g., \fImpv\fR) expect ASS format for ARIB caption.
.RE
.IP "\fB\-caption_encoding\fR \fIencoding_scheme\fR" 4
.IX Item "-caption_encoding encoding_scheme"
Specifies the encoding scheme of input subtitle text.
.RS 4
.IP \fBauto\fR 4
.IX Item "auto"
Automatically detect text encoding (default).
.IP \fBjis\fR 4
.IX Item "jis"
8bit\-char JIS encoding defined in ARIB STD B24.
This encoding used in Japan for ISDB captions.
.IP \fButf8\fR 4
.IX Item "utf8"
UTF\-8 encoding defined in ARIB STD B24.
This encoding is used in Philippines for ISDB-T captions.
.IP \fBlatin\fR 4
.IX Item "latin"
Latin character encoding defined in ABNT NBR 15606\-1.
This encoding is used in South America for SBTVD / ISDB-Tb captions.
.RE
.RS 4
.RE
.IP "\fB\-font\fR \fIfont_name[,font_name2,...]\fR" 4
.IX Item "-font font_name[,font_name2,...]"
Specify comma-separated list of font family names to be used for \fIbitmap\fR
or \fIass\fR type subtitle rendering.
Only first font name is used for \fIass\fR type subtitle.
.Sp
If not specified, use internally defined default font family.
.IP "\fB\-ass_single_rect\fR \fIboolean\fR" 4
.IX Item "-ass_single_rect boolean"
ARIB STD\-B24 specifies that some captions may be displayed at different
positions at a time (multi-rectangle subtitle).
Since some players (e.g., old \fImpv\fR) can't handle multiple ASS rectangles
in a single AVSubtitle, or multiple ASS rectangles of indeterminate duration
with the same start timestamp, this option can change the behavior so that
all the texts are displayed in a single ASS rectangle.
.Sp
The default is \fIfalse\fR.
.Sp
If your player cannot handle AVSubtitles with multiple ASS rectangles properly,
set this option to \fItrue\fR or define \fBASS_SINGLE_RECT=1\fR to change
default behavior at compilation.
.IP "\fB\-force_outline_text\fR \fIboolean\fR" 4
.IX Item "-force_outline_text boolean"
Specify whether always render outline text for all characters regardless of
the indication by character style.
.Sp
The default is \fIfalse\fR.
.IP "\fB\-outline_width\fR \fInumber\fR \fB(0.0 \- 3.0)\fR" 4
.IX Item "-outline_width number (0.0 - 3.0)"
Specify width for outline text, in dots (relative).
.Sp
The default is \fI1.5\fR.
.IP "\fB\-ignore_background\fR \fIboolean\fR" 4
.IX Item "-ignore_background boolean"
Specify whether to ignore background color rendering.
.Sp
The default is \fIfalse\fR.
.IP "\fB\-ignore_ruby\fR \fIboolean\fR" 4
.IX Item "-ignore_ruby boolean"
Specify whether to ignore rendering for ruby-like (furigana) characters.
.Sp
The default is \fIfalse\fR.
.IP "\fB\-replace_drcs\fR \fIboolean\fR" 4
.IX Item "-replace_drcs boolean"
Specify whether to render replaced DRCS characters as Unicode characters.
.Sp
The default is \fItrue\fR.
.IP "\fB\-replace_msz_ascii\fR \fIboolean\fR" 4
.IX Item "-replace_msz_ascii boolean"
Specify whether to replace MSZ (Middle Size; half width) fullwidth
alphanumerics with halfwidth alphanumerics.
.Sp
The default is \fItrue\fR.
.IP "\fB\-replace_msz_japanese\fR \fIboolean\fR" 4
.IX Item "-replace_msz_japanese boolean"
Specify whether to replace some MSZ (Middle Size; half width) fullwidth
japanese special characters with halfwidth ones.
.Sp
The default is \fItrue\fR.
.IP "\fB\-replace_msz_glyph\fR \fIboolean\fR" 4
.IX Item "-replace_msz_glyph boolean"
Specify whether to replace MSZ (Middle Size; half width) characters
with halfwidth glyphs if the fonts supports it.
This option works under FreeType or DirectWrite renderer
with Adobe\-Japan1 compliant fonts.
e.g., IBM Plex Sans JP, Morisawa BIZ UDGothic, Morisawa BIZ UDMincho,
Yu Gothic, Yu Mincho, and Meiryo.
.Sp
The default is \fItrue\fR.
.IP "\fB\-canvas_size\fR \fIimage_size\fR" 4
.IX Item "-canvas_size image_size"
Specify the resolution of the canvas to render subtitles to; usually, this
should be frame size of input video.
This only applies when \f(CW\*(C`\-subtitle_type\*(C'\fR is set to \fIbitmap\fR.
.Sp
The libaribcaption decoder assumes input frame size for bitmap rendering as below:
.RS 4
.IP 1. 4
PROFILE_A : 1440 x 1080 with SAR (PAR) 4:3
.IP 2. 4
PROFILE_C : 320 x 180 with SAR (PAR) 1:1
.RE
.RS 4
.Sp
If actual frame size of input video does not match above assumption,
the rendered captions may be distorted.
To make the captions undistorted, add \f(CW\*(C`\-canvas_size\*(C'\fR option to specify
actual input video size.
.Sp
Note that the \f(CW\*(C`\-canvas_size\*(C'\fR option is not required for video with
different size but same aspect ratio.
In such cases, the caption will be stretched or shrunk to actual video size
if \f(CW\*(C`\-canvas_size\*(C'\fR option is not specified.
If \f(CW\*(C`\-canvas_size\*(C'\fR option is specified with different size,
the caption will be stretched or shrunk as specified size with calculated SAR.
.RE
.PP
\fIlibaribcaption decoder usage examples\fR
.IX Subsection "libaribcaption decoder usage examples"
.PP
Display MPEG-TS file with ARIB subtitle by \f(CW\*(C`ffplay\*(C'\fR tool:
.PP
.Vb 1
\&        ffplay \-sub_type bitmap MPEG.TS
.Ve
.PP
Display MPEG-TS file with input frame size 1920x1080 by \f(CW\*(C`ffplay\*(C'\fR tool:
.PP
.Vb 1
\&        ffplay \-sub_type bitmap \-canvas_size 1920x1080 MPEG.TS
.Ve
.PP
Embed ARIB subtitle in transcoded video:
.PP
.Vb 1
\&        ffmpeg \-sub_type bitmap \-i src.m2t \-filter_complex "[0:v][0:s]overlay" \-vcodec h264 dest.mp4
.Ve
.SS dvbsub
.IX Subsection "dvbsub"
\fIOptions\fR
.IX Subsection "Options"
.IP \fBcompute_clut\fR 4
.IX Item "compute_clut"
.RS 4
.PD 0
.IP \fB\-2\fR 4
.IX Item "-2"
.PD
Compute clut once if no matching CLUT is in the stream.
.IP \fB\-1\fR 4
.IX Item "-1"
Compute clut if no matching CLUT is in the stream.
.IP \fB0\fR 4
.IX Item "0"
Never compute CLUT
.IP \fB1\fR 4
.IX Item "1"
Always compute CLUT and override the one provided in the stream.
.RE
.RS 4
.RE
.IP \fBdvb_substream\fR 4
.IX Item "dvb_substream"
Selects the dvb substream, or all substreams if \-1 which is default.
.SS dvdsub
.IX Subsection "dvdsub"
This codec decodes the bitmap subtitles used in DVDs; the same subtitles can
also be found in VobSub file pairs and in some Matroska files.
.PP
\fIOptions\fR
.IX Subsection "Options"
.IP \fBpalette\fR 4
.IX Item "palette"
Specify the global palette used by the bitmaps. When stored in VobSub, the
palette is normally specified in the index file; in Matroska, the palette is
stored in the codec extra-data in the same format as in VobSub. In DVDs, the
palette is stored in the IFO file, and therefore not available when reading
from dumped VOB files.
.Sp
The format for this option is a string containing 16 24\-bits hexadecimal
numbers (without 0x prefix) separated by commas, for example \f(CW\*(C`0d00ee,
ee450d, 101010, eaeaea, 0ce60b, ec14ed, ebff0b, 0d617a, 7b7b7b, d1d1d1,
7b2a0e, 0d950c, 0f007b, cf0dec, cfa80c, 7c127b\*(C'\fR.
.IP \fBifo_palette\fR 4
.IX Item "ifo_palette"
Specify the IFO file from which the global palette is obtained.
(experimental)
.IP \fBforced_subs_only\fR 4
.IX Item "forced_subs_only"
Only decode subtitle entries marked as forced. Some titles have forced
and non-forced subtitles in the same track. Setting this flag to \f(CW1\fR
will only keep the forced subtitles. Default value is \f(CW0\fR.
.SS libzvbi-teletext
.IX Subsection "libzvbi-teletext"
Libzvbi allows libavcodec to decode DVB teletext pages and DVB teletext
subtitles. Requires the presence of the libzvbi headers and library during
configuration. You need to explicitly configure the build with
\&\f(CW\*(C`\-\-enable\-libzvbi\*(C'\fR.
.PP
\fIOptions\fR
.IX Subsection "Options"
.IP \fBtxt_page\fR 4
.IX Item "txt_page"
List of teletext page numbers to decode. Pages that do not match the specified
list are dropped. You may use the special \f(CW\*(C`*\*(C'\fR string to match all pages,
or \f(CW\*(C`subtitle\*(C'\fR to match all subtitle pages.
Default value is *.
.IP \fBtxt_default_region\fR 4
.IX Item "txt_default_region"
Set default character set used for decoding, a value between 0 and 87 (see
ETS 300 706, Section 15, Table 32). Default value is \-1, which does not
override the libzvbi default. This option is needed for some legacy level 1.0
transmissions which cannot signal the proper charset.
.IP \fBtxt_chop_top\fR 4
.IX Item "txt_chop_top"
Discards the top teletext line. Default value is 1.
.IP \fBtxt_format\fR 4
.IX Item "txt_format"
Specifies the format of the decoded subtitles.
.RS 4
.IP \fBbitmap\fR 4
.IX Item "bitmap"
The default format, you should use this for teletext pages, because certain
graphics and colors cannot be expressed in simple text or even ASS.
.IP \fBtext\fR 4
.IX Item "text"
Simple text based output without formatting.
.IP \fBass\fR 4
.IX Item "ass"
Formatted ASS output, subtitle pages and teletext pages are returned in
different styles, subtitle pages are stripped down to text, but an effort is
made to keep the text alignment and the formatting.
.RE
.RS 4
.RE
.IP \fBtxt_left\fR 4
.IX Item "txt_left"
X offset of generated bitmaps, default is 0.
.IP \fBtxt_top\fR 4
.IX Item "txt_top"
Y offset of generated bitmaps, default is 0.
.IP \fBtxt_chop_spaces\fR 4
.IX Item "txt_chop_spaces"
Chops leading and trailing spaces and removes empty lines from the generated
text. This option is useful for teletext based subtitles where empty spaces may
be present at the start or at the end of the lines or empty lines may be
present between the subtitle lines because of double-sized teletext characters.
Default value is 1.
.IP \fBtxt_duration\fR 4
.IX Item "txt_duration"
Sets the display duration of the decoded teletext pages or subtitles in
milliseconds. Default value is \-1 which means infinity or until the next
subtitle event comes.
.IP \fBtxt_transparent\fR 4
.IX Item "txt_transparent"
Force transparent background of the generated teletext bitmaps. Default value
is 0 which means an opaque background.
.IP \fBtxt_opacity\fR 4
.IX Item "txt_opacity"
Sets the opacity (0\-255) of the teletext background. If
\&\fBtxt_transparent\fR is not set, it only affects characters between a start
box and an end box, typically subtitles. Default value is 0 if
\&\fBtxt_transparent\fR is set, 255 otherwise.
.SH ENCODERS
.IX Header "ENCODERS"
Encoders are configured elements in FFmpeg which allow the encoding of
multimedia streams.
.PP
When you configure your FFmpeg build, all the supported native encoders
are enabled by default. Encoders requiring an external library must be enabled
manually via the corresponding \f(CW\*(C`\-\-enable\-lib\*(C'\fR option. You can list all
available encoders using the configure option \f(CW\*(C`\-\-list\-encoders\*(C'\fR.
.PP
You can disable all the encoders with the configure option
\&\f(CW\*(C`\-\-disable\-encoders\*(C'\fR and selectively enable / disable single encoders
with the options \f(CW\*(C`\-\-enable\-encoder=\fR\f(CIENCODER\fR\f(CW\*(C'\fR /
\&\f(CW\*(C`\-\-disable\-encoder=\fR\f(CIENCODER\fR\f(CW\*(C'\fR.
.PP
The option \f(CW\*(C`\-encoders\*(C'\fR of the ff* tools will display the list of
enabled encoders.
.SH "AUDIO ENCODERS"
.IX Header "AUDIO ENCODERS"
A description of some of the currently available audio encoders
follows.
.SS aac
.IX Subsection "aac"
Advanced Audio Coding (AAC) encoder.
.PP
This encoder is the default AAC encoder, natively implemented into FFmpeg.
.PP
\fIOptions\fR
.IX Subsection "Options"
.IP \fBb\fR 4
.IX Item "b"
Set bit rate in bits/s. Setting this automatically activates constant bit rate
(CBR) mode. If this option is unspecified it is set to 128kbps.
.IP \fBq\fR 4
.IX Item "q"
Set quality for variable bit rate (VBR) mode. This option is valid only using
the \fBffmpeg\fR command-line tool. For library interface users, use
\&\fBglobal_quality\fR.
.IP \fBcutoff\fR 4
.IX Item "cutoff"
Set cutoff frequency. If unspecified will allow the encoder to dynamically
adjust the cutoff to improve clarity on low bitrates.
.IP \fBaac_coder\fR 4
.IX Item "aac_coder"
Set AAC encoder coding method. Possible values:
.RS 4
.IP \fBtwoloop\fR 4
.IX Item "twoloop"
Two loop searching (TLS) method. This is the default method.
.Sp
This method first sets quantizers depending on band thresholds and then tries
to find an optimal combination by adding or subtracting a specific value from
all quantizers and adjusting some individual quantizer a little.  Will tune
itself based on whether \fBaac_is\fR, \fBaac_ms\fR and \fBaac_pns\fR
are enabled.
.IP \fBanmr\fR 4
.IX Item "anmr"
Average noise to mask ratio (ANMR) trellis-based solution.
.Sp
This is an experimental coder which currently produces a lower quality, is more
unstable and is slower than the default twoloop coder but has potential.
Currently has no support for the \fBaac_is\fR or \fBaac_pns\fR options.
Not currently recommended.
.IP \fBfast\fR 4
.IX Item "fast"
Constant quantizer method.
.Sp
Uses a cheaper version of twoloop algorithm that doesn't try to do as many
clever adjustments. Worse with low bitrates (less than 64kbps), but is better
and much faster at higher bitrates.
.RE
.RS 4
.RE
.IP \fBaac_ms\fR 4
.IX Item "aac_ms"
Sets mid/side coding mode. The default value of "auto" will automatically use
M/S with bands which will benefit from such coding. Can be forced for all bands
using the value "enable", which is mainly useful for debugging or disabled using
"disable".
.IP \fBaac_is\fR 4
.IX Item "aac_is"
Sets intensity stereo coding tool usage. By default, it's enabled and will
automatically toggle IS for similar pairs of stereo bands if it's beneficial.
Can be disabled for debugging by setting the value to "disable".
.IP \fBaac_pns\fR 4
.IX Item "aac_pns"
Uses perceptual noise substitution to replace low entropy high frequency bands
with imperceptible white noise during the decoding process. By default, it's
enabled, but can be disabled for debugging purposes by using "disable".
.IP \fBaac_tns\fR 4
.IX Item "aac_tns"
Enables the use of a multitap FIR filter which spans through the high frequency
bands to hide quantization noise during the encoding process and is reverted
by the decoder. As well as decreasing unpleasant artifacts in the high range
this also reduces the entropy in the high bands and allows for more bits to
be used by the mid-low bands. By default it's enabled but can be disabled for
debugging by setting the option to "disable".
.IP \fBaac_ltp\fR 4
.IX Item "aac_ltp"
Enables the use of the long term prediction extension which increases coding
efficiency in very low bandwidth situations such as encoding of voice or
solo piano music by extending constant harmonic peaks in bands throughout
frames. This option is implied by profile:a aac_low.
Use in conjunction with \fB\-ar\fR to decrease the samplerate.
.IP \fBprofile\fR 4
.IX Item "profile"
Sets the encoding profile, possible values:
.RS 4
.IP \fBaac_low\fR 4
.IX Item "aac_low"
The default, AAC "Low-complexity" profile. Is the most compatible and produces
decent quality.
.IP \fBmpeg2_aac_low\fR 4
.IX Item "mpeg2_aac_low"
Equivalent to \f(CW\*(C`\-profile:a aac_low \-aac_pns 0\*(C'\fR. PNS was introduced with the
MPEG4 specifications.
.IP \fBaac_ltp\fR 4
.IX Item "aac_ltp"
Long term prediction profile, is enabled by and will enable the \fBaac_ltp\fR
option. Introduced in MPEG4.
.RE
.RS 4
.Sp
If this option is unspecified it is set to \fBaac_low\fR.
.RE
.SS "ac3 and ac3_fixed"
.IX Subsection "ac3 and ac3_fixed"
AC\-3 audio encoders.
.PP
These encoders implement part of ATSC A/52:2010 and ETSI TS 102 366.
.PP
The \fIac3\fR encoder uses floating-point math, while the \fIac3_fixed\fR
encoder only uses fixed-point integer math. This does not mean that one is
always faster, just that one or the other may be better suited to a
particular system. The \fIac3_fixed\fR encoder is not the default codec for
any of the output formats, so it must be specified explicitly using the option
\&\f(CW\*(C`\-acodec ac3_fixed\*(C'\fR in order to use it.
.PP
\fIAC\-3 Metadata\fR
.IX Subsection "AC-3 Metadata"
.PP
The AC\-3 metadata options are used to set parameters that describe the audio,
but in most cases do not affect the audio encoding itself. Some of the options
do directly affect or influence the decoding and playback of the resulting
bitstream, while others are just for informational purposes. A few of the
options will add bits to the output stream that could otherwise be used for
audio data, and will thus affect the quality of the output. Those will be
indicated accordingly with a note in the option list below.
.PP
These parameters are described in detail in several publicly-available
documents.
.IP *<<\fBhttp://www.atsc.org/cms/standards/a_52\-2010.pdf\fR>> 4
.IX Item "*<<http://www.atsc.org/cms/standards/a_52-2010.pdf>>"
.PD 0
.IP *<<\fBhttp://www.atsc.org/cms/standards/a_54a_with_corr_1.pdf\fR>> 4
.IX Item "*<<http://www.atsc.org/cms/standards/a_54a_with_corr_1.pdf>>"
.IP *<<\fBhttp://www.dolby.com/uploadedFiles/zz\-_Shared_Assets/English_PDFs/Professional/18_Metadata.Guide.pdf\fR>> 4
.IX Item "*<<http://www.dolby.com/uploadedFiles/zz-_Shared_Assets/English_PDFs/Professional/18_Metadata.Guide.pdf>>"
.IP *<<\fBhttp://www.dolby.com/uploadedFiles/zz\-_Shared_Assets/English_PDFs/Professional/46_DDEncodingGuidelines.pdf\fR>> 4
.IX Item "*<<http://www.dolby.com/uploadedFiles/zz-_Shared_Assets/English_PDFs/Professional/46_DDEncodingGuidelines.pdf>>"
.PD
.PP
Metadata Control Options
.IX Subsection "Metadata Control Options"
.IP "\fB\-per_frame_metadata\fR \fIboolean\fR" 4
.IX Item "-per_frame_metadata boolean"
Allow Per-Frame Metadata. Specifies if the encoder should check for changing
metadata for each frame.
.RS 4
.IP \fB0\fR 4
.IX Item "0"
The metadata values set at initialization will be used for every frame in the
stream. (default)
.IP \fB1\fR 4
.IX Item "1"
Metadata values can be changed before encoding each frame.
.RE
.RS 4
.RE
.PP
Downmix Levels
.IX Subsection "Downmix Levels"
.IP "\fB\-center_mixlev\fR \fIlevel\fR" 4
.IX Item "-center_mixlev level"
Center Mix Level. The amount of gain the decoder should apply to the center
channel when downmixing to stereo. This field will only be written to the
bitstream if a center channel is present. The value is specified as a scale
factor. There are 3 valid values:
.RS 4
.IP \fB0.707\fR 4
.IX Item "0.707"
Apply \-3dB gain
.IP \fB0.595\fR 4
.IX Item "0.595"
Apply \-4.5dB gain (default)
.IP \fB0.500\fR 4
.IX Item "0.500"
Apply \-6dB gain
.RE
.RS 4
.RE
.IP "\fB\-surround_mixlev\fR \fIlevel\fR" 4
.IX Item "-surround_mixlev level"
Surround Mix Level. The amount of gain the decoder should apply to the surround
channel(s) when downmixing to stereo. This field will only be written to the
bitstream if one or more surround channels are present. The value is specified
as a scale factor.  There are 3 valid values:
.RS 4
.IP \fB0.707\fR 4
.IX Item "0.707"
Apply \-3dB gain
.IP \fB0.500\fR 4
.IX Item "0.500"
Apply \-6dB gain (default)
.IP \fB0.000\fR 4
.IX Item "0.000"
Silence Surround Channel(s)
.RE
.RS 4
.RE
.PP
Audio Production Information
.IX Subsection "Audio Production Information"
.PP
Audio Production Information is optional information describing the mixing
environment.  Either none or both of the fields are written to the bitstream.
.IP "\fB\-mixing_level\fR \fInumber\fR" 4
.IX Item "-mixing_level number"
Mixing Level. Specifies peak sound pressure level (SPL) in the production
environment when the mix was mastered. Valid values are 80 to 111, or \-1 for
unknown or not indicated. The default value is \-1, but that value cannot be
used if the Audio Production Information is written to the bitstream. Therefore,
if the \f(CW\*(C`room_type\*(C'\fR option is not the default value, the \f(CW\*(C`mixing_level\*(C'\fR
option must not be \-1.
.IP "\fB\-room_type\fR \fItype\fR" 4
.IX Item "-room_type type"
Room Type. Describes the equalization used during the final mixing session at
the studio or on the dubbing stage. A large room is a dubbing stage with the
industry standard X\-curve equalization; a small room has flat equalization.
This field will not be written to the bitstream if both the \f(CW\*(C`mixing_level\*(C'\fR
option and the \f(CW\*(C`room_type\*(C'\fR option have the default values.
.RS 4
.IP \fB0\fR 4
.IX Item "0"
.PD 0
.IP \fBnotindicated\fR 4
.IX Item "notindicated"
.PD
Not Indicated (default)
.IP \fB1\fR 4
.IX Item "1"
.PD 0
.IP \fBlarge\fR 4
.IX Item "large"
.PD
Large Room
.IP \fB2\fR 4
.IX Item "2"
.PD 0
.IP \fBsmall\fR 4
.IX Item "small"
.PD
Small Room
.RE
.RS 4
.RE
.PP
Other Metadata Options
.IX Subsection "Other Metadata Options"
.IP "\fB\-copyright\fR \fIboolean\fR" 4
.IX Item "-copyright boolean"
Copyright Indicator. Specifies whether a copyright exists for this audio.
.RS 4
.IP \fB0\fR 4
.IX Item "0"
.PD 0
.IP \fBoff\fR 4
.IX Item "off"
.PD
No Copyright Exists (default)
.IP \fB1\fR 4
.IX Item "1"
.PD 0
.IP \fBon\fR 4
.IX Item "on"
.PD
Copyright Exists
.RE
.RS 4
.RE
.IP "\fB\-dialnorm\fR \fIvalue\fR" 4
.IX Item "-dialnorm value"
Dialogue Normalization. Indicates how far the average dialogue level of the
program is below digital 100% full scale (0 dBFS). This parameter determines a
level shift during audio reproduction that sets the average volume of the
dialogue to a preset level. The goal is to match volume level between program
sources. A value of \-31dB will result in no volume level change, relative to
the source volume, during audio reproduction. Valid values are whole numbers in
the range \-31 to \-1, with \-31 being the default.
.IP "\fB\-dsur_mode\fR \fImode\fR" 4
.IX Item "-dsur_mode mode"
Dolby Surround Mode. Specifies whether the stereo signal uses Dolby Surround
(Pro Logic). This field will only be written to the bitstream if the audio
stream is stereo. Using this option does \fBNOT\fR mean the encoder will actually
apply Dolby Surround processing.
.RS 4
.IP \fB0\fR 4
.IX Item "0"
.PD 0
.IP \fBnotindicated\fR 4
.IX Item "notindicated"
.PD
Not Indicated (default)
.IP \fB1\fR 4
.IX Item "1"
.PD 0
.IP \fBoff\fR 4
.IX Item "off"
.PD
Not Dolby Surround Encoded
.IP \fB2\fR 4
.IX Item "2"
.PD 0
.IP \fBon\fR 4
.IX Item "on"
.PD
Dolby Surround Encoded
.RE
.RS 4
.RE
.IP "\fB\-original\fR \fIboolean\fR" 4
.IX Item "-original boolean"
Original Bit Stream Indicator. Specifies whether this audio is from the
original source and not a copy.
.RS 4
.IP \fB0\fR 4
.IX Item "0"
.PD 0
.IP \fBoff\fR 4
.IX Item "off"
.PD
Not Original Source
.IP \fB1\fR 4
.IX Item "1"
.PD 0
.IP \fBon\fR 4
.IX Item "on"
.PD
Original Source (default)
.RE
.RS 4
.RE
.PP
\fIExtended Bitstream Information\fR
.IX Subsection "Extended Bitstream Information"
.PP
The extended bitstream options are part of the Alternate Bit Stream Syntax as
specified in Annex D of the A/52:2010 standard. It is grouped into 2 parts.
If any one parameter in a group is specified, all values in that group will be
written to the bitstream.  Default values are used for those that are written
but have not been specified.  If the mixing levels are written, the decoder
will use these values instead of the ones specified in the \f(CW\*(C`center_mixlev\*(C'\fR
and \f(CW\*(C`surround_mixlev\*(C'\fR options if it supports the Alternate Bit Stream
Syntax.
.PP
Extended Bitstream Information \- Part 1
.IX Subsection "Extended Bitstream Information - Part 1"
.IP "\fB\-dmix_mode\fR \fImode\fR" 4
.IX Item "-dmix_mode mode"
Preferred Stereo Downmix Mode. Allows the user to select either Lt/Rt
(Dolby Surround) or Lo/Ro (normal stereo) as the preferred stereo downmix mode.
.RS 4
.IP \fB0\fR 4
.IX Item "0"
.PD 0
.IP \fBnotindicated\fR 4
.IX Item "notindicated"
.PD
Not Indicated (default)
.IP \fB1\fR 4
.IX Item "1"
.PD 0
.IP \fBltrt\fR 4
.IX Item "ltrt"
.PD
Lt/Rt Downmix Preferred
.IP \fB2\fR 4
.IX Item "2"
.PD 0
.IP \fBloro\fR 4
.IX Item "loro"
.PD
Lo/Ro Downmix Preferred
.RE
.RS 4
.RE
.IP "\fB\-ltrt_cmixlev\fR \fIlevel\fR" 4
.IX Item "-ltrt_cmixlev level"
Lt/Rt Center Mix Level. The amount of gain the decoder should apply to the
center channel when downmixing to stereo in Lt/Rt mode.
.RS 4
.IP \fB1.414\fR 4
.IX Item "1.414"
Apply +3dB gain
.IP \fB1.189\fR 4
.IX Item "1.189"
Apply +1.5dB gain
.IP \fB1.000\fR 4
.IX Item "1.000"
Apply 0dB gain
.IP \fB0.841\fR 4
.IX Item "0.841"
Apply \-1.5dB gain
.IP \fB0.707\fR 4
.IX Item "0.707"
Apply \-3.0dB gain
.IP \fB0.595\fR 4
.IX Item "0.595"
Apply \-4.5dB gain (default)
.IP \fB0.500\fR 4
.IX Item "0.500"
Apply \-6.0dB gain
.IP \fB0.000\fR 4
.IX Item "0.000"
Silence Center Channel
.RE
.RS 4
.RE
.IP "\fB\-ltrt_surmixlev\fR \fIlevel\fR" 4
.IX Item "-ltrt_surmixlev level"
Lt/Rt Surround Mix Level. The amount of gain the decoder should apply to the
surround channel(s) when downmixing to stereo in Lt/Rt mode.
.RS 4
.IP \fB0.841\fR 4
.IX Item "0.841"
Apply \-1.5dB gain
.IP \fB0.707\fR 4
.IX Item "0.707"
Apply \-3.0dB gain
.IP \fB0.595\fR 4
.IX Item "0.595"
Apply \-4.5dB gain
.IP \fB0.500\fR 4
.IX Item "0.500"
Apply \-6.0dB gain (default)
.IP \fB0.000\fR 4
.IX Item "0.000"
Silence Surround Channel(s)
.RE
.RS 4
.RE
.IP "\fB\-loro_cmixlev\fR \fIlevel\fR" 4
.IX Item "-loro_cmixlev level"
Lo/Ro Center Mix Level. The amount of gain the decoder should apply to the
center channel when downmixing to stereo in Lo/Ro mode.
.RS 4
.IP \fB1.414\fR 4
.IX Item "1.414"
Apply +3dB gain
.IP \fB1.189\fR 4
.IX Item "1.189"
Apply +1.5dB gain
.IP \fB1.000\fR 4
.IX Item "1.000"
Apply 0dB gain
.IP \fB0.841\fR 4
.IX Item "0.841"
Apply \-1.5dB gain
.IP \fB0.707\fR 4
.IX Item "0.707"
Apply \-3.0dB gain
.IP \fB0.595\fR 4
.IX Item "0.595"
Apply \-4.5dB gain (default)
.IP \fB0.500\fR 4
.IX Item "0.500"
Apply \-6.0dB gain
.IP \fB0.000\fR 4
.IX Item "0.000"
Silence Center Channel
.RE
.RS 4
.RE
.IP "\fB\-loro_surmixlev\fR \fIlevel\fR" 4
.IX Item "-loro_surmixlev level"
Lo/Ro Surround Mix Level. The amount of gain the decoder should apply to the
surround channel(s) when downmixing to stereo in Lo/Ro mode.
.RS 4
.IP \fB0.841\fR 4
.IX Item "0.841"
Apply \-1.5dB gain
.IP \fB0.707\fR 4
.IX Item "0.707"
Apply \-3.0dB gain
.IP \fB0.595\fR 4
.IX Item "0.595"
Apply \-4.5dB gain
.IP \fB0.500\fR 4
.IX Item "0.500"
Apply \-6.0dB gain (default)
.IP \fB0.000\fR 4
.IX Item "0.000"
Silence Surround Channel(s)
.RE
.RS 4
.RE
.PP
Extended Bitstream Information \- Part 2
.IX Subsection "Extended Bitstream Information - Part 2"
.IP "\fB\-dsurex_mode\fR \fImode\fR" 4
.IX Item "-dsurex_mode mode"
Dolby Surround EX Mode. Indicates whether the stream uses Dolby Surround EX
(7.1 matrixed to 5.1). Using this option does \fBNOT\fR mean the encoder will actually
apply Dolby Surround EX processing.
.RS 4
.IP \fB0\fR 4
.IX Item "0"
.PD 0
.IP \fBnotindicated\fR 4
.IX Item "notindicated"
.PD
Not Indicated (default)
.IP \fB1\fR 4
.IX Item "1"
.PD 0
.IP \fBon\fR 4
.IX Item "on"
.PD
Dolby Surround EX Off
.IP \fB2\fR 4
.IX Item "2"
.PD 0
.IP \fBoff\fR 4
.IX Item "off"
.PD
Dolby Surround EX On
.RE
.RS 4
.RE
.IP "\fB\-dheadphone_mode\fR \fImode\fR" 4
.IX Item "-dheadphone_mode mode"
Dolby Headphone Mode. Indicates whether the stream uses Dolby Headphone
encoding (multi-channel matrixed to 2.0 for use with headphones). Using this
option does \fBNOT\fR mean the encoder will actually apply Dolby Headphone
processing.
.RS 4
.IP \fB0\fR 4
.IX Item "0"
.PD 0
.IP \fBnotindicated\fR 4
.IX Item "notindicated"
.PD
Not Indicated (default)
.IP \fB1\fR 4
.IX Item "1"
.PD 0
.IP \fBon\fR 4
.IX Item "on"
.PD
Dolby Headphone Off
.IP \fB2\fR 4
.IX Item "2"
.PD 0
.IP \fBoff\fR 4
.IX Item "off"
.PD
Dolby Headphone On
.RE
.RS 4
.RE
.IP "\fB\-ad_conv_type\fR \fItype\fR" 4
.IX Item "-ad_conv_type type"
A/D Converter Type. Indicates whether the audio has passed through HDCD A/D
conversion.
.RS 4
.IP \fB0\fR 4
.IX Item "0"
.PD 0
.IP \fBstandard\fR 4
.IX Item "standard"
.PD
Standard A/D Converter (default)
.IP \fB1\fR 4
.IX Item "1"
.PD 0
.IP \fBhdcd\fR 4
.IX Item "hdcd"
.PD
HDCD A/D Converter
.RE
.RS 4
.RE
.PP
\fIOther AC\-3 Encoding Options\fR
.IX Subsection "Other AC-3 Encoding Options"
.IP "\fB\-stereo_rematrixing\fR \fIboolean\fR" 4
.IX Item "-stereo_rematrixing boolean"
Stereo Rematrixing. Enables/Disables use of rematrixing for stereo input. This
is an optional AC\-3 feature that increases quality by selectively encoding
the left/right channels as mid/side. This option is enabled by default, and it
is highly recommended that it be left as enabled except for testing purposes.
.IP "\fBcutoff\fR \fIfrequency\fR" 4
.IX Item "cutoff frequency"
Set lowpass cutoff frequency. If unspecified, the encoder selects a default
determined by various other encoding parameters.
.PP
\fIFloating-Point-Only AC\-3 Encoding Options\fR
.IX Subsection "Floating-Point-Only AC-3 Encoding Options"
.PP
These options are only valid for the floating-point encoder and do not exist
for the fixed-point encoder due to the corresponding features not being
implemented in fixed-point.
.IP "\fB\-channel_coupling\fR \fIboolean\fR" 4
.IX Item "-channel_coupling boolean"
Enables/Disables use of channel coupling, which is an optional AC\-3 feature
that increases quality by combining high frequency information from multiple
channels into a single channel. The per-channel high frequency information is
sent with less accuracy in both the frequency and time domains. This allows
more bits to be used for lower frequencies while preserving enough information
to reconstruct the high frequencies. This option is enabled by default for the
floating-point encoder and should generally be left as enabled except for
testing purposes or to increase encoding speed.
.RS 4
.IP \fB\-1\fR 4
.IX Item "-1"
.PD 0
.IP \fBauto\fR 4
.IX Item "auto"
.PD
Selected by Encoder (default)
.IP \fB0\fR 4
.IX Item "0"
.PD 0
.IP \fBoff\fR 4
.IX Item "off"
.PD
Disable Channel Coupling
.IP \fB1\fR 4
.IX Item "1"
.PD 0
.IP \fBon\fR 4
.IX Item "on"
.PD
Enable Channel Coupling
.RE
.RS 4
.RE
.IP "\fB\-cpl_start_band\fR \fInumber\fR" 4
.IX Item "-cpl_start_band number"
Coupling Start Band. Sets the channel coupling start band, from 1 to 15. If a
value higher than the bandwidth is used, it will be reduced to 1 less than the
coupling end band. If \fIauto\fR is used, the start band will be determined by
the encoder based on the bit rate, sample rate, and channel layout. This option
has no effect if channel coupling is disabled.
.RS 4
.IP \fB\-1\fR 4
.IX Item "-1"
.PD 0
.IP \fBauto\fR 4
.IX Item "auto"
.PD
Selected by Encoder (default)
.RE
.RS 4
.RE
.SS flac
.IX Subsection "flac"
FLAC (Free Lossless Audio Codec) Encoder
.PP
\fIOptions\fR
.IX Subsection "Options"
.PP
The following options are supported by FFmpeg's flac encoder.
.IP \fBcompression_level\fR 4
.IX Item "compression_level"
Sets the compression level, which chooses defaults for many other options
if they are not set explicitly. Valid values are from 0 to 12, 5 is the
default.
.IP \fBframe_size\fR 4
.IX Item "frame_size"
Sets the size of the frames in samples per channel.
.IP \fBlpc_coeff_precision\fR 4
.IX Item "lpc_coeff_precision"
Sets the LPC coefficient precision, valid values are from 1 to 15, 15 is the
default.
.IP \fBlpc_type\fR 4
.IX Item "lpc_type"
Sets the first stage LPC algorithm
.RS 4
.IP \fBnone\fR 4
.IX Item "none"
LPC is not used
.IP \fBfixed\fR 4
.IX Item "fixed"
fixed LPC coefficients
.IP \fBlevinson\fR 4
.IX Item "levinson"
.PD 0
.IP \fBcholesky\fR 4
.IX Item "cholesky"
.RE
.RS 4
.RE
.IP \fBlpc_passes\fR 4
.IX Item "lpc_passes"
.PD
Number of passes to use for Cholesky factorization during LPC analysis
.IP \fBmin_partition_order\fR 4
.IX Item "min_partition_order"
The minimum partition order
.IP \fBmax_partition_order\fR 4
.IX Item "max_partition_order"
The maximum partition order
.IP \fBprediction_order_method\fR 4
.IX Item "prediction_order_method"
.RS 4
.PD 0
.IP \fBestimation\fR 4
.IX Item "estimation"
.IP \fB2level\fR 4
.IX Item "2level"
.IP \fB4level\fR 4
.IX Item "4level"
.IP \fB8level\fR 4
.IX Item "8level"
.IP \fBsearch\fR 4
.IX Item "search"
.PD
Bruteforce search
.IP \fBlog\fR 4
.IX Item "log"
.RE
.RS 4
.RE
.PD 0
.IP \fBch_mode\fR 4
.IX Item "ch_mode"
.PD
Channel mode
.RS 4
.IP \fBauto\fR 4
.IX Item "auto"
The mode is chosen automatically for each frame
.IP \fBindep\fR 4
.IX Item "indep"
Channels are independently coded
.IP \fBleft_side\fR 4
.IX Item "left_side"
.PD 0
.IP \fBright_side\fR 4
.IX Item "right_side"
.IP \fBmid_side\fR 4
.IX Item "mid_side"
.RE
.RS 4
.RE
.IP \fBexact_rice_parameters\fR 4
.IX Item "exact_rice_parameters"
.PD
Chooses if rice parameters are calculated exactly or approximately.
if set to 1 then they are chosen exactly, which slows the code down slightly and
improves compression slightly.
.IP \fBmulti_dim_quant\fR 4
.IX Item "multi_dim_quant"
Multi Dimensional Quantization. If set to 1 then a 2nd stage LPC algorithm is
applied after the first stage to finetune the coefficients. This is quite slow
and slightly improves compression.
.SS opus
.IX Subsection "opus"
Opus encoder.
.PP
This is a native FFmpeg encoder for the Opus format. Currently, it's in development and
only implements the CELT part of the codec. Its quality is usually worse and at best
is equal to the libopus encoder.
.PP
\fIOptions\fR
.IX Subsection "Options"
.IP \fBb\fR 4
.IX Item "b"
Set bit rate in bits/s. If unspecified it uses the number of channels and the layout
to make a good guess.
.IP \fBopus_delay\fR 4
.IX Item "opus_delay"
Sets the maximum delay in milliseconds. Lower delays than 20ms will very quickly
decrease quality.
.SS libfdk_aac
.IX Subsection "libfdk_aac"
libfdk-aac AAC (Advanced Audio Coding) encoder wrapper.
.PP
The libfdk-aac library is based on the Fraunhofer FDK AAC code from
the Android project.
.PP
Requires the presence of the libfdk-aac headers and library during
configuration. You need to explicitly configure the build with
\&\f(CW\*(C`\-\-enable\-libfdk\-aac\*(C'\fR. The library is also incompatible with GPL,
so if you allow the use of GPL, you should configure with
\&\f(CW\*(C`\-\-enable\-gpl \-\-enable\-nonfree \-\-enable\-libfdk\-aac\*(C'\fR.
.PP
This encoder has support for the AAC-HE profiles.
.PP
VBR encoding, enabled through the \fBvbr\fR or \fBflags
+qscale\fR options, is experimental and only works with some
combinations of parameters.
.PP
Support for encoding 7.1 audio is only available with libfdk-aac 0.1.3 or
higher.
.PP
For more information see the fdk-aac project at
<\fBhttp://sourceforge.net/p/opencore\-amr/fdk\-aac/\fR>.
.PP
\fIOptions\fR
.IX Subsection "Options"
.PP
The following options are mapped on the shared FFmpeg codec options.
.IP \fBb\fR 4
.IX Item "b"
Set bit rate in bits/s. If the bitrate is not explicitly specified, it
is automatically set to a suitable value depending on the selected
profile.
.Sp
In case VBR mode is enabled the option is ignored.
.IP \fBar\fR 4
.IX Item "ar"
Set audio sampling rate (in Hz).
.IP \fBchannels\fR 4
.IX Item "channels"
Set the number of audio channels.
.IP "\fBflags +qscale\fR" 4
.IX Item "flags +qscale"
Enable fixed quality, VBR (Variable Bit Rate) mode.
Note that VBR is implicitly enabled when the \fBvbr\fR value is
positive.
.IP \fBcutoff\fR 4
.IX Item "cutoff"
Set cutoff frequency. If not specified (or explicitly set to 0) it
will use a value automatically computed by the library. Default value
is 0.
.IP \fBprofile\fR 4
.IX Item "profile"
Set audio profile.
.Sp
The following profiles are recognized:
.RS 4
.IP \fBaac_low\fR 4
.IX Item "aac_low"
Low Complexity AAC (LC)
.IP \fBaac_he\fR 4
.IX Item "aac_he"
High Efficiency AAC (HE-AAC)
.IP \fBaac_he_v2\fR 4
.IX Item "aac_he_v2"
High Efficiency AAC version 2 (HE\-AACv2)
.IP \fBaac_ld\fR 4
.IX Item "aac_ld"
Low Delay AAC (LD)
.IP \fBaac_eld\fR 4
.IX Item "aac_eld"
Enhanced Low Delay AAC (ELD)
.RE
.RS 4
.Sp
If not specified it is set to \fBaac_low\fR.
.RE
.PP
The following are private options of the libfdk_aac encoder.
.IP \fBafterburner\fR 4
.IX Item "afterburner"
Enable afterburner feature if set to 1, disabled if set to 0. This
improves the quality but also the required processing power.
.Sp
Default value is 1.
.IP \fBeld_sbr\fR 4
.IX Item "eld_sbr"
Enable SBR (Spectral Band Replication) for ELD if set to 1, disabled
if set to 0.
.Sp
Default value is 0.
.IP \fBeld_v2\fR 4
.IX Item "eld_v2"
Enable ELDv2 (LD-MPS extension for ELD stereo signals) for ELDv2 if set to 1,
disabled if set to 0.
.Sp
Note that option is available when fdk-aac version (AACENCODER_LIB_VL0.AACENCODER_LIB_VL1.AACENCODER_LIB_VL2) > (4.0.0).
.Sp
Default value is 0.
.IP \fBsignaling\fR 4
.IX Item "signaling"
Set SBR/PS signaling style.
.Sp
It can assume one of the following values:
.RS 4
.IP \fBdefault\fR 4
.IX Item "default"
choose signaling implicitly (explicit hierarchical by default,
implicit if global header is disabled)
.IP \fBimplicit\fR 4
.IX Item "implicit"
implicit backwards compatible signaling
.IP \fBexplicit_sbr\fR 4
.IX Item "explicit_sbr"
explicit SBR, implicit PS signaling
.IP \fBexplicit_hierarchical\fR 4
.IX Item "explicit_hierarchical"
explicit hierarchical signaling
.RE
.RS 4
.Sp
Default value is \fBdefault\fR.
.RE
.IP \fBlatm\fR 4
.IX Item "latm"
Output LATM/LOAS encapsulated data if set to 1, disabled if set to 0.
.Sp
Default value is 0.
.IP \fBheader_period\fR 4
.IX Item "header_period"
Set StreamMuxConfig and PCE repetition period (in frames) for sending
in-band configuration buffers within LATM/LOAS transport layer.
.Sp
Must be a 16\-bits non-negative integer.
.Sp
Default value is 0.
.IP \fBvbr\fR 4
.IX Item "vbr"
Set VBR mode, from 1 to 5. 1 is lowest quality (though still pretty
good) and 5 is highest quality. A value of 0 will disable VBR, and CBR
(Constant Bit Rate) is enabled.
.Sp
Currently only the \fBaac_low\fR profile supports VBR encoding.
.Sp
VBR modes 1\-5 correspond to roughly the following average bit rates:
.RS 4
.IP \fB1\fR 4
.IX Item "1"
32 kbps/channel
.IP \fB2\fR 4
.IX Item "2"
40 kbps/channel
.IP \fB3\fR 4
.IX Item "3"
48\-56 kbps/channel
.IP \fB4\fR 4
.IX Item "4"
64 kbps/channel
.IP \fB5\fR 4
.IX Item "5"
about 80\-96 kbps/channel
.RE
.RS 4
.Sp
Default value is 0.
.RE
.IP \fBframe_length\fR 4
.IX Item "frame_length"
Set the audio frame length in samples. Default value is the internal
default of the library. Refer to the library's documentation for information
about supported values.
.PP
\fIExamples\fR
.IX Subsection "Examples"
.IP \(bu 4
Use \fBffmpeg\fR to convert an audio file to VBR AAC in an M4A (MP4)
container:
.Sp
.Vb 1
\&        ffmpeg \-i input.wav \-codec:a libfdk_aac \-vbr 3 output.m4a
.Ve
.IP \(bu 4
Use \fBffmpeg\fR to convert an audio file to CBR 64k kbps AAC, using the
High-Efficiency AAC profile:
.Sp
.Vb 1
\&        ffmpeg \-i input.wav \-c:a libfdk_aac \-profile:a aac_he \-b:a 64k output.m4a
.Ve
.SS liblc3
.IX Subsection "liblc3"
liblc3 LC3 (Low Complexity Communication Codec) encoder wrapper.
.PP
Requires the presence of the liblc3 headers and library during configuration.
You need to explicitly configure the build with \f(CW\*(C`\-\-enable\-liblc3\*(C'\fR.
.PP
This encoder has support for the Bluetooth SIG LC3 codec for the LE Audio
protocol, and the following features of LC3plus:
.IP \(bu 4
Frame duration of 2.5 and 5ms.
.IP \(bu 4
High-Resolution mode, 48 KHz, and 96 kHz sampling rates.
.PP
For more information see the liblc3 project at
<\fBhttps://github.com/google/liblc3\fR>.
.PP
\fIOptions\fR
.IX Subsection "Options"
.PP
The following options are mapped on the shared FFmpeg codec options.
.IP "\fBb\fR \fIbitrate\fR" 4
.IX Item "b bitrate"
Set the bit rate in bits/s. This will determine the fixed size of the encoded
frames, for a selected frame duration.
.IP "\fBar\fR \fIfrequency\fR" 4
.IX Item "ar frequency"
Set the audio sampling rate (in Hz).
.IP \fBchannels\fR 4
.IX Item "channels"
Set the number of audio channels.
.IP \fBframe_duration\fR 4
.IX Item "frame_duration"
Set the audio frame duration in milliseconds. Default value is 10ms.
Allowed frame durations are 2.5ms, 5ms, 7.5ms and 10ms.
LC3 (Bluetooth LE Audio), allows 7.5ms and 10ms; and LC3plus 2.5ms, 5ms
and 10ms.
.Sp
The 10ms frame duration is available in LC3 and LC3 plus standard.
In this mode, the produced bitstream can be referenced either as LC3 or LC3plus.
.IP "\fBhigh_resolution\fR \fIboolean\fR" 4
.IX Item "high_resolution boolean"
Enable the high-resolution mode if set to 1. The high-resolution mode is
available with all LC3plus frame durations and for a sampling rate of 48 KHz,
and 96 KHz.
.Sp
The encoder automatically turns off this mode at lower sampling rates and
activates it at 96 KHz.
.Sp
This mode should be preferred at high bitrates. In this mode, the audio
bandwidth is always up to the Nyquist frequency, compared to LC3 at 48 KHz,
which limits the bandwidth to 20 KHz.
.SS libmp3lame
.IX Subsection "libmp3lame"
LAME (Lame Ain't an MP3 Encoder) MP3 encoder wrapper.
.PP
Requires the presence of the libmp3lame headers and library during
configuration. You need to explicitly configure the build with
\&\f(CW\*(C`\-\-enable\-libmp3lame\*(C'\fR.
.PP
See \fBlibshine\fR for a fixed-point MP3 encoder, although with a
lower quality.
.PP
\fIOptions\fR
.IX Subsection "Options"
.PP
The following options are supported by the libmp3lame wrapper. The
\&\fBlame\fR\-equivalent of the options are listed in parentheses.
.IP "\fBb (\fR\fI\-b\fR\fB)\fR" 4
.IX Item "b (-b)"
Set bitrate expressed in bits/s for CBR or ABR. LAME \f(CW\*(C`bitrate\*(C'\fR is
expressed in kilobits/s.
.IP "\fBq (\fR\fI\-V\fR\fB)\fR" 4
.IX Item "q (-V)"
Set constant quality setting for VBR. This option is valid only
using the \fBffmpeg\fR command-line tool. For library interface
users, use \fBglobal_quality\fR.
.IP "\fBcompression_level (\fR\fI\-q\fR\fB)\fR" 4
.IX Item "compression_level (-q)"
Set algorithm quality. Valid arguments are integers in the 0\-9 range,
with 0 meaning highest quality but slowest, and 9 meaning fastest
while producing the worst quality.
.IP "\fBcutoff (\fR\fI\-\-lowpass\fR\fB)\fR" 4
.IX Item "cutoff (--lowpass)"
Set lowpass cutoff frequency. If unspecified, the encoder dynamically
adjusts the cutoff.
.IP \fBreservoir\fR 4
.IX Item "reservoir"
Enable use of bit reservoir when set to 1. Default value is 1. LAME
has this enabled by default, but can be overridden by use
\&\fB\-\-nores\fR option.
.IP "\fBjoint_stereo (\fR\fI\-m j\fR\fB)\fR" 4
.IX Item "joint_stereo (-m j)"
Enable the encoder to use (on a frame by frame basis) either L/R
stereo or mid/side stereo. Default value is 1.
.IP "\fBabr (\fR\fI\-\-abr\fR\fB)\fR" 4
.IX Item "abr (--abr)"
Enable the encoder to use ABR when set to 1. The \fBlame\fR
\&\fB\-\-abr\fR sets the target bitrate, while this options only
tells FFmpeg to use ABR still relies on \fBb\fR to set bitrate.
.IP "\fBcopyright (\fR\fI\-c\fR\fB)\fR" 4
.IX Item "copyright (-c)"
Set MPEG audio copyright flag when set to 1. The default value is 0
(disabled).
.IP "\fBoriginal (\fR\fI\-o\fR\fB)\fR" 4
.IX Item "original (-o)"
Set MPEG audio original flag when set to 1. The default value is 1
(enabled).
.SS libopencore-amrnb
.IX Subsection "libopencore-amrnb"
OpenCORE Adaptive Multi-Rate Narrowband encoder.
.PP
Requires the presence of the libopencore-amrnb headers and library during
configuration. You need to explicitly configure the build with
\&\f(CW\*(C`\-\-enable\-libopencore\-amrnb \-\-enable\-version3\*(C'\fR.
.PP
This is a mono-only encoder. Officially it only supports 8000Hz sample rate,
but you can override it by setting \fBstrict\fR to \fBunofficial\fR or
lower.
.PP
\fIOptions\fR
.IX Subsection "Options"
.IP \fBb\fR 4
.IX Item "b"
Set bitrate in bits per second. Only the following bitrates are supported,
otherwise libavcodec will round to the nearest valid bitrate.
.RS 4
.IP \fB4750\fR 4
.IX Item "4750"
.PD 0
.IP \fB5150\fR 4
.IX Item "5150"
.IP \fB5900\fR 4
.IX Item "5900"
.IP \fB6700\fR 4
.IX Item "6700"
.IP \fB7400\fR 4
.IX Item "7400"
.IP \fB7950\fR 4
.IX Item "7950"
.IP \fB10200\fR 4
.IX Item "10200"
.IP \fB12200\fR 4
.IX Item "12200"
.RE
.RS 4
.RE
.IP \fBdtx\fR 4
.IX Item "dtx"
.PD
Allow discontinuous transmission (generate comfort noise) when set to 1. The
default value is 0 (disabled).
.SS libopus
.IX Subsection "libopus"
libopus Opus Interactive Audio Codec encoder wrapper.
.PP
Requires the presence of the libopus headers and library during
configuration. You need to explicitly configure the build with
\&\f(CW\*(C`\-\-enable\-libopus\*(C'\fR.
.PP
\fIOption Mapping\fR
.IX Subsection "Option Mapping"
.PP
Most libopus options are modelled after the \fBopusenc\fR utility from
opus-tools. The following is an option mapping chart describing options
supported by the libopus wrapper, and their \fBopusenc\fR\-equivalent
in parentheses.
.IP "\fBb (\fR\fIbitrate\fR\fB)\fR" 4
.IX Item "b (bitrate)"
Set the bit rate in bits/s.  FFmpeg's \fBb\fR option is
expressed in bits/s, while \fBopusenc\fR's \fBbitrate\fR in
kilobits/s.
.IP "\fBvbr (\fR\fIvbr\fR\fB,\fR \fIhard-cbr\fR\fB, and\fR \fIcvbr\fR\fB)\fR" 4
.IX Item "vbr (vbr, hard-cbr, and cvbr)"
Set VBR mode. The FFmpeg \fBvbr\fR option has the following
valid arguments, with the \fBopusenc\fR equivalent options
in parentheses:
.RS 4
.IP "\fBoff (\fR\fIhard-cbr\fR\fB)\fR" 4
.IX Item "off (hard-cbr)"
Use constant bit rate encoding.
.IP "\fBon (\fR\fIvbr\fR\fB)\fR" 4
.IX Item "on (vbr)"
Use variable bit rate encoding (the default).
.IP "\fBconstrained (\fR\fIcvbr\fR\fB)\fR" 4
.IX Item "constrained (cvbr)"
Use constrained variable bit rate encoding.
.RE
.RS 4
.RE
.IP "\fBcompression_level (\fR\fIcomp\fR\fB)\fR" 4
.IX Item "compression_level (comp)"
Set encoding algorithm complexity. Valid options are integers in
the 0\-10 range. 0 gives the fastest encodes but lower quality, while 10
gives the highest quality but slowest encoding. The default is 10.
.IP "\fBframe_duration (\fR\fIframesize\fR\fB)\fR" 4
.IX Item "frame_duration (framesize)"
Set maximum frame size, or duration of a frame in milliseconds. The
argument must be exactly the following: 2.5, 5, 10, 20, 40, 60. Smaller
frame sizes achieve lower latency but less quality at a given bitrate.
Sizes greater than 20ms are only interesting at fairly low bitrates.
The default is 20ms.
.IP "\fBpacket_loss (\fR\fIexpect-loss\fR\fB)\fR" 4
.IX Item "packet_loss (expect-loss)"
Set expected packet loss percentage. The default is 0.
.IP "\fBfec (\fR\fIn/a\fR\fB)\fR" 4
.IX Item "fec (n/a)"
Enable inband forward error correction. \fBpacket_loss\fR must be non-zero
to take advantage \- frequency of FEC 'side\-data' is proportional to expected packet loss.
Default is disabled.
.IP "\fBapplication (N.A.)\fR" 4
.IX Item "application (N.A.)"
Set intended application type. Valid options are listed below:
.RS 4
.IP \fBvoip\fR 4
.IX Item "voip"
Favor improved speech intelligibility.
.IP \fBaudio\fR 4
.IX Item "audio"
Favor faithfulness to the input (the default).
.IP \fBlowdelay\fR 4
.IX Item "lowdelay"
Restrict to only the lowest delay modes by disabling voice-optimized
modes.
.RE
.RS 4
.RE
.IP "\fBcutoff (N.A.)\fR" 4
.IX Item "cutoff (N.A.)"
Set cutoff bandwidth in Hz. The argument must be exactly one of the
following: 4000, 6000, 8000, 12000, or 20000, corresponding to
narrowband, mediumband, wideband, super wideband, and fullband
respectively. The default is 0 (cutoff disabled). Note that libopus
forces a wideband cutoff for bitrates < 15 kbps, unless CELT-only
(\fBapplication\fR set to \fBlowdelay\fR) mode is used.
.IP "\fBmapping_family (\fR\fImapping_family\fR\fB)\fR" 4
.IX Item "mapping_family (mapping_family)"
Set channel mapping family to be used by the encoder. The default value of \-1
uses mapping family 0 for mono and stereo inputs, and mapping family 1
otherwise. The default also disables the surround masking and LFE bandwidth
optimizations in libopus, and requires that the input contains 8 channels or
fewer.
.Sp
Other values include 0 for mono and stereo, 1 for surround sound with masking
and LFE bandwidth optimizations, and 255 for independent streams with an
unspecified channel layout.
.IP "\fBapply_phase_inv (N.A.) (requires libopus >= 1.2)\fR" 4
.IX Item "apply_phase_inv (N.A.) (requires libopus >= 1.2)"
If set to 0, disables the use of phase inversion for intensity stereo,
improving the quality of mono downmixes, but slightly reducing normal stereo
quality. The default is 1 (phase inversion enabled).
.SS libshine
.IX Subsection "libshine"
Shine Fixed-Point MP3 encoder wrapper.
.PP
Shine is a fixed-point MP3 encoder. It has a far better performance on
platforms without an FPU, e.g. armel CPUs, and some phones and tablets.
However, as it is more targeted on performance than quality, it is not on par
with LAME and other production-grade encoders quality-wise. Also, according to
the project's homepage, this encoder may not be free of bugs as the code was
written a long time ago and the project was dead for at least 5 years.
.PP
This encoder only supports stereo and mono input. This is also CBR-only.
.PP
The original project (last updated in early 2007) is at
<\fBhttp://sourceforge.net/projects/libshine\-fxp/\fR>. We only support the
updated fork by the Savonet/Liquidsoap project at <\fBhttps://github.com/savonet/shine\fR>.
.PP
Requires the presence of the libshine headers and library during
configuration. You need to explicitly configure the build with
\&\f(CW\*(C`\-\-enable\-libshine\*(C'\fR.
.PP
See also \fBlibmp3lame\fR.
.PP
\fIOptions\fR
.IX Subsection "Options"
.PP
The following options are supported by the libshine wrapper. The
\&\fBshineenc\fR\-equivalent of the options are listed in parentheses.
.IP "\fBb (\fR\fI\-b\fR\fB)\fR" 4
.IX Item "b (-b)"
Set bitrate expressed in bits/s for CBR. \fBshineenc\fR \fB\-b\fR option
is expressed in kilobits/s.
.SS libtwolame
.IX Subsection "libtwolame"
TwoLAME MP2 encoder wrapper.
.PP
Requires the presence of the libtwolame headers and library during
configuration. You need to explicitly configure the build with
\&\f(CW\*(C`\-\-enable\-libtwolame\*(C'\fR.
.PP
\fIOptions\fR
.IX Subsection "Options"
.PP
The following options are supported by the libtwolame wrapper. The
\&\fBtwolame\fR\-equivalent options follow the FFmpeg ones and are in
parentheses.
.IP "\fBb (\fR\fI\-b\fR\fB)\fR" 4
.IX Item "b (-b)"
Set bitrate expressed in bits/s for CBR. \fBtwolame\fR \fBb\fR
option is expressed in kilobits/s. Default value is 128k.
.IP "\fBq (\fR\fI\-V\fR\fB)\fR" 4
.IX Item "q (-V)"
Set quality for experimental VBR support. Maximum value range is
from \-50 to 50, useful range is from \-10 to 10. The higher the
value, the better the quality. This option is valid only using the
\&\fBffmpeg\fR command-line tool. For library interface users,
use \fBglobal_quality\fR.
.IP "\fBmode (\fR\fI\-\-mode\fR\fB)\fR" 4
.IX Item "mode (--mode)"
Set the mode of the resulting audio. Possible values:
.RS 4
.IP \fBauto\fR 4
.IX Item "auto"
Choose mode automatically based on the input. This is the default.
.IP \fBstereo\fR 4
.IX Item "stereo"
Stereo
.IP \fBjoint_stereo\fR 4
.IX Item "joint_stereo"
Joint stereo
.IP \fBdual_channel\fR 4
.IX Item "dual_channel"
Dual channel
.IP \fBmono\fR 4
.IX Item "mono"
Mono
.RE
.RS 4
.RE
.IP "\fBpsymodel (\fR\fI\-\-psyc\-mode\fR\fB)\fR" 4
.IX Item "psymodel (--psyc-mode)"
Set psychoacoustic model to use in encoding. The argument must be
an integer between \-1 and 4, inclusive. The higher the value, the
better the quality. The default value is 3.
.IP "\fBenergy_levels (\fR\fI\-\-energy\fR\fB)\fR" 4
.IX Item "energy_levels (--energy)"
Enable energy levels extensions when set to 1. The default value is
0 (disabled).
.IP "\fBerror_protection (\fR\fI\-\-protect\fR\fB)\fR" 4
.IX Item "error_protection (--protect)"
Enable CRC error protection when set to 1. The default value is 0
(disabled).
.IP "\fBcopyright (\fR\fI\-\-copyright\fR\fB)\fR" 4
.IX Item "copyright (--copyright)"
Set MPEG audio copyright flag when set to 1. The default value is 0
(disabled).
.IP "\fBoriginal (\fR\fI\-\-original\fR\fB)\fR" 4
.IX Item "original (--original)"
Set MPEG audio original flag when set to 1. The default value is 0
(disabled).
.SS libvo-amrwbenc
.IX Subsection "libvo-amrwbenc"
VisualOn Adaptive Multi-Rate Wideband encoder.
.PP
Requires the presence of the libvo-amrwbenc headers and library during
configuration. You need to explicitly configure the build with
\&\f(CW\*(C`\-\-enable\-libvo\-amrwbenc \-\-enable\-version3\*(C'\fR.
.PP
This is a mono-only encoder. Officially it only supports 16000Hz sample
rate, but you can override it by setting \fBstrict\fR to
\&\fBunofficial\fR or lower.
.PP
\fIOptions\fR
.IX Subsection "Options"
.IP \fBb\fR 4
.IX Item "b"
Set bitrate in bits/s. Only the following bitrates are supported, otherwise
libavcodec will round to the nearest valid bitrate.
.RS 4
.IP \fB6600\fR 4
.IX Item "6600"
.PD 0
.IP \fB8850\fR 4
.IX Item "8850"
.IP \fB12650\fR 4
.IX Item "12650"
.IP \fB14250\fR 4
.IX Item "14250"
.IP \fB15850\fR 4
.IX Item "15850"
.IP \fB18250\fR 4
.IX Item "18250"
.IP \fB19850\fR 4
.IX Item "19850"
.IP \fB23050\fR 4
.IX Item "23050"
.IP \fB23850\fR 4
.IX Item "23850"
.RE
.RS 4
.RE
.IP \fBdtx\fR 4
.IX Item "dtx"
.PD
Allow discontinuous transmission (generate comfort noise) when set to 1. The
default value is 0 (disabled).
.SS libvorbis
.IX Subsection "libvorbis"
libvorbis encoder wrapper.
.PP
Requires the presence of the libvorbisenc headers and library during
configuration. You need to explicitly configure the build with
\&\f(CW\*(C`\-\-enable\-libvorbis\*(C'\fR.
.PP
\fIOptions\fR
.IX Subsection "Options"
.PP
The following options are supported by the libvorbis wrapper. The
\&\fBoggenc\fR\-equivalent of the options are listed in parentheses.
.PP
To get a more accurate and extensive documentation of the libvorbis
options, consult the libvorbisenc's and \fBoggenc\fR's documentations.
See <\fBhttp://xiph.org/vorbis/\fR>,
<\fBhttp://wiki.xiph.org/Vorbis\-tools\fR>, and \fBoggenc\fR\|(1).
.IP "\fBb (\fR\fI\-b\fR\fB)\fR" 4
.IX Item "b (-b)"
Set bitrate expressed in bits/s for ABR. \fBoggenc\fR \fB\-b\fR is
expressed in kilobits/s.
.IP "\fBq (\fR\fI\-q\fR\fB)\fR" 4
.IX Item "q (-q)"
Set constant quality setting for VBR. The value should be a float
number in the range of \-1.0 to 10.0. The higher the value, the better
the quality. The default value is \fB3.0\fR.
.Sp
This option is valid only using the \fBffmpeg\fR command-line tool.
For library interface users, use \fBglobal_quality\fR.
.IP "\fBcutoff (\fR\fI\-\-advanced\-encode\-option lowpass_frequency=N\fR\fB)\fR" 4
.IX Item "cutoff (--advanced-encode-option lowpass_frequency=N)"
Set cutoff bandwidth in Hz, a value of 0 disables cutoff. \fBoggenc\fR's
related option is expressed in kHz. The default value is \fB0\fR (cutoff
disabled).
.IP "\fBminrate (\fR\fI\-m\fR\fB)\fR" 4
.IX Item "minrate (-m)"
Set minimum bitrate expressed in bits/s. \fBoggenc\fR \fB\-m\fR is
expressed in kilobits/s.
.IP "\fBmaxrate (\fR\fI\-M\fR\fB)\fR" 4
.IX Item "maxrate (-M)"
Set maximum bitrate expressed in bits/s. \fBoggenc\fR \fB\-M\fR is
expressed in kilobits/s. This only has effect on ABR mode.
.IP "\fBiblock (\fR\fI\-\-advanced\-encode\-option impulse_noisetune=N\fR\fB)\fR" 4
.IX Item "iblock (--advanced-encode-option impulse_noisetune=N)"
Set noise floor bias for impulse blocks. The value is a float number from
\&\-15.0 to 0.0. A negative bias instructs the encoder to pay special attention
to the crispness of transients in the encoded audio. The tradeoff for better
transient response is a higher bitrate.
.SS mjpeg
.IX Subsection "mjpeg"
Motion JPEG encoder.
.PP
\fIOptions\fR
.IX Subsection "Options"
.IP \fBhuffman\fR 4
.IX Item "huffman"
Set the huffman encoding strategy. Possible values:
.RS 4
.IP \fBdefault\fR 4
.IX Item "default"
Use the default huffman tables. This is the default strategy.
.IP \fBoptimal\fR 4
.IX Item "optimal"
Compute and use optimal huffman tables.
.RE
.RS 4
.RE
.SS wavpack
.IX Subsection "wavpack"
WavPack lossless audio encoder.
.PP
\fIOptions\fR
.IX Subsection "Options"
.PP
The equivalent options for \fBwavpack\fR command line utility are listed in
parentheses.
.PP
Shared options
.IX Subsection "Shared options"
.PP
The following shared options are effective for this encoder. Only special notes
about this particular encoder will be documented here. For the general meaning
of the options, see \fBthe Codec Options chapter\fR.
.IP "\fBframe_size (\fR\fI\-\-blocksize\fR\fB)\fR" 4
.IX Item "frame_size (--blocksize)"
For this encoder, the range for this option is between 128 and 131072. Default
is automatically decided based on sample rate and number of channel.
.Sp
For the complete formula of calculating default, see
\&\fIlibavcodec/wavpackenc.c\fR.
.IP "\fBcompression_level (\fR\fI\-f\fR\fB,\fR \fI\-h\fR\fB,\fR \fI\-hh\fR\fB, and\fR \fI\-x\fR\fB)\fR" 4
.IX Item "compression_level (-f, -h, -hh, and -x)"
.PP
Private options
.IX Subsection "Private options"
.IP "\fBjoint_stereo (\fR\fI\-j\fR\fB)\fR" 4
.IX Item "joint_stereo (-j)"
Set whether to enable joint stereo. Valid values are:
.RS 4
.IP "\fBon (\fR\fI1\fR\fB)\fR" 4
.IX Item "on (1)"
Force mid/side audio encoding.
.IP "\fBoff (\fR\fI0\fR\fB)\fR" 4
.IX Item "off (0)"
Force left/right audio encoding.
.IP \fBauto\fR 4
.IX Item "auto"
Let the encoder decide automatically.
.RE
.RS 4
.RE
.IP \fBoptimize_mono\fR 4
.IX Item "optimize_mono"
Set whether to enable optimization for mono. This option is only effective for
non-mono streams. Available values:
.RS 4
.IP \fBon\fR 4
.IX Item "on"
enabled
.IP \fBoff\fR 4
.IX Item "off"
disabled
.RE
.RS 4
.RE
.SH "VIDEO ENCODERS"
.IX Header "VIDEO ENCODERS"
A description of some of the currently available video encoders
follows.
.SS "a64_multi, a64_multi5"
.IX Subsection "a64_multi, a64_multi5"
A64 / Commodore 64 multicolor charset encoder. \f(CW\*(C`a64_multi5\*(C'\fR is extended with 5th color (colram).
.SS Cinepak
.IX Subsection "Cinepak"
Cinepak aka CVID encoder.
Compatible with Windows 3.1 and vintage MacOS.
.PP
\fIOptions\fR
.IX Subsection "Options"
.IP "\fBg\fR \fIinteger\fR" 4
.IX Item "g integer"
Keyframe interval.
A keyframe is inserted at least every \f(CW\*(C`\-g\*(C'\fR frames, sometimes sooner.
.IP "\fBq:v\fR \fIinteger\fR" 4
.IX Item "q:v integer"
Quality factor. Lower is better. Higher gives lower bitrate.
The following table lists bitrates when encoding akiyo_cif.y4m for various values of \f(CW\*(C`\-q:v\*(C'\fR with \f(CW\*(C`\-g 100\*(C'\fR:
.RS 4
.ie n .IP "\fR\fB""\-q:v 1""\fR\fB 1918 kb/s\fR" 4
.el .IP "\fR\f(CB\-q:v 1\fR\fB 1918 kb/s\fR" 4
.IX Item "-q:v 1 1918 kb/s"
.PD 0
.ie n .IP "\fR\fB""\-q:v 2""\fR\fB 1735 kb/s\fR" 4
.el .IP "\fR\f(CB\-q:v 2\fR\fB 1735 kb/s\fR" 4
.IX Item "-q:v 2 1735 kb/s"
.ie n .IP "\fR\fB""\-q:v 4""\fR\fB 1500 kb/s\fR" 4
.el .IP "\fR\f(CB\-q:v 4\fR\fB 1500 kb/s\fR" 4
.IX Item "-q:v 4 1500 kb/s"
.ie n .IP "\fR\fB""\-q:v 10""\fR\fB 1041 kb/s\fR" 4
.el .IP "\fR\f(CB\-q:v 10\fR\fB 1041 kb/s\fR" 4
.IX Item "-q:v 10 1041 kb/s"
.ie n .IP "\fR\fB""\-q:v 20""\fR\fB 826 kb/s\fR" 4
.el .IP "\fR\f(CB\-q:v 20\fR\fB 826 kb/s\fR" 4
.IX Item "-q:v 20 826 kb/s"
.ie n .IP "\fR\fB""\-q:v 40""\fR\fB 553 kb/s\fR" 4
.el .IP "\fR\f(CB\-q:v 40\fR\fB 553 kb/s\fR" 4
.IX Item "-q:v 40 553 kb/s"
.ie n .IP "\fR\fB""\-q:v 100""\fR\fB 394 kb/s\fR" 4
.el .IP "\fR\f(CB\-q:v 100\fR\fB 394 kb/s\fR" 4
.IX Item "-q:v 100 394 kb/s"
.ie n .IP "\fR\fB""\-q:v 200""\fR\fB 312 kb/s\fR" 4
.el .IP "\fR\f(CB\-q:v 200\fR\fB 312 kb/s\fR" 4
.IX Item "-q:v 200 312 kb/s"
.ie n .IP "\fR\fB""\-q:v 400""\fR\fB 266 kb/s\fR" 4
.el .IP "\fR\f(CB\-q:v 400\fR\fB 266 kb/s\fR" 4
.IX Item "-q:v 400 266 kb/s"
.ie n .IP "\fR\fB""\-q:v 1000""\fR\fB 237 kb/s\fR" 4
.el .IP "\fR\f(CB\-q:v 1000\fR\fB 237 kb/s\fR" 4
.IX Item "-q:v 1000 237 kb/s"
.RE
.RS 4
.RE
.IP "\fBmax_extra_cb_iterations\fR \fIinteger\fR" 4
.IX Item "max_extra_cb_iterations integer"
.PD
Max extra codebook recalculation passes, more is better and slower.
.IP "\fBskip_empty_cb\fR \fIboolean\fR" 4
.IX Item "skip_empty_cb boolean"
Avoid wasting bytes, ignore vintage MacOS decoder.
.IP "\fBmax_strips\fR \fIinteger\fR" 4
.IX Item "max_strips integer"
.PD 0
.IP "\fBmin_strips\fR \fIinteger\fR" 4
.IX Item "min_strips integer"
.PD
The minimum and maximum number of strips to use.
Wider range sometimes improves quality.
More strips is generally better quality but costs more bits.
Fewer strips tend to yield more keyframes.
Vintage compatible is 1..3.
.IP "\fBstrip_number_adaptivity\fR \fIinteger\fR" 4
.IX Item "strip_number_adaptivity integer"
How much number of strips is allowed to change between frames.
Higher is better but slower.
.SS ffv1
.IX Subsection "ffv1"
FFv1 Encoder
.PP
\fIOptions\fR
.IX Subsection "Options"
.PP
The following options are supported by FFmpeg's FFv1 encoder.
.IP \fBcontext\fR 4
.IX Item "context"
Sets the context size, 0 (default) is small, 1 is big.
.IP \fBcoder\fR 4
.IX Item "coder"
Set the coder,
.RS 4
.IP \fBrice\fR 4
.IX Item "rice"
Golomb rice coder
.IP \fBrange_def\fR 4
.IX Item "range_def"
Range coder with default table
.IP \fBrange_tab\fR 4
.IX Item "range_tab"
Range coder with custom table
.RE
.RS 4
.RE
.IP \fBslicecrc\fR 4
.IX Item "slicecrc"
\&\-1 (default, automatic), 1 use crc with zero initial and final state, 2 use crc with non zero initial and final state
.IP \fBqtable\fR 4
.IX Item "qtable"
.RS 4
.PD 0
.IP \fBdefault\fR 4
.IX Item "default"
.PD
default, automatic
.IP \fB8bit\fR 4
.IX Item "8bit"
use 8bit default
.IP \fBgreater8bit\fR 4
.IX Item "greater8bit"
use >8bit default
.RE
.RS 4
.RE
.IP \fBremap_optimizer\fR 4
.IX Item "remap_optimizer"
0 \- 5, default 3, how much effort the encoder puts into optimizing the remap table.
.SS GIF
.IX Subsection "GIF"
GIF image/animation encoder.
.PP
\fIOptions\fR
.IX Subsection "Options"
.IP "\fBgifflags\fR \fIinteger\fR" 4
.IX Item "gifflags integer"
Sets the flags used for GIF encoding.
.RS 4
.IP \fBoffsetting\fR 4
.IX Item "offsetting"
Enables picture offsetting.
.Sp
Default is enabled.
.IP \fBtransdiff\fR 4
.IX Item "transdiff"
Enables transparency detection between frames.
.Sp
Default is enabled.
.RE
.RS 4
.RE
.IP "\fBgifimage\fR \fIinteger\fR" 4
.IX Item "gifimage integer"
Enables encoding one full GIF image per frame, rather than an animated GIF.
.Sp
Default value is \fB0\fR.
.IP "\fBglobal_palette\fR \fIinteger\fR" 4
.IX Item "global_palette integer"
Writes a palette to the global GIF header where feasible.
.Sp
If disabled, every frame will always have a palette written, even if there
is a global palette supplied.
.Sp
Default value is \fB1\fR.
.SS Hap
.IX Subsection "Hap"
Vidvox Hap video encoder.
.PP
\fIOptions\fR
.IX Subsection "Options"
.IP "\fBformat\fR \fIinteger\fR" 4
.IX Item "format integer"
Specifies the Hap format to encode.
.RS 4
.IP \fBhap\fR 4
.IX Item "hap"
.PD 0
.IP \fBhap_alpha\fR 4
.IX Item "hap_alpha"
.IP \fBhap_q\fR 4
.IX Item "hap_q"
.RE
.RS 4
.PD
.Sp
Default value is \fBhap\fR.
.RE
.IP "\fBchunks\fR \fIinteger\fR" 4
.IX Item "chunks integer"
Specifies the number of chunks to split frames into, between 1 and 64. This
permits multithreaded decoding of large frames, potentially at the cost of
data-rate. The encoder may modify this value to divide frames evenly.
.Sp
Default value is \fI1\fR.
.IP "\fBcompressor\fR \fIinteger\fR" 4
.IX Item "compressor integer"
Specifies the second-stage compressor to use. If set to \fBnone\fR,
\&\fBchunks\fR will be limited to 1, as chunked uncompressed frames offer no
benefit.
.RS 4
.IP \fBnone\fR 4
.IX Item "none"
.PD 0
.IP \fBsnappy\fR 4
.IX Item "snappy"
.RE
.RS 4
.PD
.Sp
Default value is \fBsnappy\fR.
.RE
.SS jpeg2000
.IX Subsection "jpeg2000"
The native jpeg 2000 encoder is lossy by default, the \f(CW\*(C`\-q:v\*(C'\fR
option can be used to set the encoding quality. Lossless encoding
can be selected with \f(CW\*(C`\-pred 1\*(C'\fR.
.PP
\fIOptions\fR
.IX Subsection "Options"
.IP "\fBformat\fR \fIinteger\fR" 4
.IX Item "format integer"
Can be set to either \f(CW\*(C`j2k\*(C'\fR or \f(CW\*(C`jp2\*(C'\fR (the default) that
makes it possible to store non-rgb pix_fmts.
.IP "\fBtile_width\fR \fIinteger\fR" 4
.IX Item "tile_width integer"
Sets tile width. Range is 1 to 1073741824. Default is 256.
.IP "\fBtile_height\fR \fIinteger\fR" 4
.IX Item "tile_height integer"
Sets tile height. Range is 1 to 1073741824. Default is 256.
.IP "\fBpred\fR \fIinteger\fR" 4
.IX Item "pred integer"
Allows setting the discrete wavelet transform (DWT) type
.RS 4
.IP "\fBdwt97int (Lossy)\fR" 4
.IX Item "dwt97int (Lossy)"
.PD 0
.IP "\fBdwt53 (Lossless)\fR" 4
.IX Item "dwt53 (Lossless)"
.RE
.RS 4
.PD
.Sp
Default is \f(CW\*(C`dwt97int\*(C'\fR
.RE
.IP "\fBsop\fR \fIboolean\fR" 4
.IX Item "sop boolean"
Enable this to add SOP marker at the start of each packet. Disabled by default.
.IP "\fBeph\fR \fIboolean\fR" 4
.IX Item "eph boolean"
Enable this to add EPH marker at the end of each packet header. Disabled by default.
.IP "\fBprog\fR \fIinteger\fR" 4
.IX Item "prog integer"
Sets the progression order to be used by the encoder.
Possible values are:
.RS 4
.IP \fBlrcp\fR 4
.IX Item "lrcp"
.PD 0
.IP \fBrlcp\fR 4
.IX Item "rlcp"
.IP \fBrpcl\fR 4
.IX Item "rpcl"
.IP \fBpcrl\fR 4
.IX Item "pcrl"
.IP \fBcprl\fR 4
.IX Item "cprl"
.RE
.RS 4
.PD
.Sp
Set to \f(CW\*(C`lrcp\*(C'\fR by default.
.RE
.IP "\fBlayer_rates\fR \fIstring\fR" 4
.IX Item "layer_rates string"
By default, when this option is not used, compression is done using the quality metric.
This option allows for compression using compression ratio. The compression ratio for each
level could be specified. The compression ratio of a layer \f(CW\*(C`l\*(C'\fR species the what ratio of
total file size is contained in the first \f(CW\*(C`l\*(C'\fR layers.
.Sp
Example usage:
.Sp
.Vb 1
\&        ffmpeg \-i input.bmp \-c:v jpeg2000 \-layer_rates "100,10,1" output.j2k
.Ve
.Sp
This would compress the image to contain 3 layers, where the data contained in the
first layer would be compressed by 1000 times, compressed by 100 in the first two layers,
and shall contain all data while using all 3 layers.
.SS librav1e
.IX Subsection "librav1e"
rav1e AV1 encoder wrapper.
.PP
Requires the presence of the rav1e headers and library during configuration.
You need to explicitly configure the build with \f(CW\*(C`\-\-enable\-librav1e\*(C'\fR.
.PP
\fIOptions\fR
.IX Subsection "Options"
.IP \fBqmax\fR 4
.IX Item "qmax"
Sets the maximum quantizer to use when using bitrate mode.
.IP \fBqmin\fR 4
.IX Item "qmin"
Sets the minimum quantizer to use when using bitrate mode.
.IP \fBqp\fR 4
.IX Item "qp"
Uses quantizer mode to encode at the given quantizer (0\-255).
.IP \fBspeed\fR 4
.IX Item "speed"
Selects the speed preset (0\-10) to encode with.
.IP \fBtiles\fR 4
.IX Item "tiles"
Selects how many tiles to encode with.
.IP \fBtile-rows\fR 4
.IX Item "tile-rows"
Selects how many rows of tiles to encode with.
.IP \fBtile-columns\fR 4
.IX Item "tile-columns"
Selects how many columns of tiles to encode with.
.IP \fBrav1e\-params\fR 4
.IX Item "rav1e-params"
Set rav1e options using a list of \fIkey\fR=\fIvalue\fR pairs separated
by ":". See \fBrav1e \-\-help\fR for a list of options.
.Sp
For example to specify librav1e encoding options with \fB\-rav1e\-params\fR:
.Sp
.Vb 1
\&        ffmpeg \-i input \-c:v librav1e \-b:v 500K \-rav1e\-params speed=5:low_latency=true output.mp4
.Ve
.SS libaom\-av1
.IX Subsection "libaom-av1"
libaom AV1 encoder wrapper.
.PP
Requires the presence of the libaom headers and library during
configuration.  You need to explicitly configure the build with
\&\f(CW\*(C`\-\-enable\-libaom\*(C'\fR.
.PP
\fIOptions\fR
.IX Subsection "Options"
.PP
The wrapper supports the following standard libavcodec options:
.IP \fBb\fR 4
.IX Item "b"
Set bitrate target in bits/second.  By default this will use
variable-bitrate mode.  If \fBmaxrate\fR and \fBminrate\fR are
also set to the same value then it will use constant-bitrate mode,
otherwise if \fBcrf\fR is set as well then it will use
constrained-quality mode.
.IP "\fBg keyint_min\fR" 4
.IX Item "g keyint_min"
Set key frame placement.  The GOP size sets the maximum distance between
key frames; if zero the output stream will be intra-only.  The minimum
distance is ignored unless it is the same as the GOP size, in which case
key frames will always appear at a fixed interval.  Not set by default,
so without this option the library has completely free choice about
where to place key frames.
.IP "\fBqmin qmax\fR" 4
.IX Item "qmin qmax"
Set minimum/maximum quantisation values.  Valid range is from 0 to 63
(warning: this does not match the quantiser values actually used by AV1
\&\- divide by four to map real quantiser values to this range).  Defaults
to min/max (no constraint).
.IP "\fBminrate maxrate bufsize rc_init_occupancy\fR" 4
.IX Item "minrate maxrate bufsize rc_init_occupancy"
Set rate control buffering parameters.  Not used if not set \- defaults
to unconstrained variable bitrate.
.IP \fBthreads\fR 4
.IX Item "threads"
Set the number of threads to use while encoding.  This may require the
\&\fBtiles\fR or \fBrow-mt\fR options to also be set to actually
use the specified number of threads fully. Defaults to the number of
hardware threads supported by the host machine.
.IP \fBprofile\fR 4
.IX Item "profile"
Set the encoding profile.  Defaults to using the profile which matches
the bit depth and chroma subsampling of the input.
.PP
The wrapper also has some specific options:
.IP \fBcpu-used\fR 4
.IX Item "cpu-used"
Set the quality/encoding speed tradeoff.  Valid range is from 0 to 8,
higher numbers indicating greater speed and lower quality.  The default
value is 1, which will be slow and high quality.
.IP \fBauto-alt-ref\fR 4
.IX Item "auto-alt-ref"
Enable use of alternate reference frames.  Defaults to the internal
default of the library.
.IP "\fBarnr-max-frames (\fR\fIframes\fR\fB)\fR" 4
.IX Item "arnr-max-frames (frames)"
Set altref noise reduction max frame count. Default is \-1.
.IP "\fBarnr-strength (\fR\fIstrength\fR\fB)\fR" 4
.IX Item "arnr-strength (strength)"
Set altref noise reduction filter strength. Range is \-1 to 6. Default is \-1.
.IP "\fBaq-mode (\fR\fIaq-mode\fR\fB)\fR" 4
.IX Item "aq-mode (aq-mode)"
Set adaptive quantization mode. Possible values:
.RS 4
.IP "\fBnone (\fR\fI0\fR\fB)\fR" 4
.IX Item "none (0)"
Disabled.
.IP "\fBvariance (\fR\fI1\fR\fB)\fR" 4
.IX Item "variance (1)"
Variance-based.
.IP "\fBcomplexity (\fR\fI2\fR\fB)\fR" 4
.IX Item "complexity (2)"
Complexity-based.
.IP "\fBcyclic (\fR\fI3\fR\fB)\fR" 4
.IX Item "cyclic (3)"
Cyclic refresh.
.RE
.RS 4
.RE
.IP "\fBtune (\fR\fItune\fR\fB)\fR" 4
.IX Item "tune (tune)"
Set the distortion metric the encoder is tuned with. Default is \f(CW\*(C`psnr\*(C'\fR.
.RS 4
.IP "\fBpsnr (\fR\fI0\fR\fB)\fR" 4
.IX Item "psnr (0)"
.PD 0
.IP "\fBssim (\fR\fI1\fR\fB)\fR" 4
.IX Item "ssim (1)"
.RE
.RS 4
.RE
.IP \fBlag-in-frames\fR 4
.IX Item "lag-in-frames"
.PD
Set the maximum number of frames which the encoder may keep in flight
at any one time for lookahead purposes.  Defaults to the internal
default of the library.
.IP \fBerror-resilience\fR 4
.IX Item "error-resilience"
Enable error resilience features:
.RS 4
.IP \fBdefault\fR 4
.IX Item "default"
Improve resilience against losses of whole frames.
.RE
.RS 4
.Sp
Not enabled by default.
.RE
.IP \fBcrf\fR 4
.IX Item "crf"
Set the quality/size tradeoff for constant-quality (no bitrate target)
and constrained-quality (with maximum bitrate target) modes. Valid
range is 0 to 63, higher numbers indicating lower quality and smaller
output size.  Only used if set; by default only the bitrate target is
used.
.IP \fBstatic-thresh\fR 4
.IX Item "static-thresh"
Set a change threshold on blocks below which they will be skipped by
the encoder.  Defined in arbitrary units as a nonnegative integer,
defaulting to zero (no blocks are skipped).
.IP \fBdrop-threshold\fR 4
.IX Item "drop-threshold"
Set a threshold for dropping frames when close to rate control bounds.
Defined as a percentage of the target buffer \- when the rate control
buffer falls below this percentage, frames will be dropped until it
has refilled above the threshold.  Defaults to zero (no frames are
dropped).
.IP "\fBdenoise-noise-level (\fR\fIlevel\fR\fB)\fR" 4
.IX Item "denoise-noise-level (level)"
Amount of noise to be removed for grain synthesis. Grain synthesis is disabled if
this option is not set or set to 0.
.IP "\fBdenoise-block-size (\fR\fIpixels\fR\fB)\fR" 4
.IX Item "denoise-block-size (pixels)"
Block size used for denoising for grain synthesis. If not set, AV1 codec
uses the default value of 32.
.IP "\fBundershoot-pct (\fR\fIpct\fR\fB)\fR" 4
.IX Item "undershoot-pct (pct)"
Set datarate undershoot (min) percentage of the target bitrate. Range is \-1 to 100.
Default is \-1.
.IP "\fBovershoot-pct (\fR\fIpct\fR\fB)\fR" 4
.IX Item "overshoot-pct (pct)"
Set datarate overshoot (max) percentage of the target bitrate. Range is \-1 to 1000.
Default is \-1.
.IP "\fBminsection-pct (\fR\fIpct\fR\fB)\fR" 4
.IX Item "minsection-pct (pct)"
Minimum percentage variation of the GOP bitrate from the target bitrate. If minsection-pct
is not set, the libaomenc wrapper computes it as follows: \f(CW\*(C`(minrate * 100 / bitrate)\*(C'\fR.
Range is \-1 to 100. Default is \-1 (unset).
.IP "\fBmaxsection-pct (\fR\fIpct\fR\fB)\fR" 4
.IX Item "maxsection-pct (pct)"
Maximum percentage variation of the GOP bitrate from the target bitrate. If maxsection-pct
is not set, the libaomenc wrapper computes it as follows: \f(CW\*(C`(maxrate * 100 / bitrate)\*(C'\fR.
Range is \-1 to 5000. Default is \-1 (unset).
.IP "\fBframe-parallel (\fR\fIboolean\fR\fB)\fR" 4
.IX Item "frame-parallel (boolean)"
Enable frame parallel decodability features. Default is true.
.IP \fBtiles\fR 4
.IX Item "tiles"
Set the number of tiles to encode the input video with, as columns x
rows.  Larger numbers allow greater parallelism in both encoding and
decoding, but may decrease coding efficiency.  Defaults to the minimum
number of tiles required by the size of the input video (this is 1x1
(that is, a single tile) for sizes up to and including 4K).
.IP "\fBtile-columns tile-rows\fR" 4
.IX Item "tile-columns tile-rows"
Set the number of tiles as log2 of the number of tile rows and columns.
Provided for compatibility with libvpx/VP9.
.IP "\fBrow-mt (Requires libaom >= 1.0.0\-759\-g90a15f4f2)\fR" 4
.IX Item "row-mt (Requires libaom >= 1.0.0-759-g90a15f4f2)"
Enable row based multi-threading. Disabled by default.
.IP "\fBenable-cdef (\fR\fIboolean\fR\fB)\fR" 4
.IX Item "enable-cdef (boolean)"
Enable Constrained Directional Enhancement Filter. The libaom\-av1
encoder enables CDEF by default.
.IP "\fBenable-restoration (\fR\fIboolean\fR\fB)\fR" 4
.IX Item "enable-restoration (boolean)"
Enable Loop Restoration Filter. Default is true for libaom\-av1.
.IP "\fBenable-global-motion (\fR\fIboolean\fR\fB)\fR" 4
.IX Item "enable-global-motion (boolean)"
Enable the use of global motion for block prediction. Default is true.
.IP "\fBenable-intrabc (\fR\fIboolean\fR\fB)\fR" 4
.IX Item "enable-intrabc (boolean)"
Enable block copy mode for intra block prediction. This mode is
useful for screen content. Default is true.
.IP "\fBenable-rect-partitions (\fR\fIboolean\fR\fB) (Requires libaom >= v2.0.0)\fR" 4
.IX Item "enable-rect-partitions (boolean) (Requires libaom >= v2.0.0)"
Enable rectangular partitions. Default is true.
.IP "\fBenable\-1to4\-partitions (\fR\fIboolean\fR\fB) (Requires libaom >= v2.0.0)\fR" 4
.IX Item "enable-1to4-partitions (boolean) (Requires libaom >= v2.0.0)"
Enable 1:4/4:1 partitions. Default is true.
.IP "\fBenable-ab-partitions (\fR\fIboolean\fR\fB) (Requires libaom >= v2.0.0)\fR" 4
.IX Item "enable-ab-partitions (boolean) (Requires libaom >= v2.0.0)"
Enable AB shape partitions. Default is true.
.IP "\fBenable-angle-delta (\fR\fIboolean\fR\fB) (Requires libaom >= v2.0.0)\fR" 4
.IX Item "enable-angle-delta (boolean) (Requires libaom >= v2.0.0)"
Enable angle delta intra prediction. Default is true.
.IP "\fBenable-cfl-intra (\fR\fIboolean\fR\fB) (Requires libaom >= v2.0.0)\fR" 4
.IX Item "enable-cfl-intra (boolean) (Requires libaom >= v2.0.0)"
Enable chroma predicted from luma intra prediction. Default is true.
.IP "\fBenable-filter-intra (\fR\fIboolean\fR\fB) (Requires libaom >= v2.0.0)\fR" 4
.IX Item "enable-filter-intra (boolean) (Requires libaom >= v2.0.0)"
Enable filter intra predictor. Default is true.
.IP "\fBenable-intra-edge-filter (\fR\fIboolean\fR\fB) (Requires libaom >= v2.0.0)\fR" 4
.IX Item "enable-intra-edge-filter (boolean) (Requires libaom >= v2.0.0)"
Enable intra edge filter. Default is true.
.IP "\fBenable-smooth-intra (\fR\fIboolean\fR\fB) (Requires libaom >= v2.0.0)\fR" 4
.IX Item "enable-smooth-intra (boolean) (Requires libaom >= v2.0.0)"
Enable smooth intra prediction mode. Default is true.
.IP "\fBenable-paeth-intra (\fR\fIboolean\fR\fB) (Requires libaom >= v2.0.0)\fR" 4
.IX Item "enable-paeth-intra (boolean) (Requires libaom >= v2.0.0)"
Enable paeth predictor in intra prediction. Default is true.
.IP "\fBenable-palette (\fR\fIboolean\fR\fB) (Requires libaom >= v2.0.0)\fR" 4
.IX Item "enable-palette (boolean) (Requires libaom >= v2.0.0)"
Enable palette prediction mode. Default is true.
.IP "\fBenable-flip-idtx (\fR\fIboolean\fR\fB) (Requires libaom >= v2.0.0)\fR" 4
.IX Item "enable-flip-idtx (boolean) (Requires libaom >= v2.0.0)"
Enable extended transform type, including FLIPADST_DCT, DCT_FLIPADST,
FLIPADST_FLIPADST, ADST_FLIPADST, FLIPADST_ADST, IDTX, V_DCT, H_DCT,
V_ADST, H_ADST, V_FLIPADST, H_FLIPADST. Default is true.
.IP "\fBenable\-tx64 (\fR\fIboolean\fR\fB) (Requires libaom >= v2.0.0)\fR" 4
.IX Item "enable-tx64 (boolean) (Requires libaom >= v2.0.0)"
Enable 64\-pt transform. Default is true.
.IP "\fBreduced-tx-type-set (\fR\fIboolean\fR\fB) (Requires libaom >= v2.0.0)\fR" 4
.IX Item "reduced-tx-type-set (boolean) (Requires libaom >= v2.0.0)"
Use reduced set of transform types. Default is false.
.IP "\fBuse-intra-dct-only (\fR\fIboolean\fR\fB) (Requires libaom >= v2.0.0)\fR" 4
.IX Item "use-intra-dct-only (boolean) (Requires libaom >= v2.0.0)"
Use DCT only for INTRA modes. Default is false.
.IP "\fBuse-inter-dct-only (\fR\fIboolean\fR\fB) (Requires libaom >= v2.0.0)\fR" 4
.IX Item "use-inter-dct-only (boolean) (Requires libaom >= v2.0.0)"
Use DCT only for INTER modes. Default is false.
.IP "\fBuse-intra-default-tx-only (\fR\fIboolean\fR\fB) (Requires libaom >= v2.0.0)\fR" 4
.IX Item "use-intra-default-tx-only (boolean) (Requires libaom >= v2.0.0)"
Use Default-transform only for INTRA modes. Default is false.
.IP "\fBenable-ref-frame-mvs (\fR\fIboolean\fR\fB) (Requires libaom >= v2.0.0)\fR" 4
.IX Item "enable-ref-frame-mvs (boolean) (Requires libaom >= v2.0.0)"
Enable temporal mv prediction. Default is true.
.IP "\fBenable-reduced-reference-set (\fR\fIboolean\fR\fB) (Requires libaom >= v2.0.0)\fR" 4
.IX Item "enable-reduced-reference-set (boolean) (Requires libaom >= v2.0.0)"
Use reduced set of single and compound references. Default is false.
.IP "\fBenable-obmc (\fR\fIboolean\fR\fB) (Requires libaom >= v2.0.0)\fR" 4
.IX Item "enable-obmc (boolean) (Requires libaom >= v2.0.0)"
Enable obmc. Default is true.
.IP "\fBenable-dual-filter (\fR\fIboolean\fR\fB) (Requires libaom >= v2.0.0)\fR" 4
.IX Item "enable-dual-filter (boolean) (Requires libaom >= v2.0.0)"
Enable dual filter. Default is true.
.IP "\fBenable-diff-wtd-comp (\fR\fIboolean\fR\fB) (Requires libaom >= v2.0.0)\fR" 4
.IX Item "enable-diff-wtd-comp (boolean) (Requires libaom >= v2.0.0)"
Enable difference-weighted compound. Default is true.
.IP "\fBenable-dist-wtd-comp (\fR\fIboolean\fR\fB) (Requires libaom >= v2.0.0)\fR" 4
.IX Item "enable-dist-wtd-comp (boolean) (Requires libaom >= v2.0.0)"
Enable distance-weighted compound. Default is true.
.IP "\fBenable-onesided-comp (\fR\fIboolean\fR\fB) (Requires libaom >= v2.0.0)\fR" 4
.IX Item "enable-onesided-comp (boolean) (Requires libaom >= v2.0.0)"
Enable one sided compound. Default is true.
.IP "\fBenable-interinter-wedge (\fR\fIboolean\fR\fB) (Requires libaom >= v2.0.0)\fR" 4
.IX Item "enable-interinter-wedge (boolean) (Requires libaom >= v2.0.0)"
Enable interinter wedge compound. Default is true.
.IP "\fBenable-interintra-wedge (\fR\fIboolean\fR\fB) (Requires libaom >= v2.0.0)\fR" 4
.IX Item "enable-interintra-wedge (boolean) (Requires libaom >= v2.0.0)"
Enable interintra wedge compound. Default is true.
.IP "\fBenable-masked-comp (\fR\fIboolean\fR\fB) (Requires libaom >= v2.0.0)\fR" 4
.IX Item "enable-masked-comp (boolean) (Requires libaom >= v2.0.0)"
Enable masked compound. Default is true.
.IP "\fBenable-interintra-comp (\fR\fIboolean\fR\fB) (Requires libaom >= v2.0.0)\fR" 4
.IX Item "enable-interintra-comp (boolean) (Requires libaom >= v2.0.0)"
Enable interintra compound. Default is true.
.IP "\fBenable-smooth-interintra (\fR\fIboolean\fR\fB) (Requires libaom >= v2.0.0)\fR" 4
.IX Item "enable-smooth-interintra (boolean) (Requires libaom >= v2.0.0)"
Enable smooth interintra mode. Default is true.
.IP \fBaom-params\fR 4
.IX Item "aom-params"
Set libaom options using a list of \fIkey\fR=\fIvalue\fR pairs separated
by ":". For a list of supported options, see \fBaomenc \-\-help\fR under the
section "AV1 Specific Options".
.Sp
For example to specify libaom encoding options with \fB\-aom\-params\fR:
.Sp
.Vb 1
\&        ffmpeg \-i input \-c:v libaom\-av1 \-b:v 500K \-aom\-params tune=psnr:enable\-tpl\-model=1 output.mp4
.Ve
.SS liboapv
.IX Subsection "liboapv"
Advanced Professional Video codec encoder wrapper.
.PP
This encoder requires the presence of the liboapv headers and library
during configuration. You need to explicitly configure the build with
\&\fB\-\-enable\-liboapv\fR.
.Sp
.RS 4
Many liboapv encoder options are mapped to FFmpeg global codec options,
while unique encoder options are provided through private options.
.RE
.PP
The apv project website is at <\fBhttps://github.com/AcademySoftwareFoundation/openapv\fR>.
.PP
\fIOptions\fR
.IX Subsection "Options"
.PP
The following options are supported by the liboapv wrapper.
.Sp
.RS 4
To get a more extensive documentation of the liboapv options, consult the
liboapv documentation.
.RE
.IP \fBpreset\fR 4
.IX Item "preset"
Set the quality-speed tradeoff [fastest, fast, medium, slow, placebo, default]
.IP \fBqp\fR 4
.IX Item "qp"
Set the quantization parameter value for CQP rate control mode.
.IP "\fBoapv-params (\fR\fIparse_apv_params\fR\fB)\fR" 4
.IX Item "oapv-params (parse_apv_params)"
Set liboapvenc options using a list of \fIkey\fR=\fIvalue\fR pairs separated
by ":". See the liboapv encoder user guide for a list of accepted parameters.
.SS libsvtav1
.IX Subsection "libsvtav1"
SVT\-AV1 encoder wrapper.
.PP
Requires the presence of the SVT\-AV1 headers and library during configuration.
You need to explicitly configure the build with \f(CW\*(C`\-\-enable\-libsvtav1\*(C'\fR.
.PP
\fIOptions\fR
.IX Subsection "Options"
.IP \fBprofile\fR 4
.IX Item "profile"
Set the encoding profile.
.RS 4
.IP \fBmain\fR 4
.IX Item "main"
.PD 0
.IP \fBhigh\fR 4
.IX Item "high"
.IP \fBprofessional\fR 4
.IX Item "professional"
.RE
.RS 4
.RE
.IP \fBlevel\fR 4
.IX Item "level"
.PD
Set the operating point level. For example: '4.0'
.IP \fBhielevel\fR 4
.IX Item "hielevel"
Set the Hierarchical prediction levels.
.RS 4
.IP \fB3level\fR 4
.IX Item "3level"
.PD 0
.IP \fB4level\fR 4
.IX Item "4level"
.PD
This is the default.
.RE
.RS 4
.RE
.IP \fBtier\fR 4
.IX Item "tier"
Set the operating point tier.
.RS 4
.IP \fBmain\fR 4
.IX Item "main"
This is the default.
.IP \fBhigh\fR 4
.IX Item "high"
.RE
.RS 4
.RE
.PD 0
.IP \fBqmax\fR 4
.IX Item "qmax"
.PD
Set the maximum quantizer to use when using a bitrate mode.
.IP \fBqmin\fR 4
.IX Item "qmin"
Set the minimum quantizer to use when using a bitrate mode.
.IP \fBcrf\fR 4
.IX Item "crf"
Constant rate factor value used in crf rate control mode (0\-63).
.IP \fBqp\fR 4
.IX Item "qp"
Set the quantizer used in cqp rate control mode (0\-63).
.IP \fBsc_detection\fR 4
.IX Item "sc_detection"
Enable scene change detection.
.IP \fBla_depth\fR 4
.IX Item "la_depth"
Set number of frames to look ahead (0\-120).
.IP \fBpreset\fR 4
.IX Item "preset"
Set the quality-speed tradeoff, in the range 0 to 13.  Higher values are
faster but lower quality.
.IP \fBtile_rows\fR 4
.IX Item "tile_rows"
Set log2 of the number of rows of tiles to use (0\-6).
.IP \fBtile_columns\fR 4
.IX Item "tile_columns"
Set log2 of the number of columns of tiles to use (0\-4).
.IP \fBsvtav1\-params\fR 4
.IX Item "svtav1-params"
Set SVT\-AV1 options using a list of \fIkey\fR=\fIvalue\fR pairs separated
by ":". See the SVT\-AV1 encoder user guide for a list of accepted parameters.
.SS libjxl
.IX Subsection "libjxl"
libjxl JPEG XL encoder wrapper.
.PP
Requires the presence of the libjxl headers and library during
configuration. You need to explicitly configure the build with
\&\f(CW\*(C`\-\-enable\-libjxl\*(C'\fR.
.PP
\fIOptions\fR
.IX Subsection "Options"
.PP
The libjxl wrapper supports the following options:
.IP \fBdistance\fR 4
.IX Item "distance"
Set the target Butteraugli distance. This is a quality setting: lower
distance yields higher quality, with distance=1.0 roughly comparable to
libjpeg Quality 90 for photographic content. Setting distance=0.0 yields
true lossless encoding. Valid values range between 0.0 and 15.0, and sane
values rarely exceed 5.0. Setting distance=0.1 usually attains
transparency for most input. The default is 1.0.
.IP \fBeffort\fR 4
.IX Item "effort"
Set the encoding effort used. Higher effort values produce more consistent
quality and usually produces a better quality/bpp curve, at the cost of
more CPU time required. Valid values range from 1 to 9, and the default is 7.
.IP \fBmodular\fR 4
.IX Item "modular"
Force the encoder to use Modular mode instead of choosing automatically. The
default is to use VarDCT for lossy encoding and Modular for lossless. VarDCT
is generally superior to Modular for lossy encoding but does not support
lossless encoding.
.SS libkvazaar
.IX Subsection "libkvazaar"
Kvazaar H.265/HEVC encoder.
.PP
Requires the presence of the libkvazaar headers and library during
configuration. You need to explicitly configure the build with
\&\fB\-\-enable\-libkvazaar\fR.
.PP
\fIOptions\fR
.IX Subsection "Options"
.IP \fBb\fR 4
.IX Item "b"
Set target video bitrate in bit/s and enable rate control.
.IP \fBkvazaar-params\fR 4
.IX Item "kvazaar-params"
Set kvazaar parameters as a list of \fIname\fR=\fIvalue\fR pairs separated
by commas (,). See kvazaar documentation for a list of options.
.SS libopenh264
.IX Subsection "libopenh264"
Cisco libopenh264 H.264/MPEG\-4 AVC encoder wrapper.
.PP
This encoder requires the presence of the libopenh264 headers and
library during configuration. You need to explicitly configure the
build with \f(CW\*(C`\-\-enable\-libopenh264\*(C'\fR. The library is detected using
\&\fBpkg-config\fR.
.PP
For more information about the library see
<\fBhttp://www.openh264.org\fR>.
.PP
\fIOptions\fR
.IX Subsection "Options"
.PP
The following FFmpeg global options affect the configurations of the
libopenh264 encoder.
.IP \fBb\fR 4
.IX Item "b"
Set the bitrate (as a number of bits per second).
.IP \fBg\fR 4
.IX Item "g"
Set the GOP size.
.IP \fBmaxrate\fR 4
.IX Item "maxrate"
Set the max bitrate (as a number of bits per second).
.IP "\fBflags +global_header\fR" 4
.IX Item "flags +global_header"
Set global header in the bitstream.
.IP \fBslices\fR 4
.IX Item "slices"
Set the number of slices, used in parallelized encoding. Default value
is 0. This is only used when \fBslice_mode\fR is set to
\&\fBfixed\fR.
.IP \fBloopfilter\fR 4
.IX Item "loopfilter"
Enable loop filter, if set to 1 (automatically enabled). To disable
set a value of 0.
.IP \fBprofile\fR 4
.IX Item "profile"
Set profile restrictions. If set to the value of \fBmain\fR enable
CABAC (set the \f(CW\*(C`SEncParamExt.iEntropyCodingModeFlag\*(C'\fR flag to 1).
.IP \fBmax_nal_size\fR 4
.IX Item "max_nal_size"
Set maximum NAL size in bytes.
.IP \fBallow_skip_frames\fR 4
.IX Item "allow_skip_frames"
Allow skipping frames to hit the target bitrate if set to 1.
.SS libtheora
.IX Subsection "libtheora"
libtheora Theora encoder wrapper.
.PP
Requires the presence of the libtheora headers and library during
configuration. You need to explicitly configure the build with
\&\f(CW\*(C`\-\-enable\-libtheora\*(C'\fR.
.PP
For more information about the libtheora project see
<\fBhttp://www.theora.org/\fR>.
.PP
\fIOptions\fR
.IX Subsection "Options"
.PP
The following global options are mapped to internal libtheora options
which affect the quality and the bitrate of the encoded stream.
.IP \fBb\fR 4
.IX Item "b"
Set the video bitrate in bit/s for CBR (Constant Bit Rate) mode.  In
case VBR (Variable Bit Rate) mode is enabled this option is ignored.
.IP \fBflags\fR 4
.IX Item "flags"
Used to enable constant quality mode (VBR) encoding through the
\&\fBqscale\fR flag, and to enable the \f(CW\*(C`pass1\*(C'\fR and \f(CW\*(C`pass2\*(C'\fR
modes.
.IP \fBg\fR 4
.IX Item "g"
Set the GOP size.
.IP \fBglobal_quality\fR 4
.IX Item "global_quality"
Set the global quality as an integer in lambda units.
.Sp
Only relevant when VBR mode is enabled with \f(CW\*(C`flags +qscale\*(C'\fR. The
value is converted to QP units by dividing it by \f(CW\*(C`FF_QP2LAMBDA\*(C'\fR,
clipped in the [0 \- 10] range, and then multiplied by 6.3 to get a
value in the native libtheora range [0\-63]. A higher value corresponds
to a higher quality.
.IP \fBq\fR 4
.IX Item "q"
Enable VBR mode when set to a non-negative value, and set constant
quality value as a double floating point value in QP units.
.Sp
The value is clipped in the [0\-10] range, and then multiplied by 6.3
to get a value in the native libtheora range [0\-63].
.Sp
This option is valid only using the \fBffmpeg\fR command-line
tool. For library interface users, use \fBglobal_quality\fR.
.PP
\fIExamples\fR
.IX Subsection "Examples"
.IP \(bu 4
Set maximum constant quality (VBR) encoding with \fBffmpeg\fR:
.Sp
.Vb 1
\&        ffmpeg \-i INPUT \-codec:v libtheora \-q:v 10 OUTPUT.ogg
.Ve
.IP \(bu 4
Use \fBffmpeg\fR to convert a CBR 1000 kbps Theora video stream:
.Sp
.Vb 1
\&        ffmpeg \-i INPUT \-codec:v libtheora \-b:v 1000k OUTPUT.ogg
.Ve
.SS libvpx
.IX Subsection "libvpx"
VP8/VP9 format supported through libvpx.
.PP
Requires the presence of the libvpx headers and library during configuration.
You need to explicitly configure the build with \f(CW\*(C`\-\-enable\-libvpx\*(C'\fR.
.PP
\fIOptions\fR
.IX Subsection "Options"
.PP
The following options are supported by the libvpx wrapper. The
\&\fBvpxenc\fR\-equivalent options or values are listed in parentheses
for easy migration.
.PP
To reduce the duplication of documentation, only the private options
and some others requiring special attention are documented here. For
the documentation of the undocumented generic options, see
\&\fBthe Codec Options chapter\fR.
.PP
To get more documentation of the libvpx options, invoke the command
\&\fBffmpeg \-h encoder=libvpx\fR, \fBffmpeg \-h encoder=libvpx\-vp9\fR or
\&\fBvpxenc \-\-help\fR. Further information is available in the libvpx API
documentation.
.IP "\fBb (\fR\fItarget-bitrate\fR\fB)\fR" 4
.IX Item "b (target-bitrate)"
Set bitrate in bits/s. Note that FFmpeg's \fBb\fR option is
expressed in bits/s, while \fBvpxenc\fR's \fBtarget-bitrate\fR is in
kilobits/s.
.IP "\fBg (\fR\fIkf-max-dist\fR\fB)\fR" 4
.IX Item "g (kf-max-dist)"
.PD 0
.IP "\fBkeyint_min (\fR\fIkf-min-dist\fR\fB)\fR" 4
.IX Item "keyint_min (kf-min-dist)"
.IP "\fBqmin (\fR\fImin-q\fR\fB)\fR" 4
.IX Item "qmin (min-q)"
.PD
Minimum (Best Quality) Quantizer.
.IP "\fBqmax (\fR\fImax-q\fR\fB)\fR" 4
.IX Item "qmax (max-q)"
Maximum (Worst Quality) Quantizer.
Can be changed per-frame.
.IP "\fBbufsize (\fR\fIbuf-sz\fR\fB,\fR \fIbuf-optimal-sz\fR\fB)\fR" 4
.IX Item "bufsize (buf-sz, buf-optimal-sz)"
Set ratecontrol buffer size (in bits). Note \fBvpxenc\fR's options are
specified in milliseconds, the libvpx wrapper converts this value as follows:
\&\f(CW\*(C`buf\-sz = bufsize * 1000 / bitrate\*(C'\fR,
\&\f(CW\*(C`buf\-optimal\-sz = bufsize * 1000 / bitrate * 5 / 6\*(C'\fR.
.IP "\fBrc_init_occupancy (\fR\fIbuf-initial-sz\fR\fB)\fR" 4
.IX Item "rc_init_occupancy (buf-initial-sz)"
Set number of bits which should be loaded into the rc buffer before decoding
starts. Note \fBvpxenc\fR's option is specified in milliseconds, the libvpx
wrapper converts this value as follows:
\&\f(CW\*(C`rc_init_occupancy * 1000 / bitrate\*(C'\fR.
.IP \fBundershoot-pct\fR 4
.IX Item "undershoot-pct"
Set datarate undershoot (min) percentage of the target bitrate.
.IP \fBovershoot-pct\fR 4
.IX Item "overshoot-pct"
Set datarate overshoot (max) percentage of the target bitrate.
.IP "\fBskip_threshold (\fR\fIdrop-frame\fR\fB)\fR" 4
.IX Item "skip_threshold (drop-frame)"
.PD 0
.IP "\fBqcomp (\fR\fIbias-pct\fR\fB)\fR" 4
.IX Item "qcomp (bias-pct)"
.IP "\fBmaxrate (\fR\fImaxsection-pct\fR\fB)\fR" 4
.IX Item "maxrate (maxsection-pct)"
.PD
Set GOP max bitrate in bits/s. Note \fBvpxenc\fR's option is specified as a
percentage of the target bitrate, the libvpx wrapper converts this value as
follows: \f(CW\*(C`(maxrate * 100 / bitrate)\*(C'\fR.
.IP "\fBminrate (\fR\fIminsection-pct\fR\fB)\fR" 4
.IX Item "minrate (minsection-pct)"
Set GOP min bitrate in bits/s. Note \fBvpxenc\fR's option is specified as a
percentage of the target bitrate, the libvpx wrapper converts this value as
follows: \f(CW\*(C`(minrate * 100 / bitrate)\*(C'\fR.
.IP "\fBminrate, maxrate, b\fR \fIend\-usage=cbr\fR" 4
.IX Item "minrate, maxrate, b end-usage=cbr"
\&\f(CW\*(C`(minrate == maxrate == bitrate)\*(C'\fR.
.IP "\fBcrf (\fR\fIend\-usage=cq\fR\fB,\fR \fIcq-level\fR\fB)\fR" 4
.IX Item "crf (end-usage=cq, cq-level)"
.PD 0
.IP "\fBtune (\fR\fItune\fR\fB)\fR" 4
.IX Item "tune (tune)"
.RS 4
.IP "\fBpsnr (\fR\fIpsnr\fR\fB)\fR" 4
.IX Item "psnr (psnr)"
.IP "\fBssim (\fR\fIssim\fR\fB)\fR" 4
.IX Item "ssim (ssim)"
.RE
.RS 4
.RE
.IP "\fBquality, deadline (\fR\fIdeadline\fR\fB)\fR" 4
.IX Item "quality, deadline (deadline)"
.RS 4
.IP \fBbest\fR 4
.IX Item "best"
.PD
Use best quality deadline. Poorly named and quite slow, this option should be
avoided as it may give worse quality output than good.
.IP \fBgood\fR 4
.IX Item "good"
Use good quality deadline. This is a good trade-off between speed and quality
when used with the \fBcpu-used\fR option.
.IP \fBrealtime\fR 4
.IX Item "realtime"
Use realtime quality deadline.
.RE
.RS 4
.RE
.IP "\fBspeed, cpu-used (\fR\fIcpu-used\fR\fB)\fR" 4
.IX Item "speed, cpu-used (cpu-used)"
Set quality/speed ratio modifier. Higher values speed up the encode at the cost
of quality.
.IP "\fBnr (\fR\fInoise-sensitivity\fR\fB)\fR" 4
.IX Item "nr (noise-sensitivity)"
.PD 0
.IP \fBstatic-thresh\fR 4
.IX Item "static-thresh"
.PD
Set a change threshold on blocks below which they will be skipped by the
encoder.
.IP "\fBslices (\fR\fItoken-parts\fR\fB)\fR" 4
.IX Item "slices (token-parts)"
Note that FFmpeg's \fBslices\fR option gives the total number of partitions,
while \fBvpxenc\fR's \fBtoken-parts\fR is given as
\&\f(CWlog2(partitions)\fR.
.IP \fBmax-intra-rate\fR 4
.IX Item "max-intra-rate"
Set maximum I\-frame bitrate as a percentage of the target bitrate. A value of 0
means unlimited.
.IP \fBforce_key_frames\fR 4
.IX Item "force_key_frames"
\&\f(CW\*(C`VPX_EFLAG_FORCE_KF\*(C'\fR
.IP "\fBAlternate reference frame related\fR" 4
.IX Item "Alternate reference frame related"
.RS 4
.PD 0
.IP \fBauto-alt-ref\fR 4
.IX Item "auto-alt-ref"
.PD
Enable use of alternate reference frames (2\-pass only).
Values greater than 1 enable multi-layer alternate reference frames (VP9 only).
.IP \fBarnr-maxframes\fR 4
.IX Item "arnr-maxframes"
Set altref noise reduction max frame count.
.IP \fBarnr-type\fR 4
.IX Item "arnr-type"
Set altref noise reduction filter type: backward, forward, centered.
.IP \fBarnr-strength\fR 4
.IX Item "arnr-strength"
Set altref noise reduction filter strength.
.IP "\fBrc-lookahead, lag-in-frames (\fR\fIlag-in-frames\fR\fB)\fR" 4
.IX Item "rc-lookahead, lag-in-frames (lag-in-frames)"
Set number of frames to look ahead for frametype and ratecontrol.
.IP \fBmin-gf-interval\fR 4
.IX Item "min-gf-interval"
Set minimum golden/alternate reference frame interval (VP9 only).
.RE
.RS 4
.RE
.IP \fBerror-resilient\fR 4
.IX Item "error-resilient"
Enable error resiliency features.
.IP "\fBsharpness\fR \fIinteger\fR" 4
.IX Item "sharpness integer"
Increase sharpness at the expense of lower PSNR.
The valid range is [0, 7].
.IP \fBts-parameters\fR 4
.IX Item "ts-parameters"
Sets the temporal scalability configuration using a :\-separated list of
key=value pairs. For example, to specify temporal scalability parameters
with \f(CW\*(C`ffmpeg\*(C'\fR:
.Sp
.Vb 3
\&        ffmpeg \-i INPUT \-c:v libvpx \-ts\-parameters ts_number_layers=3:\e
\&        ts_target_bitrate=250,500,1000:ts_rate_decimator=4,2,1:\e
\&        ts_periodicity=4:ts_layer_id=0,2,1,2:ts_layering_mode=3 OUTPUT
.Ve
.Sp
Below is a brief explanation of each of the parameters, please
refer to \f(CW\*(C`struct vpx_codec_enc_cfg\*(C'\fR in \f(CW\*(C`vpx/vpx_encoder.h\*(C'\fR for more
details.
.RS 4
.IP \fBts_number_layers\fR 4
.IX Item "ts_number_layers"
Number of temporal coding layers.
.IP \fBts_target_bitrate\fR 4
.IX Item "ts_target_bitrate"
Target bitrate for each temporal layer (in kbps).
(bitrate should be inclusive of the lower temporal layer).
.IP \fBts_rate_decimator\fR 4
.IX Item "ts_rate_decimator"
Frame rate decimation factor for each temporal layer.
.IP \fBts_periodicity\fR 4
.IX Item "ts_periodicity"
Length of the sequence defining frame temporal layer membership.
.IP \fBts_layer_id\fR 4
.IX Item "ts_layer_id"
Template defining the membership of frames to temporal layers.
.IP \fBts_layering_mode\fR 4
.IX Item "ts_layering_mode"
(optional) Selecting the temporal structure from a set of pre-defined temporal layering modes.
Currently supports the following options.
.RS 4
.IP \fB0\fR 4
.IX Item "0"
No temporal layering flags are provided internally,
relies on flags being passed in using \f(CW\*(C`metadata\*(C'\fR field in \f(CW\*(C`AVFrame\*(C'\fR
with following keys.
.RS 4
.IP \fBvp8\-flags\fR 4
.IX Item "vp8-flags"
Sets the flags passed into the encoder to indicate the referencing scheme for
the current frame.
Refer to function \f(CW\*(C`vpx_codec_encode\*(C'\fR in \f(CW\*(C`vpx/vpx_encoder.h\*(C'\fR for more
details.
.IP \fBtemporal_id\fR 4
.IX Item "temporal_id"
Explicitly sets the temporal id of the current frame to encode.
.RE
.RS 4
.RE
.IP \fB2\fR 4
.IX Item "2"
Two temporal layers. 0\-1...
.IP \fB3\fR 4
.IX Item "3"
Three temporal layers. 0\-2\-1\-2...; with single reference frame.
.IP \fB4\fR 4
.IX Item "4"
Same as option "3", except there is a dependency between
the two temporal layer 2 frames within the temporal period.
.RE
.RS 4
.RE
.RE
.RS 4
.RE
.IP "\fBVP8\-specific options\fR" 4
.IX Item "VP8-specific options"
.RS 4
.PD 0
.IP \fBscreen-content-mode\fR 4
.IX Item "screen-content-mode"
.PD
Screen content mode, one of: 0 (off), 1 (screen), 2 (screen with more aggressive rate control).
.RE
.RS 4
.RE
.IP "\fBVP9\-specific options\fR" 4
.IX Item "VP9-specific options"
.RS 4
.PD 0
.IP \fBlossless\fR 4
.IX Item "lossless"
.PD
Enable lossless mode.
.IP \fBtile-columns\fR 4
.IX Item "tile-columns"
Set number of tile columns to use. Note this is given as
\&\f(CWlog2(tile_columns)\fR. For example, 8 tile columns would be requested by
setting the \fBtile-columns\fR option to 3.
.IP \fBtile-rows\fR 4
.IX Item "tile-rows"
Set number of tile rows to use. Note this is given as \f(CWlog2(tile_rows)\fR.
For example, 4 tile rows would be requested by setting the \fBtile-rows\fR
option to 2.
.IP \fBframe-parallel\fR 4
.IX Item "frame-parallel"
Enable frame parallel decodability features.
.IP \fBaq-mode\fR 4
.IX Item "aq-mode"
Set adaptive quantization mode (0: off (default), 1: variance 2: complexity, 3:
cyclic refresh, 4: equator360).
.IP "\fBcolorspace\fR \fIcolor-space\fR" 4
.IX Item "colorspace color-space"
Set input color space. The VP9 bitstream supports signaling the following
colorspaces:
.RS 4
.IP "\fBrgb \fR\f(BIsRGB\fR" 4
.IX Item "rgb sRGB"
.PD 0
.IP "\fBbt709 \fR\f(BIbt709\fR" 4
.IX Item "bt709 bt709"
.IP "\fBunspecified \fR\f(BIunknown\fR" 4
.IX Item "unspecified unknown"
.IP "\fBbt470bg \fR\f(BIbt601\fR" 4
.IX Item "bt470bg bt601"
.IP "\fBsmpte170m \fR\f(BIsmpte170\fR" 4
.IX Item "smpte170m smpte170"
.IP "\fBsmpte240m \fR\f(BIsmpte240\fR" 4
.IX Item "smpte240m smpte240"
.IP "\fBbt2020_ncl \fR\f(BIbt2020\fR" 4
.IX Item "bt2020_ncl bt2020"
.RE
.RS 4
.RE
.IP "\fBrow-mt\fR \fIboolean\fR" 4
.IX Item "row-mt boolean"
.PD
Enable row based multi-threading.
.IP \fBtune-content\fR 4
.IX Item "tune-content"
Set content type: default (0), screen (1), film (2).
.IP \fBcorpus-complexity\fR 4
.IX Item "corpus-complexity"
Corpus VBR mode is a variant of standard VBR where the complexity distribution
midpoint is passed in rather than calculated for a specific clip or chunk.
.Sp
The valid range is [0, 10000]. 0 (default) uses standard VBR.
.IP "\fBenable-tpl\fR \fIboolean\fR" 4
.IX Item "enable-tpl boolean"
Enable temporal dependency model.
.IP \fBref-frame-config\fR 4
.IX Item "ref-frame-config"
Using per-frame metadata, set members of the structure \f(CW\*(C`vpx_svc_ref_frame_config_t\*(C'\fR in \f(CW\*(C`vpx/vp8cx.h\*(C'\fR to fine-control referencing schemes and frame buffer management.
Use a :\-separated list of key=value pairs.
For example,
.Sp
.Vb 2
\&        av_dict_set(&av_frame\->metadata, "ref\-frame\-config", \e
\&        "rfc_update_buffer_slot=7:rfc_lst_fb_idx=0:rfc_gld_fb_idx=1:rfc_alt_fb_idx=2:rfc_reference_last=0:rfc_reference_golden=0:rfc_reference_alt_ref=0");
.Ve
.RS 4
.IP \fBrfc_update_buffer_slot\fR 4
.IX Item "rfc_update_buffer_slot"
Indicates the buffer slot number to update
.IP \fBrfc_update_last\fR 4
.IX Item "rfc_update_last"
Indicates whether to update the LAST frame
.IP \fBrfc_update_golden\fR 4
.IX Item "rfc_update_golden"
Indicates whether to update GOLDEN frame
.IP \fBrfc_update_alt_ref\fR 4
.IX Item "rfc_update_alt_ref"
Indicates whether to update ALT_REF frame
.IP \fBrfc_lst_fb_idx\fR 4
.IX Item "rfc_lst_fb_idx"
LAST frame buffer index
.IP \fBrfc_gld_fb_idx\fR 4
.IX Item "rfc_gld_fb_idx"
GOLDEN frame buffer index
.IP \fBrfc_alt_fb_idx\fR 4
.IX Item "rfc_alt_fb_idx"
ALT_REF frame buffer index
.IP \fBrfc_reference_last\fR 4
.IX Item "rfc_reference_last"
Indicates whether to reference LAST frame
.IP \fBrfc_reference_golden\fR 4
.IX Item "rfc_reference_golden"
Indicates whether to reference GOLDEN frame
.IP \fBrfc_reference_alt_ref\fR 4
.IX Item "rfc_reference_alt_ref"
Indicates whether to reference ALT_REF frame
.IP \fBrfc_reference_duration\fR 4
.IX Item "rfc_reference_duration"
Indicates frame duration
.RE
.RS 4
.RE
.RE
.RS 4
.RE
.PP
For more information about libvpx see:
<\fBhttp://www.webmproject.org/\fR>
.SS libvvenc
.IX Subsection "libvvenc"
VVenC H.266/VVC encoder wrapper.
.PP
This encoder requires the presence of the libvvenc headers and library
during configuration. You need to explicitly configure the build with
\&\fB\-\-enable\-libvvenc\fR.
.PP
The VVenC project website is at
<\fBhttps://github.com/fraunhoferhhi/vvenc\fR>.
.PP
\fISupported Pixel Formats\fR
.IX Subsection "Supported Pixel Formats"
.PP
VVenC supports only 10\-bit color spaces as input. But the internal (encoded)
bit depth can be set to 8\-bit or 10\-bit at runtime.
.PP
\fIOptions\fR
.IX Subsection "Options"
.IP \fBb\fR 4
.IX Item "b"
Sets target video bitrate.
.IP \fBg\fR 4
.IX Item "g"
Set the GOP size. Currently support for g=1 (Intra only) or default.
.IP \fBpreset\fR 4
.IX Item "preset"
Set the VVenC preset.
.IP \fBlevelidc\fR 4
.IX Item "levelidc"
Set level idc.
.IP \fBtier\fR 4
.IX Item "tier"
Set vvc tier.
.IP \fBqp\fR 4
.IX Item "qp"
Set constant quantization parameter.
.IP "\fBsubopt\fR \fIboolean\fR" 4
.IX Item "subopt boolean"
Set subjective (perceptually motivated) optimization. Default is 1 (on).
.IP "\fBbitdepth8\fR \fIboolean\fR" 4
.IX Item "bitdepth8 boolean"
Set 8bit coding mode instead of using 10bit. Default is 0 (off).
.IP \fBperiod\fR 4
.IX Item "period"
set (intra) refresh period in seconds.
.IP \fBvvenc-params\fR 4
.IX Item "vvenc-params"
Set vvenc options using a list of \fIkey\fR=\fIvalue\fR couples separated
by ":". See \fBvvencapp \-\-fullhelp\fR or \fBvvencFFapp \-\-fullhelp\fR for a list of options.
.Sp
For example, the options might be provided as:
.Sp
.Vb 1
\&        intraperiod=64:decodingrefreshtype=idr:poc0idr=1:internalbitdepth=8
.Ve
.Sp
For example the encoding options might be provided with \fB\-vvenc\-params\fR:
.Sp
.Vb 1
\&        ffmpeg \-i input \-c:v libvvenc \-b 1M \-vvenc\-params intraperiod=64:decodingrefreshtype=idr:poc0idr=1:internalbitdepth=8 output.mp4
.Ve
.SS libwebp
.IX Subsection "libwebp"
libwebp WebP Image encoder wrapper
.PP
libwebp is Google's official encoder for WebP images. It can encode in either
lossy or lossless mode. Lossy images are essentially a wrapper around a VP8
frame. Lossless images are a separate codec developed by Google.
.PP
\fIPixel Format\fR
.IX Subsection "Pixel Format"
.PP
Currently, libwebp only supports YUV420 for lossy and RGB for lossless due
to limitations of the format and libwebp. Alpha is supported for either mode.
Because of API limitations, if RGB is passed in when encoding lossy or YUV is
passed in for encoding lossless, the pixel format will automatically be
converted using functions from libwebp. This is not ideal and is done only for
convenience.
.PP
\fIOptions\fR
.IX Subsection "Options"
.IP "\fB\-lossless\fR \fIboolean\fR" 4
.IX Item "-lossless boolean"
Enables/Disables use of lossless mode. Default is 0.
.IP "\fB\-compression_level\fR \fIinteger\fR" 4
.IX Item "-compression_level integer"
For lossy, this is a quality/speed tradeoff. Higher values give better quality
for a given size at the cost of increased encoding time. For lossless, this is
a size/speed tradeoff. Higher values give smaller size at the cost of increased
encoding time. More specifically, it controls the number of extra algorithms
and compression tools used, and varies the combination of these tools. This
maps to the \fImethod\fR option in libwebp. The valid range is 0 to 6.
Default is 4.
.IP "\fB\-quality\fR \fIfloat\fR" 4
.IX Item "-quality float"
For lossy encoding, this controls image quality. For lossless encoding, this
controls the effort and time spent in compression.
Range is 0 to 100. Default is 75.
.IP "\fB\-preset\fR \fItype\fR" 4
.IX Item "-preset type"
Configuration preset. This does some automatic settings based on the general
type of the image.
.RS 4
.IP \fBnone\fR 4
.IX Item "none"
Do not use a preset.
.IP \fBdefault\fR 4
.IX Item "default"
Use the encoder default.
.IP \fBpicture\fR 4
.IX Item "picture"
Digital picture, like portrait, inner shot
.IP \fBphoto\fR 4
.IX Item "photo"
Outdoor photograph, with natural lighting
.IP \fBdrawing\fR 4
.IX Item "drawing"
Hand or line drawing, with high-contrast details
.IP \fBicon\fR 4
.IX Item "icon"
Small-sized colorful images
.IP \fBtext\fR 4
.IX Item "text"
Text-like
.RE
.RS 4
.RE
.SS "libx264, libx264rgb"
.IX Subsection "libx264, libx264rgb"
x264 H.264/MPEG\-4 AVC encoder wrapper.
.PP
This encoder requires the presence of the libx264 headers and library
during configuration. You need to explicitly configure the build with
\&\f(CW\*(C`\-\-enable\-libx264\*(C'\fR.
.PP
libx264 supports an impressive number of features, including 8x8 and
4x4 adaptive spatial transform, adaptive B\-frame placement, CAVLC/CABAC
entropy coding, interlacing (MBAFF), lossless mode, psy optimizations
for detail retention (adaptive quantization, psy-RD, psy-trellis).
.PP
Many libx264 encoder options are mapped to FFmpeg global codec
options, while unique encoder options are provided through private
options. Additionally the \fBx264opts\fR and \fBx264\-params\fR
private options allows one to pass a list of key=value tuples as accepted
by the libx264 \f(CW\*(C`x264_param_parse\*(C'\fR function.
.PP
The x264 project website is at
<\fBhttp://www.videolan.org/developers/x264.html\fR>.
.PP
The libx264rgb encoder is the same as libx264, except it accepts packed RGB
pixel formats as input instead of YUV.
.PP
\fISupported Pixel Formats\fR
.IX Subsection "Supported Pixel Formats"
.PP
x264 supports 8\- to 10\-bit color spaces. The exact bit depth is controlled at
x264's configure time.
.PP
\fIOptions\fR
.IX Subsection "Options"
.PP
The following options are supported by the libx264 wrapper. The
\&\fBx264\fR\-equivalent options or values are listed in parentheses
for easy migration.
.PP
To reduce the duplication of documentation, only the private options
and some others requiring special attention are documented here. For
the documentation of the undocumented generic options, see
\&\fBthe Codec Options chapter\fR.
.PP
To get a more accurate and extensive documentation of the libx264
options, invoke the command \fBx264 \-\-fullhelp\fR or consult
the libx264 documentation.
.PP
In the list below, note that the \fBx264\fR option name is shown
in parentheses after the libavcodec corresponding name, in case there
is a direct mapping.
.IP "\fBb (\fR\fIbitrate\fR\fB)\fR" 4
.IX Item "b (bitrate)"
Set bitrate in bits/s. Note that FFmpeg's \fBb\fR option is
expressed in bits/s, while \fBx264\fR's \fBbitrate\fR is in
kilobits/s.
.IP "\fBbf (\fR\fIbframes\fR\fB)\fR" 4
.IX Item "bf (bframes)"
Number of B\-frames between I and P\-frames
.IP "\fBg (\fR\fIkeyint\fR\fB)\fR" 4
.IX Item "g (keyint)"
Maximum GOP size
.IP "\fBqmin (\fR\fIqpmin\fR\fB)\fR" 4
.IX Item "qmin (qpmin)"
Minimum quantizer scale
.IP "\fBqmax (\fR\fIqpmax\fR\fB)\fR" 4
.IX Item "qmax (qpmax)"
Maximum quantizer scale
.IP "\fBqdiff (\fR\fIqpstep\fR\fB)\fR" 4
.IX Item "qdiff (qpstep)"
Maximum difference between quantizer scales
.IP "\fBqblur (\fR\fIqblur\fR\fB)\fR" 4
.IX Item "qblur (qblur)"
Quantizer curve blur
.IP "\fBqcomp (\fR\fIqcomp\fR\fB)\fR" 4
.IX Item "qcomp (qcomp)"
Quantizer curve compression factor
.IP "\fBrefs (\fR\fIref\fR\fB)\fR" 4
.IX Item "refs (ref)"
Number of reference frames each P\-frame can use. The range is \fI0\-16\fR.
.IP "\fBlevel (\fR\fIlevel\fR\fB)\fR" 4
.IX Item "level (level)"
Set the \f(CW\*(C`x264_param_t.i_level_idc\*(C'\fR value in case the value is
positive, it is ignored otherwise.
.Sp
This value can be set using the \f(CW\*(C`AVCodecContext\*(C'\fR API (e.g. by
setting the \f(CW\*(C`AVCodecContext\*(C'\fR value directly), and is specified as
an integer mapped on a corresponding level (e.g. the value 31 maps
to H.264 level IDC "3.1", as defined in the \f(CW\*(C`x264_levels\*(C'\fR
table). It is ignored when set to a non positive value.
.Sp
Alternatively it can be set as a private option, overriding the value
set in \f(CW\*(C`AVCodecContext\*(C'\fR, and in this case must be specified as
the level IDC identifier (e.g. "3.1"), as defined by H.264 Annex A.
.IP "\fBsc_threshold (\fR\fIscenecut\fR\fB)\fR" 4
.IX Item "sc_threshold (scenecut)"
Sets the threshold for the scene change detection.
.IP "\fBtrellis (\fR\fItrellis\fR\fB)\fR" 4
.IX Item "trellis (trellis)"
Performs Trellis quantization to increase efficiency. Enabled by default.
.IP "\fBnr (\fR\fInr\fR\fB)\fR" 4
.IX Item "nr (nr)"
Noise reduction
.IP "\fBme_range (\fR\fImerange\fR\fB)\fR" 4
.IX Item "me_range (merange)"
Maximum range of the motion search in pixels.
.IP "\fBme_method (\fR\fIme\fR\fB)\fR" 4
.IX Item "me_method (me)"
Set motion estimation method. Possible values in the decreasing order
of speed:
.RS 4
.IP "\fBdia (\fR\fIdia\fR\fB)\fR" 4
.IX Item "dia (dia)"
.PD 0
.IP "\fBepzs (\fR\fIdia\fR\fB)\fR" 4
.IX Item "epzs (dia)"
.PD
Diamond search with radius 1 (fastest). \fBepzs\fR is an alias for
\&\fBdia\fR.
.IP "\fBhex (\fR\fIhex\fR\fB)\fR" 4
.IX Item "hex (hex)"
Hexagonal search with radius 2.
.IP "\fBumh (\fR\fIumh\fR\fB)\fR" 4
.IX Item "umh (umh)"
Uneven multi-hexagon search.
.IP "\fBesa (\fR\fIesa\fR\fB)\fR" 4
.IX Item "esa (esa)"
Exhaustive search.
.IP "\fBtesa (\fR\fItesa\fR\fB)\fR" 4
.IX Item "tesa (tesa)"
Hadamard exhaustive search (slowest).
.RE
.RS 4
.RE
.IP \fBforced-idr\fR 4
.IX Item "forced-idr"
Normally, when forcing a I\-frame type, the encoder can select any type
of I\-frame. This option forces it to choose an IDR-frame.
.IP "\fBsubq (\fR\fIsubme\fR\fB)\fR" 4
.IX Item "subq (subme)"
Sub-pixel motion estimation method.
.IP "\fBb_strategy (\fR\fIb\-adapt\fR\fB)\fR" 4
.IX Item "b_strategy (b-adapt)"
Adaptive B\-frame placement decision algorithm. Use only on first-pass.
.IP "\fBkeyint_min (\fR\fImin-keyint\fR\fB)\fR" 4
.IX Item "keyint_min (min-keyint)"
Minimum GOP size.
.IP \fBcoder\fR 4
.IX Item "coder"
Set entropy encoder. Possible values:
.RS 4
.IP \fBac\fR 4
.IX Item "ac"
Enable CABAC.
.IP \fBvlc\fR 4
.IX Item "vlc"
Enable CAVLC and disable CABAC. It generates the same effect as
\&\fBx264\fR's \fB\-\-no\-cabac\fR option.
.RE
.RS 4
.RE
.IP \fBcmp\fR 4
.IX Item "cmp"
Set full pixel motion estimation comparison algorithm. Possible values:
.RS 4
.IP \fBchroma\fR 4
.IX Item "chroma"
Enable chroma in motion estimation.
.IP \fBsad\fR 4
.IX Item "sad"
Ignore chroma in motion estimation. It generates the same effect as
\&\fBx264\fR's \fB\-\-no\-chroma\-me\fR option.
.RE
.RS 4
.RE
.IP "\fBthreads (\fR\fIthreads\fR\fB)\fR" 4
.IX Item "threads (threads)"
Number of encoding threads.
.IP \fBthread_type\fR 4
.IX Item "thread_type"
Set multithreading technique. Possible values:
.RS 4
.IP \fBslice\fR 4
.IX Item "slice"
Slice-based multithreading. It generates the same effect as
\&\fBx264\fR's \fB\-\-sliced\-threads\fR option.
.IP \fBframe\fR 4
.IX Item "frame"
Frame-based multithreading.
.RE
.RS 4
.RE
.IP \fBflags\fR 4
.IX Item "flags"
Set encoding flags. It can be used to disable closed GOP and enable
open GOP by setting it to \f(CW\*(C`\-cgop\*(C'\fR. The result is similar to
the behavior of \fBx264\fR's \fB\-\-open\-gop\fR option.
.IP "\fBrc_init_occupancy (\fR\fIvbv-init\fR\fB)\fR" 4
.IX Item "rc_init_occupancy (vbv-init)"
Initial VBV buffer occupancy
.IP "\fBpreset (\fR\fIpreset\fR\fB)\fR" 4
.IX Item "preset (preset)"
Set the encoding preset.
.IP "\fBtune (\fR\fItune\fR\fB)\fR" 4
.IX Item "tune (tune)"
Set tuning of the encoding params.
.IP "\fBprofile (\fR\fIprofile\fR\fB)\fR" 4
.IX Item "profile (profile)"
Set profile restrictions.
.IP \fBfastfirstpass\fR 4
.IX Item "fastfirstpass"
Enable fast settings when encoding first pass, when set to 1. When set
to 0, it has the same effect of \fBx264\fR's
\&\fB\-\-slow\-firstpass\fR option.
.IP "\fBcrf (\fR\fIcrf\fR\fB)\fR" 4
.IX Item "crf (crf)"
Set the quality for constant quality mode.
.IP "\fBcrf_max (\fR\fIcrf-max\fR\fB)\fR" 4
.IX Item "crf_max (crf-max)"
In CRF mode, prevents VBV from lowering quality beyond this point.
.IP "\fBqp (\fR\fIqp\fR\fB)\fR" 4
.IX Item "qp (qp)"
Set constant quantization rate control method parameter.
.IP "\fBaq-mode (\fR\fIaq-mode\fR\fB)\fR" 4
.IX Item "aq-mode (aq-mode)"
Set AQ method. Possible values:
.RS 4
.IP "\fBnone (\fR\fI0\fR\fB)\fR" 4
.IX Item "none (0)"
Disabled.
.IP "\fBvariance (\fR\fI1\fR\fB)\fR" 4
.IX Item "variance (1)"
Variance AQ (complexity mask).
.IP "\fBautovariance (\fR\fI2\fR\fB)\fR" 4
.IX Item "autovariance (2)"
Auto-variance AQ (experimental).
.RE
.RS 4
.RE
.IP "\fBaq-strength (\fR\fIaq-strength\fR\fB)\fR" 4
.IX Item "aq-strength (aq-strength)"
Set AQ strength, reduce blocking and blurring in flat and textured areas.
.IP \fBpsy\fR 4
.IX Item "psy"
Use psychovisual optimizations when set to 1. When set to 0, it has the
same effect as \fBx264\fR's \fB\-\-no\-psy\fR option.
.IP "\fBpsy-rd (\fR\fIpsy-rd\fR\fB)\fR" 4
.IX Item "psy-rd (psy-rd)"
Set strength of psychovisual optimization, in
\&\fIpsy-rd\fR:\fIpsy-trellis\fR format.
.IP "\fBrc-lookahead (\fR\fIrc-lookahead\fR\fB)\fR" 4
.IX Item "rc-lookahead (rc-lookahead)"
Set number of frames to look ahead for frametype and ratecontrol.
.IP \fBweightb\fR 4
.IX Item "weightb"
Enable weighted prediction for B\-frames when set to 1. When set to 0,
it has the same effect as \fBx264\fR's \fB\-\-no\-weightb\fR option.
.IP "\fBweightp (\fR\fIweightp\fR\fB)\fR" 4
.IX Item "weightp (weightp)"
Set weighted prediction method for P\-frames. Possible values:
.RS 4
.IP "\fBnone (\fR\fI0\fR\fB)\fR" 4
.IX Item "none (0)"
Disabled
.IP "\fBsimple (\fR\fI1\fR\fB)\fR" 4
.IX Item "simple (1)"
Enable only weighted refs
.IP "\fBsmart (\fR\fI2\fR\fB)\fR" 4
.IX Item "smart (2)"
Enable both weighted refs and duplicates
.RE
.RS 4
.RE
.IP "\fBssim (\fR\fIssim\fR\fB)\fR" 4
.IX Item "ssim (ssim)"
Enable calculation and printing SSIM stats after the encoding.
.IP "\fBintra-refresh (\fR\fIintra-refresh\fR\fB)\fR" 4
.IX Item "intra-refresh (intra-refresh)"
Enable the use of Periodic Intra Refresh instead of IDR frames when set
to 1.
.IP "\fBavcintra-class (\fR\fIclass\fR\fB)\fR" 4
.IX Item "avcintra-class (class)"
Configure the encoder to generate AVC-Intra.
Valid values are 50, 100 and 200
.IP "\fBbluray-compat (\fR\fIbluray-compat\fR\fB)\fR" 4
.IX Item "bluray-compat (bluray-compat)"
Configure the encoder to be compatible with the bluray standard.
It is a shorthand for setting "bluray\-compat=1 force\-cfr=1".
.IP "\fBb\-bias (\fR\fIb\-bias\fR\fB)\fR" 4
.IX Item "b-bias (b-bias)"
Set the influence on how often B\-frames are used.
.IP "\fBb\-pyramid (\fR\fIb\-pyramid\fR\fB)\fR" 4
.IX Item "b-pyramid (b-pyramid)"
Set method for keeping of some B\-frames as references. Possible values:
.RS 4
.IP "\fBnone (\fR\fInone\fR\fB)\fR" 4
.IX Item "none (none)"
Disabled.
.IP "\fBstrict (\fR\fIstrict\fR\fB)\fR" 4
.IX Item "strict (strict)"
Strictly hierarchical pyramid.
.IP "\fBnormal (\fR\fInormal\fR\fB)\fR" 4
.IX Item "normal (normal)"
Non-strict (not Blu-ray compatible).
.RE
.RS 4
.RE
.IP \fBmixed-refs\fR 4
.IX Item "mixed-refs"
Enable the use of one reference per partition, as opposed to one
reference per macroblock when set to 1. When set to 0, it has the
same effect as \fBx264\fR's \fB\-\-no\-mixed\-refs\fR option.
.IP \fB8x8dct\fR 4
.IX Item "8x8dct"
Enable adaptive spatial transform (high profile 8x8 transform)
when set to 1. When set to 0, it has the same effect as
\&\fBx264\fR's \fB\-\-no\-8x8dct\fR option.
.IP \fBfast-pskip\fR 4
.IX Item "fast-pskip"
Enable early SKIP detection on P\-frames when set to 1. When set
to 0, it has the same effect as \fBx264\fR's
\&\fB\-\-no\-fast\-pskip\fR option.
.IP "\fBaud (\fR\fIaud\fR\fB)\fR" 4
.IX Item "aud (aud)"
Enable use of access unit delimiters when set to 1.
.IP \fBmbtree\fR 4
.IX Item "mbtree"
Enable use macroblock tree ratecontrol when set to 1. When set
to 0, it has the same effect as \fBx264\fR's
\&\fB\-\-no\-mbtree\fR option.
.IP "\fBdeblock (\fR\fIdeblock\fR\fB)\fR" 4
.IX Item "deblock (deblock)"
Set loop filter parameters, in \fIalpha\fR:\fIbeta\fR form.
.IP "\fBcplxblur (\fR\fIcplxblur\fR\fB)\fR" 4
.IX Item "cplxblur (cplxblur)"
Set fluctuations reduction in QP (before curve compression).
.IP "\fBpartitions (\fR\fIpartitions\fR\fB)\fR" 4
.IX Item "partitions (partitions)"
Set partitions to consider as a comma-separated list of values.
Possible values in the list:
.RS 4
.IP \fBp8x8\fR 4
.IX Item "p8x8"
8x8 P\-frame partition.
.IP \fBp4x4\fR 4
.IX Item "p4x4"
4x4 P\-frame partition.
.IP \fBb8x8\fR 4
.IX Item "b8x8"
4x4 B\-frame partition.
.IP \fBi8x8\fR 4
.IX Item "i8x8"
8x8 I\-frame partition.
.IP \fBi4x4\fR 4
.IX Item "i4x4"
4x4 I\-frame partition.
(Enabling \fBp4x4\fR requires \fBp8x8\fR to be enabled. Enabling
\&\fBi8x8\fR requires adaptive spatial transform (\fB8x8dct\fR
option) to be enabled.)
.IP "\fBnone (\fR\fInone\fR\fB)\fR" 4
.IX Item "none (none)"
Do not consider any partitions.
.IP "\fBall (\fR\fIall\fR\fB)\fR" 4
.IX Item "all (all)"
Consider every partition.
.RE
.RS 4
.RE
.IP "\fBdirect-pred (\fR\fIdirect\fR\fB)\fR" 4
.IX Item "direct-pred (direct)"
Set direct MV prediction mode. Possible values:
.RS 4
.IP "\fBnone (\fR\fInone\fR\fB)\fR" 4
.IX Item "none (none)"
Disable MV prediction.
.IP "\fBspatial (\fR\fIspatial\fR\fB)\fR" 4
.IX Item "spatial (spatial)"
Enable spatial predicting.
.IP "\fBtemporal (\fR\fItemporal\fR\fB)\fR" 4
.IX Item "temporal (temporal)"
Enable temporal predicting.
.IP "\fBauto (\fR\fIauto\fR\fB)\fR" 4
.IX Item "auto (auto)"
Automatically decided.
.RE
.RS 4
.RE
.IP "\fBslice-max-size (\fR\fIslice-max-size\fR\fB)\fR" 4
.IX Item "slice-max-size (slice-max-size)"
Set the limit of the size of each slice in bytes. If not specified
but RTP payload size (\fBps\fR) is specified, that is used.
.IP "\fBstats (\fR\fIstats\fR\fB)\fR" 4
.IX Item "stats (stats)"
Set the file name for multi-pass stats.
.IP "\fBnal-hrd (\fR\fInal-hrd\fR\fB)\fR" 4
.IX Item "nal-hrd (nal-hrd)"
Set signal HRD information (requires \fBvbv-bufsize\fR to be set).
Possible values:
.RS 4
.IP "\fBnone (\fR\fInone\fR\fB)\fR" 4
.IX Item "none (none)"
Disable HRD information signaling.
.IP "\fBvbr (\fR\fIvbr\fR\fB)\fR" 4
.IX Item "vbr (vbr)"
Variable bit rate.
.IP "\fBcbr (\fR\fIcbr\fR\fB)\fR" 4
.IX Item "cbr (cbr)"
Constant bit rate (not allowed in MP4 container).
.RE
.RS 4
.RE
.IP "\fBx264opts\fR \fIopts\fR" 4
.IX Item "x264opts opts"
.PD 0
.IP "\fBx264\-params\fR \fIopts\fR" 4
.IX Item "x264-params opts"
.PD
Override the x264 configuration using a :\-separated list of key=value
options.
.Sp
The argument for both options is a list of \fIkey\fR=\fIvalue\fR
couples separated by ":". With \fBx264opts\fR the value can be
omitted, and the value \f(CW1\fR is assumed in that case.
.Sp
For \fIfilter\fR and \fIpsy-rd\fR options values that use ":" as a
separator themselves, use "," instead. They accept it as well since
long ago but this is kept undocumented for some reason.
.Sp
For example, the options might be provided as:
.Sp
.Vb 1
\&        level=30:bframes=0:weightp=0:cabac=0:ref=1:vbv\-maxrate=768:vbv\-bufsize=2000:analyse=all:me=umh:no\-fast\-pskip=1:subq=6:8x8dct=0:trellis=0
.Ve
.Sp
For example to specify libx264 encoding options with \fBffmpeg\fR:
.Sp
.Vb 1
\&        ffmpeg \-i foo.mpg \-c:v libx264 \-x264opts keyint=123:min\-keyint=20 \-an out.mkv
.Ve
.Sp
To get the complete list of the libx264 options, invoke the command
\&\fBx264 \-\-fullhelp\fR or consult the libx264 documentation.
.IP "\fBa53cc\fR \fIboolean\fR" 4
.IX Item "a53cc boolean"
Import closed captions (which must be ATSC compatible format) into output.
Only the mpeg2 and h264 decoders provide these. Default is 1 (on).
.IP "\fBudu_sei\fR \fIboolean\fR" 4
.IX Item "udu_sei boolean"
Import user data unregistered SEI if available into output. Default is 0 (off).
.IP "\fBmb_info\fR \fIboolean\fR" 4
.IX Item "mb_info boolean"
Set mb_info data through AVFrameSideData, only useful when used from the
API. Default is 0 (off).
.PP
Encoding ffpresets for common usages are provided so they can be used with the
general presets system (e.g. passing the \fBpre\fR option).
.SS libx265
.IX Subsection "libx265"
x265 H.265/HEVC encoder wrapper.
.PP
This encoder requires the presence of the libx265 headers and library
during configuration. You need to explicitly configure the build with
\&\fB\-\-enable\-libx265\fR.
.PP
\fIOptions\fR
.IX Subsection "Options"
.IP \fBb\fR 4
.IX Item "b"
Sets target video bitrate.
.IP \fBbf\fR 4
.IX Item "bf"
.PD 0
.IP \fBg\fR 4
.IX Item "g"
.PD
Set the GOP size.
.IP \fBkeyint_min\fR 4
.IX Item "keyint_min"
Minimum GOP size.
.IP \fBrefs\fR 4
.IX Item "refs"
Number of reference frames each P\-frame can use. The range is from \fI1\-16\fR.
.IP \fBpreset\fR 4
.IX Item "preset"
Set the x265 preset.
.IP \fBtune\fR 4
.IX Item "tune"
Set the x265 tune parameter.
.IP \fBprofile\fR 4
.IX Item "profile"
Set profile restrictions.
.IP \fBcrf\fR 4
.IX Item "crf"
Set the quality for constant quality mode.
.IP \fBqp\fR 4
.IX Item "qp"
Set constant quantization rate control method parameter.
.IP \fBqmin\fR 4
.IX Item "qmin"
Minimum quantizer scale.
.IP \fBqmax\fR 4
.IX Item "qmax"
Maximum quantizer scale.
.IP \fBqdiff\fR 4
.IX Item "qdiff"
Maximum difference between quantizer scales.
.IP \fBqblur\fR 4
.IX Item "qblur"
Quantizer curve blur
.IP \fBqcomp\fR 4
.IX Item "qcomp"
Quantizer curve compression factor
.IP \fBi_qfactor\fR 4
.IX Item "i_qfactor"
.PD 0
.IP \fBb_qfactor\fR 4
.IX Item "b_qfactor"
.IP \fBforced-idr\fR 4
.IX Item "forced-idr"
.PD
Normally, when forcing a I\-frame type, the encoder can select any type
of I\-frame. This option forces it to choose an IDR-frame.
.IP "\fBudu_sei\fR \fIboolean\fR" 4
.IX Item "udu_sei boolean"
Import user data unregistered SEI if available into output. Default is 0 (off).
.IP \fBx265\-params\fR 4
.IX Item "x265-params"
Set x265 options using a list of \fIkey\fR=\fIvalue\fR couples separated
by ":". See \fBx265 \-\-help\fR for a list of options.
.Sp
For example to specify libx265 encoding options with \fB\-x265\-params\fR:
.Sp
.Vb 1
\&        ffmpeg \-i input \-c:v libx265 \-x265\-params crf=26:psy\-rd=1 output.mp4
.Ve
.SS libxavs2
.IX Subsection "libxavs2"
xavs2 AVS2\-P2/IEEE1857.4 encoder wrapper.
.PP
This encoder requires the presence of the libxavs2 headers and library
during configuration. You need to explicitly configure the build with
\&\fB\-\-enable\-libxavs2\fR.
.PP
The following standard libavcodec options are used:
.IP \(bu 4
\&\fBb\fR / \fBbit_rate\fR
.IP \(bu 4
\&\fBg\fR / \fBgop_size\fR
.IP \(bu 4
\&\fBbf\fR / \fBmax_b_frames\fR
.PP
The encoder also has its own specific options:
.PP
\fIOptions\fR
.IX Subsection "Options"
.IP \fBlcu_row_threads\fR 4
.IX Item "lcu_row_threads"
Set the number of parallel threads for rows from 1 to 8 (default 5).
.IP \fBinitial_qp\fR 4
.IX Item "initial_qp"
Set the xavs2 quantization parameter from 1 to 63 (default 34). This is
used to set the initial qp for the first frame.
.IP \fBqp\fR 4
.IX Item "qp"
Set the xavs2 quantization parameter from 1 to 63 (default 34). This is
used to set the qp value under constant-QP mode.
.IP \fBmax_qp\fR 4
.IX Item "max_qp"
Set the max qp for rate control from 1 to 63 (default 55).
.IP \fBmin_qp\fR 4
.IX Item "min_qp"
Set the min qp for rate control from 1 to 63 (default 20).
.IP \fBspeed_level\fR 4
.IX Item "speed_level"
Set the Speed level from 0 to 9 (default 0). Higher is better but slower.
.IP \fBlog_level\fR 4
.IX Item "log_level"
Set the log level from \-1 to 3 (default 0). \-1: none, 0: error,
1: warning, 2: info, 3: debug.
.IP \fBxavs2\-params\fR 4
.IX Item "xavs2-params"
Set xavs2 options using a list of \fIkey\fR=\fIvalue\fR couples separated
by ":".
.Sp
For example to specify libxavs2 encoding options with \fB\-xavs2\-params\fR:
.Sp
.Vb 1
\&        ffmpeg \-i input \-c:v libxavs2 \-xavs2\-params RdoqLevel=0 output.avs2
.Ve
.SS libxeve
.IX Subsection "libxeve"
eXtra-fast Essential Video Encoder (XEVE) MPEG\-5 EVC encoder wrapper.
The xeve-equivalent options or values are listed in parentheses for easy migration.
.PP
This encoder requires the presence of the libxeve headers and library
during configuration. You need to explicitly configure the build with
\&\fB\-\-enable\-libxeve\fR.
.Sp
.RS 4
Many libxeve encoder options are mapped to FFmpeg global codec options,
while unique encoder options are provided through private options.
Additionally the xeve-params private options allows one to pass a list
of key=value tuples as accepted by the libxeve \f(CW\*(C`parse_xeve_params\*(C'\fR function.
.RE
.PP
The xeve project website is at <\fBhttps://github.com/mpeg5/xeve\fR>.
.PP
\fIOptions\fR
.IX Subsection "Options"
.PP
The following options are supported by the libxeve wrapper.
The xeve-equivalent options or values are listed in parentheses for easy migration.
.Sp
.RS 4
To reduce the duplication of documentation, only the private options
and some others requiring special attention are documented here. For
the documentation of the undocumented generic options, see
\&\fBthe Codec Options chapter\fR.
.RE
.Sp
.RS 4
To get a more accurate and extensive documentation of the libxeve options,
invoke the command  \f(CW\*(C`xeve_app \-\-help\*(C'\fR or consult the libxeve documentation.
.RE
.IP "\fBb (\fR\fIbitrate\fR\fB)\fR" 4
.IX Item "b (bitrate)"
Set target video bitrate in bits/s.
Note that FFmpeg's b option is expressed in bits/s, while xeve's bitrate is in kilobits/s.
.IP "\fBbf (\fR\fIbframes\fR\fB)\fR" 4
.IX Item "bf (bframes)"
Set the maximum number of B frames (1,3,7,15).
.IP "\fBg (\fR\fIkeyint\fR\fB)\fR" 4
.IX Item "g (keyint)"
Set the GOP size (I\-picture period).
.IP "\fBpreset (\fR\fIpreset\fR\fB)\fR" 4
.IX Item "preset (preset)"
Set the xeve preset.
Set the encoder preset value to determine encoding speed [fast, medium, slow, placebo]
.IP "\fBtune (\fR\fItune\fR\fB)\fR" 4
.IX Item "tune (tune)"
Set the encoder tune parameter [psnr, zerolatency]
.IP "\fBprofile (\fR\fIprofile\fR\fB)\fR" 4
.IX Item "profile (profile)"
Set the encoder profile [0: baseline; 1: main]
.IP "\fBcrf (\fR\fIcrf\fR\fB)\fR" 4
.IX Item "crf (crf)"
Set the quality for constant quality mode.
Constant rate factor <10..49> [default: 32]
.IP "\fBqp (\fR\fIqp\fR\fB)\fR" 4
.IX Item "qp (qp)"
Set constant quantization rate control method parameter.
Quantization parameter qp <0..51> [default: 32]
.IP "\fBthreads (\fR\fIthreads\fR\fB)\fR" 4
.IX Item "threads (threads)"
Force to use a specific number of threads
.SS libxvid
.IX Subsection "libxvid"
Xvid MPEG\-4 Part 2 encoder wrapper.
.PP
This encoder requires the presence of the libxvidcore headers and library
during configuration. You need to explicitly configure the build with
\&\f(CW\*(C`\-\-enable\-libxvid \-\-enable\-gpl\*(C'\fR.
.PP
The native \f(CW\*(C`mpeg4\*(C'\fR encoder supports the MPEG\-4 Part 2 format, so
users can encode to this format without this library.
.PP
\fIOptions\fR
.IX Subsection "Options"
.PP
The following options are supported by the libxvid wrapper. Some of
the following options are listed but are not documented, and
correspond to shared codec options. See \fBthe Codec
Options chapter\fR for their documentation. The other shared options
which are not listed have no effect for the libxvid encoder.
.IP \fBb\fR 4
.IX Item "b"
.PD 0
.IP \fBg\fR 4
.IX Item "g"
.IP \fBqmin\fR 4
.IX Item "qmin"
.IP \fBqmax\fR 4
.IX Item "qmax"
.IP \fBmpeg_quant\fR 4
.IX Item "mpeg_quant"
.IP \fBthreads\fR 4
.IX Item "threads"
.IP \fBbf\fR 4
.IX Item "bf"
.IP \fBb_qfactor\fR 4
.IX Item "b_qfactor"
.IP \fBb_qoffset\fR 4
.IX Item "b_qoffset"
.IP \fBflags\fR 4
.IX Item "flags"
.PD
Set specific encoding flags. Possible values:
.RS 4
.IP \fBmv4\fR 4
.IX Item "mv4"
Use four motion vector by macroblock.
.IP \fBaic\fR 4
.IX Item "aic"
Enable high quality AC prediction.
.IP \fBgray\fR 4
.IX Item "gray"
Only encode grayscale.
.IP \fBqpel\fR 4
.IX Item "qpel"
Enable quarter-pixel motion compensation.
.IP \fBcgop\fR 4
.IX Item "cgop"
Enable closed GOP.
.IP \fBglobal_header\fR 4
.IX Item "global_header"
Place global headers in extradata instead of every keyframe.
.RE
.RS 4
.RE
.IP \fBgmc\fR 4
.IX Item "gmc"
Enable the use of global motion compensation (GMC).  Default is 0
(disabled).
.IP \fBme_quality\fR 4
.IX Item "me_quality"
Set motion estimation quality level. Possible values in decreasing order of
speed and increasing order of quality:
.RS 4
.IP \fB0\fR 4
.IX Item "0"
Use no motion estimation (default).
.IP "\fB1, 2\fR" 4
.IX Item "1, 2"
Enable advanced diamond zonal search for 16x16 blocks and half-pixel
refinement for 16x16 blocks.
.IP "\fB3, 4\fR" 4
.IX Item "3, 4"
Enable all of the things described above, plus advanced diamond zonal
search for 8x8 blocks and half-pixel refinement for 8x8 blocks, also
enable motion estimation on chroma planes for P and B\-frames.
.IP "\fB5, 6\fR" 4
.IX Item "5, 6"
Enable all of the things described above, plus extended 16x16 and 8x8
blocks search.
.RE
.RS 4
.RE
.IP \fBmbd\fR 4
.IX Item "mbd"
Set macroblock decision algorithm. Possible values in the increasing
order of quality:
.RS 4
.IP \fBsimple\fR 4
.IX Item "simple"
Use macroblock comparing function algorithm (default).
.IP \fBbits\fR 4
.IX Item "bits"
Enable rate distortion-based half pixel and quarter pixel refinement for
16x16 blocks.
.IP \fBrd\fR 4
.IX Item "rd"
Enable all of the things described above, plus rate distortion-based
half pixel and quarter pixel refinement for 8x8 blocks, and rate
distortion-based search using square pattern.
.RE
.RS 4
.RE
.IP \fBlumi_aq\fR 4
.IX Item "lumi_aq"
Enable lumi masking adaptive quantization when set to 1. Default is 0
(disabled).
.IP \fBvariance_aq\fR 4
.IX Item "variance_aq"
Enable variance adaptive quantization when set to 1. Default is 0
(disabled).
.Sp
When combined with \fBlumi_aq\fR, the resulting quality will not
be better than any of the two specified individually. In other
words, the resulting quality will be the worse one of the two
effects.
.IP \fBtrellis\fR 4
.IX Item "trellis"
Set rate-distortion optimal quantization.
.IP \fBssim\fR 4
.IX Item "ssim"
Set structural similarity (SSIM) displaying method. Possible values:
.RS 4
.IP \fBoff\fR 4
.IX Item "off"
Disable displaying of SSIM information.
.IP \fBavg\fR 4
.IX Item "avg"
Output average SSIM at the end of encoding to stdout. The format of
showing the average SSIM is:
.Sp
.Vb 1
\&        Average SSIM: %f
.Ve
.Sp
For users who are not familiar with C, \f(CW%f\fR means a float number, or
a decimal (e.g. 0.939232).
.IP \fBframe\fR 4
.IX Item "frame"
Output both per-frame SSIM data during encoding and average SSIM at
the end of encoding to stdout. The format of per-frame information
is:
.Sp
.Vb 1
\&               SSIM: avg: %1.3f min: %1.3f max: %1.3f
.Ve
.Sp
For users who are not familiar with C, \f(CW%1\fR.3f means a float number
rounded to 3 digits after the dot (e.g. 0.932).
.RE
.RS 4
.RE
.IP \fBssim_acc\fR 4
.IX Item "ssim_acc"
Set SSIM accuracy. Valid options are integers within the range of
0\-4, while 0 gives the most accurate result and 4 computes the
fastest.
.SS MediaCodec
.IX Subsection "MediaCodec"
MediaCodec encoder wrapper enables hardware-accelerated video encoding on
Android device. It supports H.264, H.265 (HEVC), VP8, VP9, MPEG\-4, and AV1
encoding (whether works or not is device dependent).
.PP
Android provides two sets of APIs: Java MediaCodec and NDK MediaCodec. The
MediaCodec encoder wrapper supports both. Note that the NDK MediaCodec API
operates without requiring JVM, but may fail to function outside the JVM
environment due to dependencies on system framework services, particularly
after Android 15.
.IP "\fBndk_codec\fR \fIboolean\fR" 4
.IX Item "ndk_codec boolean"
Use the NDK-based MediaCodec API instead of the Java API. Enabled by default
if \f(CWav_jni_get_java_vm()\fR return NULL.
.IP "\fBndk_async\fR \fIboolean\fR" 4
.IX Item "ndk_async boolean"
Use NDK MediaCodec in async mode. Async mode has less overhead than poll in a
loop in sync mode. The drawback of async mode is AV_CODEC_FLAG_GLOBAL_HEADER
doesn't work (use extract_extradata bsf when necessary). It doesn't work and
will be disabled automatically on devices below Android 8.0.
.IP "\fBcodec_name\fR \fIstring\fR" 4
.IX Item "codec_name string"
A codec type can have multiple implementations on a single device, this option
specify which backend to use (via MediaCodec createCodecByName API). It's NULL
by default, and encoder is created by createEncoderByType.
.IP "\fBbitrate_mode\fR \fIinteger\fR" 4
.IX Item "bitrate_mode integer"
Possible values:
.RS 4
.IP \fBcq\fR 4
.IX Item "cq"
Constant quality mode
.IP \fBvbr\fR 4
.IX Item "vbr"
Variable bitrate mode
.IP \fBcbr\fR 4
.IX Item "cbr"
Constant bitrate mode
.IP \fBcbr_fd\fR 4
.IX Item "cbr_fd"
Constant bitrate mode with frame drops
.RE
.RS 4
.RE
.IP "\fBpts_as_dts\fR \fIboolean\fR" 4
.IX Item "pts_as_dts boolean"
Use PTS as DTS. This is a workaround since MediaCodec API doesn't provide
decoding timestamp. It is enabled automatically if B frame is 0.
.IP "\fBoperating_rate\fR \fIinteger\fR" 4
.IX Item "operating_rate integer"
The desired operating rate that the codec will need to operate at, zero for
unspecified. This is used for cases like high\-speed/slow\-motion video capture,
where the video encoder format contains the target playback rate (e.g. 30fps),
but the component must be able to handle the high operating capture rate (e.g.
240fps). This rate will be used by codec for resource planning and setting the
operating points.
.IP "\fBqp_i_min\fR \fIinteger\fR" 4
.IX Item "qp_i_min integer"
Minimum quantization parameter for I frame.
.IP "\fBqp_p_min\fR \fIinteger\fR" 4
.IX Item "qp_p_min integer"
Minimum quantization parameter for P frame.
.IP "\fBqp_b_min\fR \fIinteger\fR" 4
.IX Item "qp_b_min integer"
Minimum quantization parameter for B frame.
.IP "\fBqp_i_max\fR \fIinteger\fR" 4
.IX Item "qp_i_max integer"
Maximum quantization parameter for I frame.
.IP "\fBqp_p_max\fR \fIinteger\fR" 4
.IX Item "qp_p_max integer"
Maximum quantization parameter for P frame.
.IP "\fBqp_b_max\fR \fIinteger\fR" 4
.IX Item "qp_b_max integer"
Maximum quantization parameter for B frame.
.SS MediaFoundation
.IX Subsection "MediaFoundation"
This provides wrappers to encoders (both audio and video) in the
MediaFoundation framework. It can access both SW and HW encoders.
Video encoders can take input in either of nv12 or yuv420p form
(some encoders support both, some support only either \- in practice,
nv12 is the safer choice, especially among HW encoders).
.SS "Microsoft RLE"
.IX Subsection "Microsoft RLE"
Microsoft RLE aka MSRLE encoder.
Only 8\-bit palette mode supported.
Compatible with Windows 3.1 and Windows 95.
.PP
\fIOptions\fR
.IX Subsection "Options"
.IP "\fBg\fR \fIinteger\fR" 4
.IX Item "g integer"
Keyframe interval.
A keyframe is inserted at least every \f(CW\*(C`\-g\*(C'\fR frames, sometimes sooner.
.SS mpeg2
.IX Subsection "mpeg2"
MPEG\-2 video encoder.
.PP
\fIOptions\fR
.IX Subsection "Options"
.IP \fBprofile\fR 4
.IX Item "profile"
Select the mpeg2 profile to encode:
.RS 4
.IP \fB422\fR 4
.IX Item "422"
.PD 0
.IP \fBhigh\fR 4
.IX Item "high"
.IP \fBss\fR 4
.IX Item "ss"
.PD
Spatially Scalable
.IP \fBsnr\fR 4
.IX Item "snr"
SNR Scalable
.IP \fBmain\fR 4
.IX Item "main"
.PD 0
.IP \fBsimple\fR 4
.IX Item "simple"
.RE
.RS 4
.RE
.IP \fBlevel\fR 4
.IX Item "level"
.PD
Select the mpeg2 level to encode:
.RS 4
.IP \fBhigh\fR 4
.IX Item "high"
.PD 0
.IP \fBhigh1440\fR 4
.IX Item "high1440"
.IP \fBmain\fR 4
.IX Item "main"
.IP \fBlow\fR 4
.IX Item "low"
.RE
.RS 4
.RE
.IP "\fBseq_disp_ext\fR \fIinteger\fR" 4
.IX Item "seq_disp_ext integer"
.PD
Specifies if the encoder should write a sequence_display_extension to the
output.
.RS 4
.IP \fB\-1\fR 4
.IX Item "-1"
.PD 0
.IP \fBauto\fR 4
.IX Item "auto"
.PD
Decide automatically to write it or not (this is the default) by checking if
the data to be written is different from the default or unspecified values.
.IP \fB0\fR 4
.IX Item "0"
.PD 0
.IP \fBnever\fR 4
.IX Item "never"
.PD
Never write it.
.IP \fB1\fR 4
.IX Item "1"
.PD 0
.IP \fBalways\fR 4
.IX Item "always"
.PD
Always write it.
.RE
.RS 4
.RE
.IP "\fBvideo_format\fR \fIinteger\fR" 4
.IX Item "video_format integer"
Specifies the video_format written into the sequence display extension
indicating the source of the video pictures. The default is \fBunspecified\fR,
can be \fBcomponent\fR, \fBpal\fR, \fBntsc\fR, \fBsecam\fR or \fBmac\fR.
For maximum compatibility, use \fBcomponent\fR.
.IP "\fBa53cc\fR \fIboolean\fR" 4
.IX Item "a53cc boolean"
Import closed captions (which must be ATSC compatible format) into output.
Default is 1 (on).
.SS png
.IX Subsection "png"
PNG image encoder.
.PP
\fIOptions\fR
.IX Subsection "Options"
.IP \fBcompression_level\fR 4
.IX Item "compression_level"
Sets the compression level, from 0 to 9(default)
.PP
\fIPrivate options\fR
.IX Subsection "Private options"
.IP "\fBdpi\fR \fIinteger\fR" 4
.IX Item "dpi integer"
Set physical density of pixels, in dots per inch, unset by default
.IP "\fBdpm\fR \fIinteger\fR" 4
.IX Item "dpm integer"
Set physical density of pixels, in dots per meter, unset by default
.IP "\fBpred\fR \fImethod\fR" 4
.IX Item "pred method"
Set prediction method (none, sub, up, avg, paeth, mixed), default is paeth
.SS ProRes
.IX Subsection "ProRes"
Apple ProRes encoder.
.PP
FFmpeg contains 2 ProRes encoders, the prores-aw and prores-ks encoder.
The used encoder can be chosen with the \f(CW\*(C`\-vcodec\*(C'\fR option.
.PP
\fIPrivate Options for prores-ks\fR
.IX Subsection "Private Options for prores-ks"
.IP "\fBprofile\fR \fIinteger\fR" 4
.IX Item "profile integer"
Select the ProRes profile to encode
.RS 4
.IP \fBproxy\fR 4
.IX Item "proxy"
.PD 0
.IP \fBlt\fR 4
.IX Item "lt"
.IP \fBstandard\fR 4
.IX Item "standard"
.IP \fBhq\fR 4
.IX Item "hq"
.IP \fB4444\fR 4
.IX Item "4444"
.IP \fB4444xq\fR 4
.IX Item "4444xq"
.RE
.RS 4
.RE
.IP "\fBquant_mat\fR \fIinteger\fR" 4
.IX Item "quant_mat integer"
.PD
Select quantization matrix.
.RS 4
.IP \fBauto\fR 4
.IX Item "auto"
.PD 0
.IP \fBdefault\fR 4
.IX Item "default"
.IP \fBproxy\fR 4
.IX Item "proxy"
.IP \fBlt\fR 4
.IX Item "lt"
.IP \fBstandard\fR 4
.IX Item "standard"
.IP \fBhq\fR 4
.IX Item "hq"
.RE
.RS 4
.PD
.Sp
If set to \fIauto\fR, the matrix matching the profile will be picked.
If not set, the matrix providing the highest quality, \fIdefault\fR, will be
picked.
.RE
.IP "\fBbits_per_mb\fR \fIinteger\fR" 4
.IX Item "bits_per_mb integer"
How many bits to allot for coding one macroblock. Different profiles use
between 200 and 2400 bits per macroblock, the maximum is 8000.
.IP "\fBmbs_per_slice\fR \fIinteger\fR" 4
.IX Item "mbs_per_slice integer"
Number of macroblocks in each slice (1\-8); the default value (8)
should be good in almost all situations.
.IP "\fBvendor\fR \fIstring\fR" 4
.IX Item "vendor string"
Override the 4\-byte vendor ID.
A custom vendor ID like \fIapl0\fR would claim the stream was produced by
the Apple encoder.
.IP "\fBalpha_bits\fR \fIinteger\fR" 4
.IX Item "alpha_bits integer"
Specify number of bits for alpha component.
Possible values are \fI0\fR, \fI8\fR and \fI16\fR.
Use \fI0\fR to disable alpha plane coding.
.PP
\fISpeed considerations\fR
.IX Subsection "Speed considerations"
.PP
In the default mode of operation the encoder has to honor frame constraints
(i.e. not produce frames with size bigger than requested) while still making
output picture as good as possible.
A frame containing a lot of small details is harder to compress and the encoder
would spend more time searching for appropriate quantizers for each slice.
.PP
Setting a higher \fBbits_per_mb\fR limit will improve the speed.
.PP
For the fastest encoding speed set the \fBqscale\fR parameter (4 is the
recommended value) and do not set a size constraint.
.SS "QSV Encoders"
.IX Subsection "QSV Encoders"
The family of Intel QuickSync Video encoders (MPEG\-2, H.264, HEVC, JPEG/MJPEG,
VP9, AV1)
.PP
\fIRatecontrol Method\fR
.IX Subsection "Ratecontrol Method"
.PP
The ratecontrol method is selected as follows:
.IP \(bu 4
When \fBglobal_quality\fR is specified, a quality-based mode is used.
Specifically this means either
.RS 4
.IP \- 4
\&\fICQP\fR \- constant quantizer scale, when the \fBqscale\fR codec flag is
also set (the \fB\-qscale\fR ffmpeg option).
.IP \- 4
\&\fILA_ICQ\fR \- intelligent constant quality with lookahead, when the
\&\fBlook_ahead\fR option is also set.
.IP \- 4
\&\fIICQ\fR \-\- intelligent constant quality otherwise. For the ICQ modes, global
quality range is 1 to 51, with 1 being the best quality.
.RE
.RS 4
.RE
.IP \(bu 4
Otherwise when the desired average bitrate is specified with the \fBb\fR
option, a bitrate-based mode is used.
.RS 4
.IP \- 4
\&\fILA\fR \- VBR with lookahead, when the \fBlook_ahead\fR option is specified.
.IP \- 4
\&\fIVCM\fR \- video conferencing mode, when the \fBvcm\fR option is set.
.IP \- 4
\&\fICBR\fR \- constant bitrate, when \fBmaxrate\fR is specified and equal to
the average bitrate.
.IP \- 4
\&\fIVBR\fR \- variable bitrate, when \fBmaxrate\fR is specified, but is higher
than the average bitrate.
.IP \- 4
\&\fIAVBR\fR \- average VBR mode, when \fBmaxrate\fR is not specified, both
\&\fBavbr_accuracy\fR and \fBavbr_convergence\fR are set to non-zero. This
mode is available for H264 and HEVC on Windows.
.RE
.RS 4
.RE
.IP \(bu 4
Otherwise the default ratecontrol method \fICQP\fR is used.
.PP
Note that depending on your system, a different mode than the one you specified
may be selected by the encoder. Set the verbosity level to \fIverbose\fR or
higher to see the actual settings used by the QSV runtime.
.PP
\fIGlobal Options \-> MSDK Options\fR
.IX Subsection "Global Options -> MSDK Options"
.PP
Additional libavcodec global options are mapped to MSDK options as follows:
.IP \(bu 4
\&\fBg/gop_size\fR \-> \fBGopPicSize\fR
.IP \(bu 4
\&\fBbf/max_b_frames\fR+1 \-> \fBGopRefDist\fR
.IP \(bu 4
\&\fBrc_init_occupancy/rc_initial_buffer_occupancy\fR \->
\&\fBInitialDelayInKB\fR
.IP \(bu 4
\&\fBslices\fR \-> \fBNumSlice\fR
.IP \(bu 4
\&\fBrefs\fR \-> \fBNumRefFrame\fR
.IP \(bu 4
\&\fBb_strategy/b_frame_strategy\fR \-> \fBBRefType\fR
.IP \(bu 4
\&\fBcgop/CLOSED_GOP\fR codec flag \-> \fBGopOptFlag\fR
.IP \(bu 4
For the \fICQP\fR mode, the \fBi_qfactor/i_qoffset\fR and
\&\fBb_qfactor/b_qoffset\fR set the difference between \fIQPP\fR and \fIQPI\fR,
and \fIQPP\fR and \fIQPB\fR respectively.
.IP \(bu 4
Setting the \fBcoder\fR option to the value \fIvlc\fR will make the H.264
encoder use CAVLC instead of CABAC.
.PP
\fICommon Options\fR
.IX Subsection "Common Options"
.PP
Following options are used by all qsv encoders.
.IP \fIasync_depth\fR 4
.IX Item "async_depth"
Specifies how many asynchronous operations an application performs
before the application explicitly synchronizes the result. If zero,
the value is not specified.
.IP \fIpreset\fR 4
.IX Item "preset"
This option itemizes a range of choices from veryfast (best speed) to veryslow
(best quality).
.RS 4
.IP \fBveryfast\fR 4
.IX Item "veryfast"
.PD 0
.IP \fBfaster\fR 4
.IX Item "faster"
.IP \fBfast\fR 4
.IX Item "fast"
.IP \fBmedium\fR 4
.IX Item "medium"
.IP \fBslow\fR 4
.IX Item "slow"
.IP \fBslower\fR 4
.IX Item "slower"
.IP \fBveryslow\fR 4
.IX Item "veryslow"
.RE
.RS 4
.RE
.IP \fIforced_idr\fR 4
.IX Item "forced_idr"
.PD
Forcing I frames as IDR frames.
.IP \fIlow_power\fR 4
.IX Item "low_power"
For encoders set this flag to ON to reduce power consumption and GPU usage.
.PP
\fIRuntime Options\fR
.IX Subsection "Runtime Options"
.PP
Following options can be used during qsv encoding.
.IP \fIglobal_quality\fR 4
.IX Item "global_quality"
.PD 0
.IP \fIi_quant_factor\fR 4
.IX Item "i_quant_factor"
.IP \fIi_quant_offset\fR 4
.IX Item "i_quant_offset"
.IP \fIb_quant_factor\fR 4
.IX Item "b_quant_factor"
.IP \fIb_quant_offset\fR 4
.IX Item "b_quant_offset"
.PD
Supported in h264_qsv and hevc_qsv.
Change these value to reset qsv codec's qp configuration.
.IP \fImax_frame_size\fR 4
.IX Item "max_frame_size"
Supported in h264_qsv and hevc_qsv.
Change this value to reset qsv codec's MaxFrameSize configuration.
.IP \fIgop_size\fR 4
.IX Item "gop_size"
Change this value to reset qsv codec's gop configuration.
.IP \fIint_ref_type\fR 4
.IX Item "int_ref_type"
.PD 0
.IP \fIint_ref_cycle_size\fR 4
.IX Item "int_ref_cycle_size"
.IP \fIint_ref_qp_delta\fR 4
.IX Item "int_ref_qp_delta"
.IP \fIint_ref_cycle_dist\fR 4
.IX Item "int_ref_cycle_dist"
.PD
Supported in h264_qsv and hevc_qsv.
Change these value to reset qsv codec's Intra Refresh configuration.
.IP \fIqmax\fR 4
.IX Item "qmax"
.PD 0
.IP \fIqmin\fR 4
.IX Item "qmin"
.IP \fImax_qp_i\fR 4
.IX Item "max_qp_i"
.IP \fImin_qp_i\fR 4
.IX Item "min_qp_i"
.IP \fImax_qp_p\fR 4
.IX Item "max_qp_p"
.IP \fImin_qp_p\fR 4
.IX Item "min_qp_p"
.IP \fImax_qp_b\fR 4
.IX Item "max_qp_b"
.IP \fImin_qp_b\fR 4
.IX Item "min_qp_b"
.PD
Supported in h264_qsv.
Change these value to reset qsv codec's max/min qp configuration.
.IP \fIlow_delay_brc\fR 4
.IX Item "low_delay_brc"
Supported in h264_qsv, hevc_qsv and av1_qsv.
Change this value to reset qsv codec's low_delay_brc configuration.
.IP \fIframerate\fR 4
.IX Item "framerate"
Change this value to reset qsv codec's framerate configuration.
.IP \fIbit_rate\fR 4
.IX Item "bit_rate"
.PD 0
.IP \fIrc_buffer_size\fR 4
.IX Item "rc_buffer_size"
.IP \fIrc_initial_buffer_occupancy\fR 4
.IX Item "rc_initial_buffer_occupancy"
.IP \fIrc_max_rate\fR 4
.IX Item "rc_max_rate"
.PD
Change these value to reset qsv codec's bitrate control configuration.
.IP \fIpic_timing_sei\fR 4
.IX Item "pic_timing_sei"
Supported in h264_qsv and hevc_qsv.
Change this value to reset qsv codec's pic_timing_sei configuration.
.IP \fIqsv_params\fR 4
.IX Item "qsv_params"
Set QSV encoder parameters as a colon-separated list of key-value pairs.
.Sp
The \fBqsv_params\fR should be formatted as \f(CW\*(C`key1=value1:key2=value2:...\*(C'\fR.
.Sp
These parameters are passed directly to the underlying Intel Quick Sync Video (QSV) encoder using the MFXSetParameter function.
.Sp
Example:
.Sp
.Vb 1
\&        ffmpeg \-i input.mp4 \-c:v h264_qsv \-qsv_params "CodingOption1=1:CodingOption2=2" output.mp4
.Ve
.Sp
This option allows fine-grained control over various encoder-specific settings provided by the QSV encoder.
.PP
\fIH264 options\fR
.IX Subsection "H264 options"
.PP
These options are used by h264_qsv
.IP \fIextbrc\fR 4
.IX Item "extbrc"
Extended bitrate control.
.IP \fIrecovery_point_sei\fR 4
.IX Item "recovery_point_sei"
Set this flag to insert the recovery point SEI message at the beginning of every
intra refresh cycle.
.IP \fIrdo\fR 4
.IX Item "rdo"
Enable rate distortion optimization.
.IP \fImax_frame_size\fR 4
.IX Item "max_frame_size"
Maximum encoded frame size in bytes.
.IP \fImax_frame_size_i\fR 4
.IX Item "max_frame_size_i"
Maximum encoded frame size for I frames in bytes. If this value is set as larger
than zero, then for I frames the value set by max_frame_size is ignored.
.IP \fImax_frame_size_p\fR 4
.IX Item "max_frame_size_p"
Maximum encoded frame size for P frames in bytes. If this value is set as larger
than zero, then for P frames the value set by max_frame_size is ignored.
.IP \fImax_slice_size\fR 4
.IX Item "max_slice_size"
Maximum encoded slice size in bytes.
.IP \fIbitrate_limit\fR 4
.IX Item "bitrate_limit"
Toggle bitrate limitations.
Modifies bitrate to be in the range imposed by the QSV encoder. Setting this
flag off may lead to violation of HRD conformance. Mind that specifying bitrate
below the QSV encoder range might significantly affect quality. If on this
option takes effect in non CQP modes: if bitrate is not in the range imposed
by the QSV encoder, it will be changed to be in the range.
.IP \fImbbrc\fR 4
.IX Item "mbbrc"
Setting this flag enables macroblock level bitrate control that generally
improves subjective visual quality. Enabling this flag may have negative impact
on performance and objective visual quality metric.
.IP \fIlow_delay_brc\fR 4
.IX Item "low_delay_brc"
Setting this flag turns on or off LowDelayBRC feature in qsv plugin, which provides
more accurate bitrate control to minimize the variance of bitstream size frame
by frame. Value: \-1\-default 0\-off 1\-on
.IP \fIadaptive_i\fR 4
.IX Item "adaptive_i"
This flag controls insertion of I frames by the QSV encoder. Turn ON this flag
to allow changing of frame type from P and B to I.
.IP \fIadaptive_b\fR 4
.IX Item "adaptive_b"
This flag controls changing of frame type from B to P.
.IP \fIp_strategy\fR 4
.IX Item "p_strategy"
Enable P\-pyramid: 0\-default 1\-simple 2\-pyramid(bf need to be set to 0).
.IP \fIb_strategy\fR 4
.IX Item "b_strategy"
This option controls usage of B frames as reference.
.IP \fIdblk_idc\fR 4
.IX Item "dblk_idc"
This option disable deblocking. It has value in range 0~2.
.IP \fIcavlc\fR 4
.IX Item "cavlc"
If set, CAVLC is used; if unset, CABAC is used for encoding.
.IP \fIvcm\fR 4
.IX Item "vcm"
Video conferencing mode, please see ratecontrol method.
.IP \fIidr_interval\fR 4
.IX Item "idr_interval"
Distance (in I\-frames) between IDR frames.
.IP \fIpic_timing_sei\fR 4
.IX Item "pic_timing_sei"
Insert picture timing SEI with pic_struct_syntax element.
.IP \fIsingle_sei_nal_unit\fR 4
.IX Item "single_sei_nal_unit"
Put all the SEI messages into one NALU.
.IP \fImax_dec_frame_buffering\fR 4
.IX Item "max_dec_frame_buffering"
Maximum number of frames buffered in the DPB.
.IP \fIlook_ahead\fR 4
.IX Item "look_ahead"
Use VBR algorithm with look ahead.
.IP \fIlook_ahead_depth\fR 4
.IX Item "look_ahead_depth"
Depth of look ahead in number frames.
.IP \fIlook_ahead_downsampling\fR 4
.IX Item "look_ahead_downsampling"
Downscaling factor for the frames saved for the lookahead analysis.
.RS 4
.IP \fBunknown\fR 4
.IX Item "unknown"
.PD 0
.IP \fBauto\fR 4
.IX Item "auto"
.IP \fBoff\fR 4
.IX Item "off"
.IP \fB2x\fR 4
.IX Item "2x"
.IP \fB4x\fR 4
.IX Item "4x"
.RE
.RS 4
.RE
.IP \fIint_ref_type\fR 4
.IX Item "int_ref_type"
.PD
Specifies intra refresh type. The major goal of intra refresh is improvement of
error resilience without significant impact on encoded bitstream size caused by
I frames. The SDK encoder achieves this by encoding part of each frame in
refresh cycle using intra MBs. \fInone\fR means no refresh. \fIvertical\fR means
vertical refresh, by column of MBs. \fIhorizontal\fR means horizontal refresh,
by rows of MBs. \fIslice\fR means horizontal refresh by slices without
overlapping. In case of \fIslice\fR, in_ref_cycle_size is ignored. To enable
intra refresh, B frame should be set to 0.
.IP \fIint_ref_cycle_size\fR 4
.IX Item "int_ref_cycle_size"
Specifies number of pictures within refresh cycle starting from 2. 0 and 1 are
invalid values.
.IP \fIint_ref_qp_delta\fR 4
.IX Item "int_ref_qp_delta"
Specifies QP difference for inserted intra MBs. This is signed value in
[\-51, 51] range if target encoding bit-depth for luma samples is 8 and this
range is [\-63, 63] for 10 bit-depth or [\-75, 75] for 12 bit-depth respectively.
.IP \fIint_ref_cycle_dist\fR 4
.IX Item "int_ref_cycle_dist"
Distance between the beginnings of the intra-refresh cycles in frames.
.IP \fIprofile\fR 4
.IX Item "profile"
.RS 4
.PD 0
.IP \fBunknown\fR 4
.IX Item "unknown"
.IP \fBbaseline\fR 4
.IX Item "baseline"
.IP \fBmain\fR 4
.IX Item "main"
.IP \fBhigh\fR 4
.IX Item "high"
.RE
.RS 4
.RE
.IP \fIa53cc\fR 4
.IX Item "a53cc"
.PD
Use A53 Closed Captions (if available).
.IP \fIaud\fR 4
.IX Item "aud"
Insert the Access Unit Delimiter NAL.
.IP \fImfmode\fR 4
.IX Item "mfmode"
Multi-Frame Mode.
.RS 4
.IP \fBoff\fR 4
.IX Item "off"
.PD 0
.IP \fBauto\fR 4
.IX Item "auto"
.RE
.RS 4
.RE
.IP \fIrepeat_pps\fR 4
.IX Item "repeat_pps"
.PD
Repeat pps for every frame.
.IP \fImax_qp_i\fR 4
.IX Item "max_qp_i"
Maximum video quantizer scale for I frame.
.IP \fImin_qp_i\fR 4
.IX Item "min_qp_i"
Minimum video quantizer scale for I frame.
.IP \fImax_qp_p\fR 4
.IX Item "max_qp_p"
Maximum video quantizer scale for P frame.
.IP \fImin_qp_p\fR 4
.IX Item "min_qp_p"
Minimum video quantizer scale for P frame.
.IP \fImax_qp_b\fR 4
.IX Item "max_qp_b"
Maximum video quantizer scale for B frame.
.IP \fImin_qp_b\fR 4
.IX Item "min_qp_b"
Minimum video quantizer scale for B frame.
.IP \fIscenario\fR 4
.IX Item "scenario"
Provides a hint to encoder about the scenario for the encoding session.
.RS 4
.IP \fBunknown\fR 4
.IX Item "unknown"
.PD 0
.IP \fBdisplayremoting\fR 4
.IX Item "displayremoting"
.IP \fBvideoconference\fR 4
.IX Item "videoconference"
.IP \fBarchive\fR 4
.IX Item "archive"
.IP \fBlivestreaming\fR 4
.IX Item "livestreaming"
.IP \fBcameracapture\fR 4
.IX Item "cameracapture"
.IP \fBvideosurveillance\fR 4
.IX Item "videosurveillance"
.IP \fBgamestreaming\fR 4
.IX Item "gamestreaming"
.IP \fBremotegaming\fR 4
.IX Item "remotegaming"
.RE
.RS 4
.RE
.IP \fIavbr_accuracy\fR 4
.IX Item "avbr_accuracy"
.PD
Accuracy of the AVBR ratecontrol (unit of tenth of percent).
.IP \fIavbr_convergence\fR 4
.IX Item "avbr_convergence"
Convergence of the AVBR ratecontrol (unit of 100 frames)
.Sp
The parameters \fIavbr_accuracy\fR and \fIavbr_convergence\fR are for the
average variable bitrate control (AVBR) algorithm.
The algorithm focuses on overall encoding quality while meeting the specified
bitrate, \fItarget_bitrate\fR, within the accuracy range \fIavbr_accuracy\fR,
after a \fIavbr_Convergence\fR period. This method does not follow HRD and the
instant bitrate is not capped or padded.
.IP \fIskip_frame\fR 4
.IX Item "skip_frame"
Use per-frame metadata "qsv_skip_frame" to skip frame when encoding. This option
defines the usage of this metadata.
.RS 4
.IP \fBno_skip\fR 4
.IX Item "no_skip"
Frame skipping is disabled.
.IP \fBinsert_dummy\fR 4
.IX Item "insert_dummy"
Encoder inserts into bitstream frame where all macroblocks are encoded as
skipped.
.IP \fBinsert_nothing\fR 4
.IX Item "insert_nothing"
Similar to insert_dummy, but encoder inserts nothing into bitstream. The skipped
frames are still used in brc. For example, gop still include skipped frames, and
the frames after skipped frames will be larger in size.
.IP \fBbrc_only\fR 4
.IX Item "brc_only"
skip_frame metadata indicates the number of missed frames before the current
frame.
.RE
.RS 4
.RE
.PP
\fIHEVC Options\fR
.IX Subsection "HEVC Options"
.PP
These options are used by hevc_qsv
.IP \fIextbrc\fR 4
.IX Item "extbrc"
Extended bitrate control.
.IP \fIrecovery_point_sei\fR 4
.IX Item "recovery_point_sei"
Set this flag to insert the recovery point SEI message at the beginning of every
intra refresh cycle.
.IP \fIrdo\fR 4
.IX Item "rdo"
Enable rate distortion optimization.
.IP \fImax_frame_size\fR 4
.IX Item "max_frame_size"
Maximum encoded frame size in bytes.
.IP \fImax_frame_size_i\fR 4
.IX Item "max_frame_size_i"
Maximum encoded frame size for I frames in bytes. If this value is set as larger
than zero, then for I frames the value set by max_frame_size is ignored.
.IP \fImax_frame_size_p\fR 4
.IX Item "max_frame_size_p"
Maximum encoded frame size for P frames in bytes. If this value is set as larger
than zero, then for P frames the value set by max_frame_size is ignored.
.IP \fImax_slice_size\fR 4
.IX Item "max_slice_size"
Maximum encoded slice size in bytes.
.IP \fImbbrc\fR 4
.IX Item "mbbrc"
Setting this flag enables macroblock level bitrate control that generally
improves subjective visual quality. Enabling this flag may have negative impact
on performance and objective visual quality metric.
.IP \fIlow_delay_brc\fR 4
.IX Item "low_delay_brc"
Setting this flag turns on or off LowDelayBRC feature in qsv plugin, which provides
more accurate bitrate control to minimize the variance of bitstream size frame
by frame. Value: \-1\-default 0\-off 1\-on
.IP \fIadaptive_i\fR 4
.IX Item "adaptive_i"
This flag controls insertion of I frames by the QSV encoder. Turn ON this flag
to allow changing of frame type from P and B to I.
.IP \fIadaptive_b\fR 4
.IX Item "adaptive_b"
This flag controls changing of frame type from B to P.
.IP \fIp_strategy\fR 4
.IX Item "p_strategy"
Enable P\-pyramid: 0\-default 1\-simple 2\-pyramid(bf need to be set to 0).
.IP \fIb_strategy\fR 4
.IX Item "b_strategy"
This option controls usage of B frames as reference.
.IP \fIdblk_idc\fR 4
.IX Item "dblk_idc"
This option disable deblocking. It has value in range 0~2.
.IP \fIidr_interval\fR 4
.IX Item "idr_interval"
Distance (in I\-frames) between IDR frames.
.RS 4
.IP \fBbegin_only\fR 4
.IX Item "begin_only"
Output an IDR-frame only at the beginning of the stream.
.RE
.RS 4
.RE
.IP \fIload_plugin\fR 4
.IX Item "load_plugin"
A user plugin to load in an internal session.
.RS 4
.IP \fBnone\fR 4
.IX Item "none"
.PD 0
.IP \fBhevc_sw\fR 4
.IX Item "hevc_sw"
.IP \fBhevc_hw\fR 4
.IX Item "hevc_hw"
.RE
.RS 4
.RE
.IP \fIload_plugins\fR 4
.IX Item "load_plugins"
.PD
A :\-separate list of hexadecimal plugin UIDs to load in
an internal session.
.IP \fIlook_ahead_depth\fR 4
.IX Item "look_ahead_depth"
Depth of look ahead in number frames, available when extbrc option is enabled.
.IP \fIprofile\fR 4
.IX Item "profile"
Set the encoding profile (scc requires libmfx >= 1.32).
.RS 4
.IP \fBunknown\fR 4
.IX Item "unknown"
.PD 0
.IP \fBmain\fR 4
.IX Item "main"
.IP \fBmain10\fR 4
.IX Item "main10"
.IP \fBmainsp\fR 4
.IX Item "mainsp"
.IP \fBrext\fR 4
.IX Item "rext"
.IP \fBscc\fR 4
.IX Item "scc"
.RE
.RS 4
.RE
.IP \fItier\fR 4
.IX Item "tier"
.PD
Set the encoding tier (only level >= 4 can support high tier).
This option only takes effect when the level option is specified.
.RS 4
.IP \fBmain\fR 4
.IX Item "main"
.PD 0
.IP \fBhigh\fR 4
.IX Item "high"
.RE
.RS 4
.RE
.IP \fIgpb\fR 4
.IX Item "gpb"
.PD
1: GPB (generalized P/B frame)
.Sp
0: regular P frame.
.IP \fItile_cols\fR 4
.IX Item "tile_cols"
Number of columns for tiled encoding.
.IP \fItile_rows\fR 4
.IX Item "tile_rows"
Number of rows for tiled encoding.
.IP \fIaud\fR 4
.IX Item "aud"
Insert the Access Unit Delimiter NAL.
.IP \fIpic_timing_sei\fR 4
.IX Item "pic_timing_sei"
Insert picture timing SEI with pic_struct_syntax element.
.IP \fItransform_skip\fR 4
.IX Item "transform_skip"
Turn this option ON to enable transformskip. It is supported on platform equal
or newer than ICL.
.IP \fIint_ref_type\fR 4
.IX Item "int_ref_type"
Specifies intra refresh type. The major goal of intra refresh is improvement of
error resilience without significant impact on encoded bitstream size caused by
I frames. The SDK encoder achieves this by encoding part of each frame in
refresh cycle using intra MBs. \fInone\fR means no refresh. \fIvertical\fR means
vertical refresh, by column of MBs. \fIhorizontal\fR means horizontal refresh,
by rows of MBs. \fIslice\fR means horizontal refresh by slices without
overlapping. In case of \fIslice\fR, in_ref_cycle_size is ignored. To enable
intra refresh, B frame should be set to 0.
.IP \fIint_ref_cycle_size\fR 4
.IX Item "int_ref_cycle_size"
Specifies number of pictures within refresh cycle starting from 2. 0 and 1 are
invalid values.
.IP \fIint_ref_qp_delta\fR 4
.IX Item "int_ref_qp_delta"
Specifies QP difference for inserted intra MBs. This is signed value in
[\-51, 51] range if target encoding bit-depth for luma samples is 8 and this
range is [\-63, 63] for 10 bit-depth or [\-75, 75] for 12 bit-depth respectively.
.IP \fIint_ref_cycle_dist\fR 4
.IX Item "int_ref_cycle_dist"
Distance between the beginnings of the intra-refresh cycles in frames.
.IP \fImax_qp_i\fR 4
.IX Item "max_qp_i"
Maximum video quantizer scale for I frame.
.IP \fImin_qp_i\fR 4
.IX Item "min_qp_i"
Minimum video quantizer scale for I frame.
.IP \fImax_qp_p\fR 4
.IX Item "max_qp_p"
Maximum video quantizer scale for P frame.
.IP \fImin_qp_p\fR 4
.IX Item "min_qp_p"
Minimum video quantizer scale for P frame.
.IP \fImax_qp_b\fR 4
.IX Item "max_qp_b"
Maximum video quantizer scale for B frame.
.IP \fImin_qp_b\fR 4
.IX Item "min_qp_b"
Minimum video quantizer scale for B frame.
.IP \fIscenario\fR 4
.IX Item "scenario"
Provides a hint to encoder about the scenario for the encoding session.
.RS 4
.IP \fBunknown\fR 4
.IX Item "unknown"
.PD 0
.IP \fBdisplayremoting\fR 4
.IX Item "displayremoting"
.IP \fBvideoconference\fR 4
.IX Item "videoconference"
.IP \fBarchive\fR 4
.IX Item "archive"
.IP \fBlivestreaming\fR 4
.IX Item "livestreaming"
.IP \fBcameracapture\fR 4
.IX Item "cameracapture"
.IP \fBvideosurveillance\fR 4
.IX Item "videosurveillance"
.IP \fBgamestreaming\fR 4
.IX Item "gamestreaming"
.IP \fBremotegaming\fR 4
.IX Item "remotegaming"
.RE
.RS 4
.RE
.IP \fIavbr_accuracy\fR 4
.IX Item "avbr_accuracy"
.PD
Accuracy of the AVBR ratecontrol (unit of tenth of percent).
.IP \fIavbr_convergence\fR 4
.IX Item "avbr_convergence"
Convergence of the AVBR ratecontrol (unit of 100 frames)
.Sp
The parameters \fIavbr_accuracy\fR and \fIavbr_convergence\fR are for the
average variable bitrate control (AVBR) algorithm.
The algorithm focuses on overall encoding quality while meeting the specified
bitrate, \fItarget_bitrate\fR, within the accuracy range \fIavbr_accuracy\fR,
after a \fIavbr_Convergence\fR period. This method does not follow HRD and the
instant bitrate is not capped or padded.
.IP \fIskip_frame\fR 4
.IX Item "skip_frame"
Use per-frame metadata "qsv_skip_frame" to skip frame when encoding. This option
defines the usage of this metadata.
.RS 4
.IP \fBno_skip\fR 4
.IX Item "no_skip"
Frame skipping is disabled.
.IP \fBinsert_dummy\fR 4
.IX Item "insert_dummy"
Encoder inserts into bitstream frame where all macroblocks are encoded as
skipped.
.IP \fBinsert_nothing\fR 4
.IX Item "insert_nothing"
Similar to insert_dummy, but encoder inserts nothing into bitstream. The skipped
frames are still used in brc. For example, gop still include skipped frames, and
the frames after skipped frames will be larger in size.
.IP \fBbrc_only\fR 4
.IX Item "brc_only"
skip_frame metadata indicates the number of missed frames before the current
frame.
.RE
.RS 4
.RE
.PP
\fIMPEG2 Options\fR
.IX Subsection "MPEG2 Options"
.PP
These options are used by mpeg2_qsv
.IP \fIprofile\fR 4
.IX Item "profile"
.RS 4
.PD 0
.IP \fBunknown\fR 4
.IX Item "unknown"
.IP \fBsimple\fR 4
.IX Item "simple"
.IP \fBmain\fR 4
.IX Item "main"
.IP \fBhigh\fR 4
.IX Item "high"
.RE
.RS 4
.RE
.PD
.PP
\fIVP9 Options\fR
.IX Subsection "VP9 Options"
.PP
These options are used by vp9_qsv
.IP \fIprofile\fR 4
.IX Item "profile"
.RS 4
.PD 0
.IP \fBunknown\fR 4
.IX Item "unknown"
.IP \fBprofile0\fR 4
.IX Item "profile0"
.IP \fBprofile1\fR 4
.IX Item "profile1"
.IP \fBprofile2\fR 4
.IX Item "profile2"
.IP \fBprofile3\fR 4
.IX Item "profile3"
.RE
.RS 4
.RE
.IP \fItile_cols\fR 4
.IX Item "tile_cols"
.PD
Number of columns for tiled encoding (requires libmfx >= 1.29).
.IP \fItile_rows\fR 4
.IX Item "tile_rows"
Number of rows for tiled encoding (requires libmfx  >= 1.29).
.PP
\fIAV1 Options\fR
.IX Subsection "AV1 Options"
.PP
These options are used by av1_qsv (requires libvpl).
.IP \fIprofile\fR 4
.IX Item "profile"
.RS 4
.PD 0
.IP \fBunknown\fR 4
.IX Item "unknown"
.IP \fBmain\fR 4
.IX Item "main"
.RE
.RS 4
.RE
.IP \fItile_cols\fR 4
.IX Item "tile_cols"
.PD
Number of columns for tiled encoding.
.IP \fItile_rows\fR 4
.IX Item "tile_rows"
Number of rows for tiled encoding.
.IP \fIadaptive_i\fR 4
.IX Item "adaptive_i"
This flag controls insertion of I frames by the QSV encoder. Turn ON this flag
to allow changing of frame type from P and B to I.
.IP \fIadaptive_b\fR 4
.IX Item "adaptive_b"
This flag controls changing of frame type from B to P.
.IP \fIb_strategy\fR 4
.IX Item "b_strategy"
This option controls usage of B frames as reference.
.IP \fIextbrc\fR 4
.IX Item "extbrc"
Extended bitrate control.
.IP \fIlook_ahead_depth\fR 4
.IX Item "look_ahead_depth"
Depth of look ahead in number frames, available when extbrc option is enabled.
.IP \fIlow_delay_brc\fR 4
.IX Item "low_delay_brc"
Setting this flag turns on or off LowDelayBRC feature in qsv plugin, which provides
more accurate bitrate control to minimize the variance of bitstream size frame
by frame. Value: \-1\-default 0\-off 1\-on
.IP \fImax_frame_size\fR 4
.IX Item "max_frame_size"
Set the allowed max size in bytes for each frame. If the frame size exceeds
the limitation, encoder will adjust the QP value to control the frame size.
Invalid in CQP rate control mode.
.IP \fImax_frame_size_i\fR 4
.IX Item "max_frame_size_i"
Maximum encoded frame size for I frames in bytes. If this value is set as larger
than zero, then for I frames the value set by max_frame_size is ignored.
.IP \fImax_frame_size_p\fR 4
.IX Item "max_frame_size_p"
Maximum encoded frame size for P frames in bytes. If this value is set as larger
than zero, then for P frames the value set by max_frame_size is ignored.
.SS snow
.IX Subsection "snow"
\fIOptions\fR
.IX Subsection "Options"
.IP \fBiterative_dia_size\fR 4
.IX Item "iterative_dia_size"
dia size for the iterative motion estimation
.SS "VAAPI encoders"
.IX Subsection "VAAPI encoders"
Wrappers for hardware encoders accessible via VAAPI.
.PP
These encoders only accept input in VAAPI hardware surfaces.  If you have input
in software frames, use the \fBhwupload\fR filter to upload them to the GPU.
.PP
The following standard libavcodec options are used:
.IP \(bu 4
\&\fBg\fR / \fBgop_size\fR
.IP \(bu 4
\&\fBbf\fR / \fBmax_b_frames\fR
.IP \(bu 4
\&\fBprofile\fR
.Sp
If not set, this will be determined automatically from the format of the input
frames and the profiles supported by the driver.
.IP \(bu 4
\&\fBlevel\fR
.IP \(bu 4
\&\fBb\fR / \fBbit_rate\fR
.IP \(bu 4
\&\fBmaxrate\fR / \fBrc_max_rate\fR
.IP \(bu 4
\&\fBbufsize\fR / \fBrc_buffer_size\fR
.IP \(bu 4
\&\fBrc_init_occupancy\fR / \fBrc_initial_buffer_occupancy\fR
.IP \(bu 4
\&\fBcompression_level\fR
.Sp
Speed / quality tradeoff: higher values are faster / worse quality.
.IP \(bu 4
\&\fBq\fR / \fBglobal_quality\fR
.Sp
Size / quality tradeoff: higher values are smaller / worse quality.
.IP \(bu 4
\&\fBqmin\fR
.IP \(bu 4
\&\fBqmax\fR
.IP \(bu 4
\&\fBi_qfactor\fR / \fBi_quant_factor\fR
.IP \(bu 4
\&\fBi_qoffset\fR / \fBi_quant_offset\fR
.IP \(bu 4
\&\fBb_qfactor\fR / \fBb_quant_factor\fR
.IP \(bu 4
\&\fBb_qoffset\fR / \fBb_quant_offset\fR
.IP \(bu 4
\&\fBslices\fR
.PP
All encoders support the following options:
.IP \fBlow_power\fR 4
.IX Item "low_power"
Some drivers/platforms offer a second encoder for some codecs intended to use
less power than the default encoder; setting this option will attempt to use
that encoder.  Note that it may support a reduced feature set, so some other
options may not be available in this mode.
.IP \fBidr_interval\fR 4
.IX Item "idr_interval"
Set the number of normal intra frames between full-refresh (IDR) frames in
open-GOP mode.  The intra frames are still IRAPs, but will not include global
headers and may have non-decodable leading pictures.
.IP \fBb_depth\fR 4
.IX Item "b_depth"
Set the B\-frame reference depth.  When set to one (the default), all B\-frames
will refer only to P\- or I\-frames.  When set to greater values multiple layers
of B\-frames will be present, frames in each layer only referring to frames in
higher layers.
.IP \fBasync_depth\fR 4
.IX Item "async_depth"
Maximum processing parallelism. Increase this to improve single channel
performance. This option doesn't work if driver doesn't implement vaSyncBuffer
function. Please make sure there are enough hw_frames allocated if a large
number of async_depth is used.
.IP \fBmax_frame_size\fR 4
.IX Item "max_frame_size"
Set the allowed max size in bytes for each frame. If the frame size exceeds
the limitation, encoder will adjust the QP value to control the frame size.
Invalid in CQP rate control mode.
.IP \fBrc_mode\fR 4
.IX Item "rc_mode"
Set the rate control mode to use.  A given driver may only support a subset of
modes.
.Sp
Possible modes:
.RS 4
.IP \fBauto\fR 4
.IX Item "auto"
Choose the mode automatically based on driver support and the other options.
This is the default.
.IP \fBCQP\fR 4
.IX Item "CQP"
Constant-quality.
.IP \fBCBR\fR 4
.IX Item "CBR"
Constant-bitrate.
.IP \fBVBR\fR 4
.IX Item "VBR"
Variable-bitrate.
.IP \fBICQ\fR 4
.IX Item "ICQ"
Intelligent constant-quality.
.IP \fBQVBR\fR 4
.IX Item "QVBR"
Quality-defined variable-bitrate.
.IP \fBAVBR\fR 4
.IX Item "AVBR"
Average variable bitrate.
.RE
.RS 4
.RE
.IP \fBblbrc\fR 4
.IX Item "blbrc"
Enable block level rate control, which assigns different bitrate block by block.
Invalid for CQP mode.
.PP
Each encoder also has its own specific options:
.IP \fBav1_vaapi\fR 4
.IX Item "av1_vaapi"
\&\fBprofile\fR sets the value of \fIseq_profile\fR.
\&\fBtier\fR sets the value of \fIseq_tier\fR.
\&\fBlevel\fR sets the value of \fIseq_level_idx\fR.
.RS 4
.IP \fBtiles\fR 4
.IX Item "tiles"
Set the number of tiles to encode the input video with, as columns x rows.
(default is auto, which means use minimal tile column/row number).
.IP \fBtile_groups\fR 4
.IX Item "tile_groups"
Set tile groups number. All the tiles will be distributed as evenly as possible to
each tile group. (default is 1).
.RE
.RS 4
.RE
.IP \fBh264_vaapi\fR 4
.IX Item "h264_vaapi"
\&\fBprofile\fR sets the value of \fIprofile_idc\fR and the \fIconstraint_set*_flag\fRs.
\&\fBlevel\fR sets the value of \fIlevel_idc\fR.
.RS 4
.IP \fBcoder\fR 4
.IX Item "coder"
Set entropy encoder (default is \fIcabac\fR).  Possible values:
.RS 4
.IP \fBac\fR 4
.IX Item "ac"
.PD 0
.IP \fBcabac\fR 4
.IX Item "cabac"
.PD
Use CABAC.
.IP \fBvlc\fR 4
.IX Item "vlc"
.PD 0
.IP \fBcavlc\fR 4
.IX Item "cavlc"
.PD
Use CAVLC.
.RE
.RS 4
.RE
.IP \fBaud\fR 4
.IX Item "aud"
Include access unit delimiters in the stream (not included by default).
.IP \fBsei\fR 4
.IX Item "sei"
Set SEI message types to include.
Some combination of the following values:
.RS 4
.IP \fBidentifier\fR 4
.IX Item "identifier"
Include a \fIuser_data_unregistered\fR message containing information about
the encoder.
.IP \fBtiming\fR 4
.IX Item "timing"
Include picture timing parameters (\fIbuffering_period\fR and
\&\fIpic_timing\fR messages).
.IP \fBrecovery_point\fR 4
.IX Item "recovery_point"
Include recovery points where appropriate (\fIrecovery_point\fR messages).
.RE
.RS 4
.RE
.RE
.RS 4
.RE
.IP \fBhevc_vaapi\fR 4
.IX Item "hevc_vaapi"
\&\fBprofile\fR and \fBlevel\fR set the values of
\&\fIgeneral_profile_idc\fR and \fIgeneral_level_idc\fR respectively.
.RS 4
.IP \fBaud\fR 4
.IX Item "aud"
Include access unit delimiters in the stream (not included by default).
.IP \fBtier\fR 4
.IX Item "tier"
Set \fIgeneral_tier_flag\fR.  This may affect the level chosen for the stream
if it is not explicitly specified.
.IP \fBsei\fR 4
.IX Item "sei"
Set SEI message types to include.
Some combination of the following values:
.RS 4
.IP \fBhdr\fR 4
.IX Item "hdr"
Include HDR metadata if the input frames have it
(\fImastering_display_colour_volume\fR and \fIcontent_light_level\fR
messages).
.RE
.RS 4
.RE
.IP \fBtiles\fR 4
.IX Item "tiles"
Set the number of tiles to encode the input video with, as columns x rows.
Larger numbers allow greater parallelism in both encoding and decoding, but
may decrease coding efficiency.
.RE
.RS 4
.RE
.IP \fBmjpeg_vaapi\fR 4
.IX Item "mjpeg_vaapi"
Only baseline DCT encoding is supported.  The encoder always uses the standard
quantisation and huffman tables \- \fBglobal_quality\fR scales the standard
quantisation table (range 1\-100).
.Sp
For YUV, 4:2:0, 4:2:2 and 4:4:4 subsampling modes are supported.  RGB is also
supported, and will create an RGB JPEG.
.RS 4
.IP \fBjfif\fR 4
.IX Item "jfif"
Include JFIF header in each frame (not included by default).
.IP \fBhuffman\fR 4
.IX Item "huffman"
Include standard huffman tables (on by default).  Turning this off will save
a few hundred bytes in each output frame, but may lose compatibility with some
JPEG decoders which don't fully handle MJPEG.
.RE
.RS 4
.RE
.IP \fBmpeg2_vaapi\fR 4
.IX Item "mpeg2_vaapi"
\&\fBprofile\fR and \fBlevel\fR set the value of \fIprofile_and_level_indication\fR.
.IP \fBvp8_vaapi\fR 4
.IX Item "vp8_vaapi"
B\-frames are not supported.
.Sp
\&\fBglobal_quality\fR sets the \fIq_idx\fR used for non-key frames (range 0\-127).
.RS 4
.IP \fBloop_filter_level\fR 4
.IX Item "loop_filter_level"
.PD 0
.IP \fBloop_filter_sharpness\fR 4
.IX Item "loop_filter_sharpness"
.PD
Manually set the loop filter parameters.
.RE
.RS 4
.RE
.IP \fBvp9_vaapi\fR 4
.IX Item "vp9_vaapi"
\&\fBglobal_quality\fR sets the \fIq_idx\fR used for P\-frames (range 0\-255).
.RS 4
.IP \fBloop_filter_level\fR 4
.IX Item "loop_filter_level"
.PD 0
.IP \fBloop_filter_sharpness\fR 4
.IX Item "loop_filter_sharpness"
.PD
Manually set the loop filter parameters.
.RE
.RS 4
.Sp
B\-frames are supported, but the output stream is always in encode order rather than display
order.  If B\-frames are enabled, it may be necessary to use the \fBvp9_raw_reorder\fR
bitstream filter to modify the output stream to display frames in the correct order.
.Sp
Only normal frames are produced \- the \fBvp9_superframe\fR bitstream filter may be
required to produce a stream usable with all decoders.
.RE
.SS vbn
.IX Subsection "vbn"
Vizrt Binary Image encoder.
.PP
This format is used by the broadcast vendor Vizrt for quick texture streaming.
Advanced features of the format such as LZW compression of texture data or
generation of mipmaps are not supported.
.PP
\fIOptions\fR
.IX Subsection "Options"
.IP "\fBformat\fR \fIstring\fR" 4
.IX Item "format string"
Sets the texture compression used by the VBN file. Can be \fIdxt1\fR,
\&\fIdxt5\fR or \fIraw\fR. Default is \fIdxt5\fR.
.SS vc2
.IX Subsection "vc2"
SMPTE VC\-2 (previously BBC Dirac Pro). This codec was primarily aimed at
professional broadcasting but since it supports yuv420, yuv422 and yuv444 at
8 (limited range or full range), 10 or 12 bits, this makes it suitable for
other tasks which require low overhead and low compression (like screen
recording).
.PP
\fIOptions\fR
.IX Subsection "Options"
.IP \fBb\fR 4
.IX Item "b"
Sets target video bitrate. Usually that's around 1:6 of the uncompressed
video bitrate (e.g. for 1920x1080 50fps yuv422p10 that's around 400Mbps). Higher
values (close to the uncompressed bitrate) turn on lossless compression mode.
.IP \fBfield_order\fR 4
.IX Item "field_order"
Enables field coding when set (e.g. to tt \- top field first) for interlaced
inputs. Should increase compression with interlaced content as it splits the
fields and encodes each separately.
.IP \fBwavelet_depth\fR 4
.IX Item "wavelet_depth"
Sets the total amount of wavelet transforms to apply, between 1 and 5 (default).
Lower values reduce compression and quality. Less capable decoders may not be
able to handle values of \fBwavelet_depth\fR over 3.
.IP \fBwavelet_type\fR 4
.IX Item "wavelet_type"
Sets the transform type. Currently only \fI5_3\fR (LeGall) and \fI9_7\fR
(Deslauriers-Dubuc)
are implemented, with 9_7 being the one with better compression and thus
is the default.
.IP \fBslice_width\fR 4
.IX Item "slice_width"
.PD 0
.IP \fBslice_height\fR 4
.IX Item "slice_height"
.PD
Sets the slice size for each slice. Larger values result in better compression.
For compatibility with other more limited decoders use \fBslice_width\fR of
32 and \fBslice_height\fR of 8.
.IP \fBtolerance\fR 4
.IX Item "tolerance"
Sets the undershoot tolerance of the rate control system in percent. This is
to prevent an expensive search from being run.
.IP \fBqm\fR 4
.IX Item "qm"
Sets the quantization matrix preset to use by default or when \fBwavelet_depth\fR
is set to 5
.RS 4
.IP \- 4
\&\fIdefault\fR
Uses the default quantization matrix from the specifications, extended with
values for the fifth level. This provides a good balance between keeping detail
and omitting artifacts.
.IP \- 4
\&\fIflat\fR
Use a completely zeroed out quantization matrix. This increases PSNR but might
reduce perception. Use in bogus benchmarks.
.IP \- 4
\&\fIcolor\fR
Reduces detail but attempts to preserve color at extremely low bitrates.
.RE
.RS 4
.RE
.SH "SUBTITLES ENCODERS"
.IX Header "SUBTITLES ENCODERS"
.SS dvdsub
.IX Subsection "dvdsub"
This codec encodes the bitmap subtitle format that is used in DVDs.
Typically they are stored in VOBSUB file pairs (*.idx + *.sub),
and they can also be used in Matroska files.
.PP
\fIOptions\fR
.IX Subsection "Options"
.IP \fBpalette\fR 4
.IX Item "palette"
Specify the global palette used by the bitmaps.
.Sp
The format for this option is a string containing 16 24\-bits hexadecimal
numbers (without 0x prefix) separated by commas, for example \f(CW\*(C`0d00ee,
ee450d, 101010, eaeaea, 0ce60b, ec14ed, ebff0b, 0d617a, 7b7b7b, d1d1d1,
7b2a0e, 0d950c, 0f007b, cf0dec, cfa80c, 7c127b\*(C'\fR.
.IP \fBeven_rows_fix\fR 4
.IX Item "even_rows_fix"
When set to 1, enable a work-around that makes the number of pixel rows
even in all subtitles.  This fixes a problem with some players that
cut off the bottom row if the number is odd.  The work-around just adds
a fully transparent row if needed.  The overhead is low, typically
one byte per subtitle on average.
.Sp
By default, this work-around is disabled.
.SS lrc
.IX Subsection "lrc"
This codec encodes the LRC lyrics format.
.PP
\fIOptions\fR
.IX Subsection "Options"
.IP \fBprecision\fR 4
.IX Item "precision"
Specify the precision of the fractional part of the timestamp. Time base is
determined based on this value.
.Sp
Defaults to 2 for centiseconds.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBffmpeg\fR\|(1), \fBffplay\fR\|(1), \fBffprobe\fR\|(1), \fBlibavcodec\fR\|(3)
.SH AUTHORS
.IX Header "AUTHORS"
The FFmpeg developers.
.PP
For details about the authorship, see the Git history of the project
(https://git.ffmpeg.org/ffmpeg), e.g. by typing the command
\&\fBgit log\fR in the FFmpeg source directory, or browsing the
online repository at <\fBhttps://git.ffmpeg.org/ffmpeg\fR>.
.PP
Maintainers for the specific components are listed in the file
\&\fIMAINTAINERS\fR in the source code tree.
