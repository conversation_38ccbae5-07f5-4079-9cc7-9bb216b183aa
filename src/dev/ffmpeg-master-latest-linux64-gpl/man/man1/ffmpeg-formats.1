.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "FFMPEG-FORMATS 1"
.TH FFMPEG-FORMATS 1 " " " " " "
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
ffmpeg\-formats \- FFmpeg formats
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This document describes the supported formats (muxers and demuxers)
provided by the libavformat library.
.SH "FORMAT OPTIONS"
.IX Header "FORMAT OPTIONS"
The libavformat library provides some generic global options, which
can be set on all the muxers and demuxers. In addition each muxer or
demuxer may support so-called private options, which are specific for
that component.
.PP
Options may be set by specifying \-\fIoption\fR \fIvalue\fR in the
FFmpeg tools, or by setting the value explicitly in the
\&\f(CW\*(C`AVFormatContext\*(C'\fR options or using the \fIlibavutil/opt.h\fR API
for programmatic use.
.PP
The list of supported options follows:
.IP "\fBavioflags\fR \fIflags\fR \fB(\fR\fIinput/output\fR\fB)\fR" 4
.IX Item "avioflags flags (input/output)"
Possible values:
.RS 4
.IP \fBdirect\fR 4
.IX Item "direct"
Reduce buffering.
.RE
.RS 4
.RE
.IP "\fBprobesize\fR \fIinteger\fR \fB(\fR\fIinput\fR\fB)\fR" 4
.IX Item "probesize integer (input)"
Set probing size in bytes, i.e. the size of the data to analyze to get
stream information. A higher value will enable detecting more
information in case it is dispersed into the stream, but will increase
latency. Must be an integer not lesser than 32. It is 5000000 by default.
.IP "\fBmax_probe_packets\fR \fIinteger\fR \fB(\fR\fIinput\fR\fB)\fR" 4
.IX Item "max_probe_packets integer (input)"
Set the maximum number of buffered packets when probing a codec.
Default is 2500 packets.
.IP "\fBpacketsize\fR \fIinteger\fR \fB(\fR\fIoutput\fR\fB)\fR" 4
.IX Item "packetsize integer (output)"
Set packet size.
.IP "\fBfflags\fR \fIflags\fR" 4
.IX Item "fflags flags"
Set format flags. Some are implemented for a limited number of formats.
.Sp
Possible values for input files:
.RS 4
.IP \fBdiscardcorrupt\fR 4
.IX Item "discardcorrupt"
Discard corrupted packets.
.IP \fBfastseek\fR 4
.IX Item "fastseek"
Enable fast, but inaccurate seeks for some formats.
.IP \fBgenpts\fR 4
.IX Item "genpts"
Generate missing PTS if DTS is present.
.IP \fBigndts\fR 4
.IX Item "igndts"
Ignore DTS if PTS is also set. In case the PTS is set, the DTS value
is set to NOPTS. This is ignored when the \f(CW\*(C`nofillin\*(C'\fR flag is set.
.IP \fBignidx\fR 4
.IX Item "ignidx"
Ignore index.
.IP \fBnobuffer\fR 4
.IX Item "nobuffer"
Reduce the latency introduced by buffering during initial input streams analysis.
.IP \fBnofillin\fR 4
.IX Item "nofillin"
Do not fill in missing values in packet fields that can be exactly calculated.
.IP \fBnoparse\fR 4
.IX Item "noparse"
Disable AVParsers, this needs \f(CW\*(C`+nofillin\*(C'\fR too.
.IP \fBsortdts\fR 4
.IX Item "sortdts"
Try to interleave output packets by DTS. At present, available only for AVIs with an index.
.RE
.RS 4
.Sp
Possible values for output files:
.IP \fBautobsf\fR 4
.IX Item "autobsf"
Automatically apply bitstream filters as required by the output format. Enabled by default.
.IP \fBbitexact\fR 4
.IX Item "bitexact"
Only write platform\-, build\- and time-independent data.
This ensures that file and data checksums are reproducible and match between
platforms. Its primary use is for regression testing.
.IP \fBflush_packets\fR 4
.IX Item "flush_packets"
Write out packets immediately.
.IP \fBshortest\fR 4
.IX Item "shortest"
Stop muxing at the end of the shortest stream.
It may be needed to increase max_interleave_delta to avoid flushing the longer
streams before EOF.
.RE
.RS 4
.RE
.IP "\fBseek2any\fR \fIinteger\fR \fB(\fR\fIinput\fR\fB)\fR" 4
.IX Item "seek2any integer (input)"
Allow seeking to non-keyframes on demuxer level when supported if set to 1.
Default is 0.
.IP "\fBanalyzeduration\fR \fIinteger\fR \fB(\fR\fIinput\fR\fB)\fR" 4
.IX Item "analyzeduration integer (input)"
Specify how many microseconds are analyzed to probe the input. A
higher value will enable detecting more accurate information, but will
increase latency. It defaults to 5,000,000 microseconds = 5 seconds.
.IP "\fBcryptokey\fR \fIhexadecimal string\fR \fB(\fR\fIinput\fR\fB)\fR" 4
.IX Item "cryptokey hexadecimal string (input)"
Set decryption key.
.IP "\fBindexmem\fR \fIinteger\fR \fB(\fR\fIinput\fR\fB)\fR" 4
.IX Item "indexmem integer (input)"
Set max memory used for timestamp index (per stream).
.IP "\fBrtbufsize\fR \fIinteger\fR \fB(\fR\fIinput\fR\fB)\fR" 4
.IX Item "rtbufsize integer (input)"
Set max memory used for buffering real-time frames.
.IP "\fBfdebug\fR \fIflags\fR \fB(\fR\fIinput/output\fR\fB)\fR" 4
.IX Item "fdebug flags (input/output)"
Print specific debug info.
.Sp
Possible values:
.RS 4
.IP \fBts\fR 4
.IX Item "ts"
.RE
.RS 4
.RE
.PD 0
.IP "\fBmax_delay\fR \fIinteger\fR \fB(\fR\fIinput/output\fR\fB)\fR" 4
.IX Item "max_delay integer (input/output)"
.PD
Set maximum muxing or demuxing delay in microseconds.
.IP "\fBfpsprobesize\fR \fIinteger\fR \fB(\fR\fIinput\fR\fB)\fR" 4
.IX Item "fpsprobesize integer (input)"
Set number of frames used to probe fps.
.IP "\fBaudio_preload\fR \fIinteger\fR \fB(\fR\fIoutput\fR\fB)\fR" 4
.IX Item "audio_preload integer (output)"
Set microseconds by which audio packets should be interleaved earlier.
.IP "\fBchunk_duration\fR \fIinteger\fR \fB(\fR\fIoutput\fR\fB)\fR" 4
.IX Item "chunk_duration integer (output)"
Set microseconds for each chunk.
.IP "\fBchunk_size\fR \fIinteger\fR \fB(\fR\fIoutput\fR\fB)\fR" 4
.IX Item "chunk_size integer (output)"
Set size in bytes for each chunk.
.IP "\fBerr_detect, f_err_detect\fR \fIflags\fR \fB(\fR\fIinput\fR\fB)\fR" 4
.IX Item "err_detect, f_err_detect flags (input)"
Set error detection flags. \f(CW\*(C`f_err_detect\*(C'\fR is deprecated and
should be used only via the \fBffmpeg\fR tool.
.Sp
Possible values:
.RS 4
.IP \fBcrccheck\fR 4
.IX Item "crccheck"
Verify embedded CRCs.
.IP \fBbitstream\fR 4
.IX Item "bitstream"
Detect bitstream specification deviations.
.IP \fBbuffer\fR 4
.IX Item "buffer"
Detect improper bitstream length.
.IP \fBexplode\fR 4
.IX Item "explode"
Abort decoding on minor error detection.
.IP \fBcareful\fR 4
.IX Item "careful"
Consider things that violate the spec and have not been seen in the
wild as errors.
.IP \fBcompliant\fR 4
.IX Item "compliant"
Consider all spec non compliancies as errors.
.IP \fBaggressive\fR 4
.IX Item "aggressive"
Consider things that a sane encoder should not do as an error.
.RE
.RS 4
.RE
.IP "\fBmax_interleave_delta\fR \fIinteger\fR \fB(\fR\fIoutput\fR\fB)\fR" 4
.IX Item "max_interleave_delta integer (output)"
Set maximum buffering duration for interleaving. The duration is
expressed in microseconds, and defaults to 10000000 (10 seconds).
.Sp
To ensure all the streams are interleaved correctly, libavformat will
wait until it has at least one packet for each stream before actually
writing any packets to the output file. When some streams are
"sparse" (i.e. there are large gaps between successive packets), this
can result in excessive buffering.
.Sp
This field specifies the maximum difference between the timestamps of the
first and the last packet in the muxing queue, above which libavformat
will output a packet regardless of whether it has queued a packet for all
the streams.
.Sp
If set to 0, libavformat will continue buffering packets until it has
a packet for each stream, regardless of the maximum timestamp
difference between the buffered packets.
.IP "\fBuse_wallclock_as_timestamps\fR \fIinteger\fR \fB(\fR\fIinput\fR\fB)\fR" 4
.IX Item "use_wallclock_as_timestamps integer (input)"
Use wallclock as timestamps if set to 1. Default is 0.
.IP "\fBavoid_negative_ts\fR \fIinteger\fR \fB(\fR\fIoutput\fR\fB)\fR" 4
.IX Item "avoid_negative_ts integer (output)"
Possible values:
.RS 4
.IP \fBmake_non_negative\fR 4
.IX Item "make_non_negative"
Shift timestamps to make them non-negative.
Also note that this affects only leading negative timestamps, and not
non-monotonic negative timestamps.
.IP \fBmake_zero\fR 4
.IX Item "make_zero"
Shift timestamps so that the first timestamp is 0.
.IP "\fBauto (default)\fR" 4
.IX Item "auto (default)"
Enables shifting when required by the target format.
.IP \fBdisabled\fR 4
.IX Item "disabled"
Disables shifting of timestamp.
.RE
.RS 4
.Sp
When shifting is enabled, all output timestamps are shifted by the
same amount. Audio, video, and subtitles desynching and relative
timestamp differences are preserved compared to how they would have
been without shifting.
.RE
.IP "\fBskip_initial_bytes\fR \fIinteger\fR \fB(\fR\fIinput\fR\fB)\fR" 4
.IX Item "skip_initial_bytes integer (input)"
Set number of bytes to skip before reading header and frames if set to 1.
Default is 0.
.IP "\fBcorrect_ts_overflow\fR \fIinteger\fR \fB(\fR\fIinput\fR\fB)\fR" 4
.IX Item "correct_ts_overflow integer (input)"
Correct single timestamp overflows if set to 1. Default is 1.
.IP "\fBflush_packets\fR \fIinteger\fR \fB(\fR\fIoutput\fR\fB)\fR" 4
.IX Item "flush_packets integer (output)"
Flush the underlying I/O stream after each packet. Default is \-1 (auto), which
means that the underlying protocol will decide, 1 enables it, and has the
effect of reducing the latency, 0 disables it and may increase IO throughput in
some cases.
.IP "\fBoutput_ts_offset\fR \fIoffset\fR \fB(\fR\fIoutput\fR\fB)\fR" 4
.IX Item "output_ts_offset offset (output)"
Set the output time offset.
.Sp
\&\fIoffset\fR must be a time duration specification,
see \fBthe Time duration section in the ffmpeg\-utils\|(1) manual\fR.
.Sp
The offset is added by the muxer to the output timestamps.
.Sp
Specifying a positive offset means that the corresponding streams are
delayed bt the time duration specified in \fIoffset\fR. Default value
is \f(CW0\fR (meaning that no offset is applied).
.IP "\fBformat_whitelist\fR \fIlist\fR \fB(\fR\fIinput\fR\fB)\fR" 4
.IX Item "format_whitelist list (input)"
"," separated list of allowed demuxers. By default all are allowed.
.IP "\fBdump_separator\fR \fIstring\fR \fB(\fR\fIinput\fR\fB)\fR" 4
.IX Item "dump_separator string (input)"
Separator used to separate the fields printed on the command line about the
Stream parameters.
For example, to separate the fields with newlines and indentation:
.Sp
.Vb 2
\&        ffprobe \-dump_separator "
\&                                  "  \-i ~/videos/matrixbench_mpeg2.mpg
.Ve
.IP "\fBmax_streams\fR \fIinteger\fR \fB(\fR\fIinput\fR\fB)\fR" 4
.IX Item "max_streams integer (input)"
Specifies the maximum number of streams. This can be used to reject files that
would require too many resources due to a large number of streams.
.IP "\fBskip_estimate_duration_from_pts\fR \fIbool\fR \fB(\fR\fIinput\fR\fB)\fR" 4
.IX Item "skip_estimate_duration_from_pts bool (input)"
Skip estimation of input duration if it requires an additional probing for PTS at end of file.
At present, applicable for MPEG-PS and MPEG-TS.
.IP "\fBduration_probesize\fR \fIinteger\fR \fB(\fR\fIinput\fR\fB)\fR" 4
.IX Item "duration_probesize integer (input)"
Set probing size, in bytes, for input duration estimation when it actually requires
an additional probing for PTS at end of file (at present: MPEG-PS and MPEG-TS).
It is aimed at users interested in better durations probing for itself, or indirectly
because using the concat demuxer, for example.
The typical use case is an MPEG-TS CBR with a high bitrate, high video buffering and
ending cleaning with similar PTS for video and audio: in such a scenario, the large
physical gap between the last video packet and the last audio packet makes it necessary
to read many bytes in order to get the video stream duration.
Another use case is where the default probing behaviour only reaches a single video frame which is
not the last one of the stream due to frame reordering, so the duration is not accurate.
Setting this option has a performance impact even for small files because the probing
size is fixed.
Default behaviour is a general purpose trade-off, largely adaptive, but the probing size
will not be extended to get streams durations at all costs.
Must be an integer not lesser than 1, or 0 for default behaviour.
.IP "\fBstrict, f_strict\fR \fIinteger\fR \fB(\fR\fIinput/output\fR\fB)\fR" 4
.IX Item "strict, f_strict integer (input/output)"
Specify how strictly to follow the standards. \f(CW\*(C`f_strict\*(C'\fR is deprecated and
should be used only via the \fBffmpeg\fR tool.
.Sp
Possible values:
.RS 4
.IP \fBvery\fR 4
.IX Item "very"
strictly conform to an older more strict version of the spec or reference software
.IP \fBstrict\fR 4
.IX Item "strict"
strictly conform to all the things in the spec no matter what consequences
.IP \fBnormal\fR 4
.IX Item "normal"
.PD 0
.IP \fBunofficial\fR 4
.IX Item "unofficial"
.PD
allow unofficial extensions
.IP \fBexperimental\fR 4
.IX Item "experimental"
allow non standardized experimental things, experimental
(unfinished/work in progress/not well tested) decoders and encoders.
Note: experimental decoders can pose a security risk, do not use this for
decoding untrusted input.
.RE
.RS 4
.RE
.SS "Format stream specifiers"
.IX Subsection "Format stream specifiers"
Format stream specifiers allow selection of one or more streams that
match specific properties.
.PP
The exact semantics of stream specifiers is defined by the
\&\f(CWavformat_match_stream_specifier()\fR function declared in the
\&\fIlibavformat/avformat.h\fR header and documented in the
\&\fBStream specifiers section in the ffmpeg\|(1) manual\fR.
.SH DEMUXERS
.IX Header "DEMUXERS"
Demuxers are configured elements in FFmpeg that can read the
multimedia streams from a particular type of file.
.PP
When you configure your FFmpeg build, all the supported demuxers
are enabled by default. You can list all available ones using the
configure option \f(CW\*(C`\-\-list\-demuxers\*(C'\fR.
.PP
You can disable all the demuxers using the configure option
\&\f(CW\*(C`\-\-disable\-demuxers\*(C'\fR, and selectively enable a single demuxer with
the option \f(CW\*(C`\-\-enable\-demuxer=\fR\f(CIDEMUXER\fR\f(CW\*(C'\fR, or disable it
with the option \f(CW\*(C`\-\-disable\-demuxer=\fR\f(CIDEMUXER\fR\f(CW\*(C'\fR.
.PP
The option \f(CW\*(C`\-demuxers\*(C'\fR of the ff* tools will display the list of
enabled demuxers. Use \f(CW\*(C`\-formats\*(C'\fR to view a combined list of
enabled demuxers and muxers.
.PP
The description of some of the currently available demuxers follows.
.SS aa
.IX Subsection "aa"
Audible Format 2, 3, and 4 demuxer.
.PP
This demuxer is used to demux Audible Format 2, 3, and 4 (.aa) files.
.SS aac
.IX Subsection "aac"
Raw Audio Data Transport Stream AAC demuxer.
.PP
This demuxer is used to demux an ADTS input containing a single AAC stream
alongwith any ID3v1/2 or APE tags in it.
.SS apng
.IX Subsection "apng"
Animated Portable Network Graphics demuxer.
.PP
This demuxer is used to demux APNG files.
All headers, but the PNG signature, up to (but not including) the first
fcTL chunk are transmitted as extradata.
Frames are then split as being all the chunks between two fcTL ones, or
between the last fcTL and IEND chunks.
.IP "\fB\-ignore_loop\fR \fIbool\fR" 4
.IX Item "-ignore_loop bool"
Ignore the loop variable in the file if set. Default is enabled.
.IP "\fB\-max_fps\fR \fIint\fR" 4
.IX Item "-max_fps int"
Maximum framerate in frames per second. Default of 0 imposes no limit.
.IP "\fB\-default_fps\fR \fIint\fR" 4
.IX Item "-default_fps int"
Default framerate in frames per second when none is specified in the file
(0 meaning as fast as possible). Default is 15.
.SS asf
.IX Subsection "asf"
Advanced Systems Format demuxer.
.PP
This demuxer is used to demux ASF files and MMS network streams.
.IP "\fB\-no_resync_search\fR \fIbool\fR" 4
.IX Item "-no_resync_search bool"
Do not try to resynchronize by looking for a certain optional start code.
.SS concat
.IX Subsection "concat"
Virtual concatenation script demuxer.
.PP
This demuxer reads a list of files and other directives from a text file and
demuxes them one after the other, as if all their packets had been muxed
together.
.PP
The timestamps in the files are adjusted so that the first file starts at 0
and each next file starts where the previous one finishes. Note that it is
done globally and may cause gaps if all streams do not have exactly the same
length.
.PP
All files must have the same streams (same codecs, same time base, etc.).
.PP
The duration of each file is used to adjust the timestamps of the next file:
if the duration is incorrect (because it was computed using the bit-rate or
because the file is truncated, for example), it can cause artifacts. The
\&\f(CW\*(C`duration\*(C'\fR directive can be used to override the duration stored in
each file.
.PP
\fISyntax\fR
.IX Subsection "Syntax"
.PP
The script is a text file in extended-ASCII, with one directive per line.
Empty lines, leading spaces and lines starting with '#' are ignored. The
following directive is recognized:
.ie n .IP "\fR\fB""file path""\fR\fB\fR" 4
.el .IP "\fR\f(CBfile path\fR\fB\fR" 4
.IX Item "file path"
Path to a file to read; special characters and spaces must be escaped with
backslash or single quotes.
.Sp
All subsequent file-related directives apply to that file.
.ie n .IP "\fR\fB""ffconcat version 1.0""\fR\fB\fR" 4
.el .IP "\fR\f(CBffconcat version 1.0\fR\fB\fR" 4
.IX Item "ffconcat version 1.0"
Identify the script type and version.
.Sp
To make FFmpeg recognize the format automatically, this directive must
appear exactly as is (no extra space or byte-order-mark) on the very first
line of the script.
.ie n .IP "\fR\fB""duration dur""\fR\fB\fR" 4
.el .IP "\fR\f(CBduration dur\fR\fB\fR" 4
.IX Item "duration dur"
Duration of the file. This information can be specified from the file;
specifying it here may be more efficient or help if the information from the
file is not available or accurate.
.Sp
If the duration is set for all files, then it is possible to seek in the
whole concatenated video.
.ie n .IP "\fR\fB""inpoint timestamp""\fR\fB\fR" 4
.el .IP "\fR\f(CBinpoint timestamp\fR\fB\fR" 4
.IX Item "inpoint timestamp"
In point of the file. When the demuxer opens the file it instantly seeks to the
specified timestamp. Seeking is done so that all streams can be presented
successfully at In point.
.Sp
This directive works best with intra frame codecs, because for non-intra frame
ones you will usually get extra packets before the actual In point and the
decoded content will most likely contain frames before In point too.
.Sp
For each file, packets before the file In point will have timestamps less than
the calculated start timestamp of the file (negative in case of the first
file), and the duration of the files (if not specified by the \f(CW\*(C`duration\*(C'\fR
directive) will be reduced based on their specified In point.
.Sp
Because of potential packets before the specified In point, packet timestamps
may overlap between two concatenated files.
.ie n .IP "\fR\fB""outpoint timestamp""\fR\fB\fR" 4
.el .IP "\fR\f(CBoutpoint timestamp\fR\fB\fR" 4
.IX Item "outpoint timestamp"
Out point of the file. When the demuxer reaches the specified decoding
timestamp in any of the streams, it handles it as an end of file condition and
skips the current and all the remaining packets from all streams.
.Sp
Out point is exclusive, which means that the demuxer will not output packets
with a decoding timestamp greater or equal to Out point.
.Sp
This directive works best with intra frame codecs and formats where all streams
are tightly interleaved. For non-intra frame codecs you will usually get
additional packets with presentation timestamp after Out point therefore the
decoded content will most likely contain frames after Out point too. If your
streams are not tightly interleaved you may not get all the packets from all
streams before Out point and you may only will be able to decode the earliest
stream until Out point.
.Sp
The duration of the files (if not specified by the \f(CW\*(C`duration\*(C'\fR
directive) will be reduced based on their specified Out point.
.ie n .IP "\fR\fB""file_packet_metadata key=value""\fR\fB\fR" 4
.el .IP "\fR\f(CBfile_packet_metadata key=value\fR\fB\fR" 4
.IX Item "file_packet_metadata key=value"
Metadata of the packets of the file. The specified metadata will be set for
each file packet. You can specify this directive multiple times to add multiple
metadata entries.
This directive is deprecated, use \f(CW\*(C`file_packet_meta\*(C'\fR instead.
.ie n .IP "\fR\fB""file_packet_meta key value""\fR\fB\fR" 4
.el .IP "\fR\f(CBfile_packet_meta key value\fR\fB\fR" 4
.IX Item "file_packet_meta key value"
Metadata of the packets of the file. The specified metadata will be set for
each file packet. You can specify this directive multiple times to add multiple
metadata entries.
.ie n .IP "\fR\fB""option key value""\fR\fB\fR" 4
.el .IP "\fR\f(CBoption key value\fR\fB\fR" 4
.IX Item "option key value"
Option to access, open and probe the file.
Can be present multiple times.
.ie n .IP "\fR\fB""stream""\fR\fB\fR" 4
.el .IP \fR\f(CBstream\fR\fB\fR 4
.IX Item "stream"
Introduce a stream in the virtual file.
All subsequent stream-related directives apply to the last introduced
stream.
Some streams properties must be set in order to allow identifying the
matching streams in the subfiles.
If no streams are defined in the script, the streams from the first file are
copied.
.ie n .IP "\fR\fB""exact_stream_id id""\fR\fB\fR" 4
.el .IP "\fR\f(CBexact_stream_id id\fR\fB\fR" 4
.IX Item "exact_stream_id id"
Set the id of the stream.
If this directive is given, the string with the corresponding id in the
subfiles will be used.
This is especially useful for MPEG-PS (VOB) files, where the order of the
streams is not reliable.
.ie n .IP "\fR\fB""stream_meta key value""\fR\fB\fR" 4
.el .IP "\fR\f(CBstream_meta key value\fR\fB\fR" 4
.IX Item "stream_meta key value"
Metadata for the stream.
Can be present multiple times.
.ie n .IP "\fR\fB""stream_codec value""\fR\fB\fR" 4
.el .IP "\fR\f(CBstream_codec value\fR\fB\fR" 4
.IX Item "stream_codec value"
Codec for the stream.
.ie n .IP "\fR\fB""stream_extradata hex_string""\fR\fB\fR" 4
.el .IP "\fR\f(CBstream_extradata hex_string\fR\fB\fR" 4
.IX Item "stream_extradata hex_string"
Extradata for the string, encoded in hexadecimal.
.ie n .IP "\fR\fB""chapter id start end""\fR\fB\fR" 4
.el .IP "\fR\f(CBchapter id start end\fR\fB\fR" 4
.IX Item "chapter id start end"
Add a chapter. \fIid\fR is an unique identifier, possibly small and
consecutive.
.PP
\fIOptions\fR
.IX Subsection "Options"
.PP
This demuxer accepts the following option:
.IP \fBsafe\fR 4
.IX Item "safe"
If set to 1, reject unsafe file paths and directives.
A file path is considered safe if it
does not contain a protocol specification and is relative and all components
only contain characters from the portable character set (letters, digits,
period, underscore and hyphen) and have no period at the beginning of a
component.
.Sp
If set to 0, any file name is accepted.
.Sp
The default is 1.
.IP \fBauto_convert\fR 4
.IX Item "auto_convert"
If set to 1, try to perform automatic conversions on packet data to make the
streams concatenable.
The default is 1.
.Sp
Currently, the only conversion is adding the h264_mp4toannexb bitstream
filter to H.264 streams in MP4 format. This is necessary in particular if
there are resolution changes.
.IP \fBsegment_time_metadata\fR 4
.IX Item "segment_time_metadata"
If set to 1, every packet will contain the \fIlavf.concat.start_time\fR and the
\&\fIlavf.concat.duration\fR packet metadata values which are the start_time and
the duration of the respective file segments in the concatenated output
expressed in microseconds. The duration metadata is only set if it is known
based on the concat file.
The default is 0.
.PP
\fIExamples\fR
.IX Subsection "Examples"
.IP \(bu 4
Use absolute filenames and include some comments:
.Sp
.Vb 6
\&        # my first filename
\&        file /mnt/share/file\-1.wav
\&        # my second filename including whitespace
\&        file \*(Aq/mnt/share/file 2.wav\*(Aq
\&        # my third filename including whitespace plus single quote
\&        file \*(Aq/mnt/share/file 3\*(Aq\e\*(Aq\*(Aq.wav\*(Aq
.Ve
.IP \(bu 4
Allow for input format auto-probing, use safe filenames and set the duration of
the first file:
.Sp
.Vb 1
\&        ffconcat version 1.0
\&        
\&        file file\-1.wav
\&        duration 20.0
\&        
\&        file subdir/file\-2.wav
.Ve
.SS dash
.IX Subsection "dash"
Dynamic Adaptive Streaming over HTTP demuxer.
.PP
This demuxer presents all AVStreams found in the manifest.
By setting the discard flags on AVStreams the caller can decide
which streams to actually receive.
Each stream mirrors the \f(CW\*(C`id\*(C'\fR and \f(CW\*(C`bandwidth\*(C'\fR properties from the
\&\f(CW\*(C`<Representation>\*(C'\fR as metadata keys named "id" and "variant_bitrate" respectively.
.PP
\fIOptions\fR
.IX Subsection "Options"
.PP
This demuxer accepts the following option:
.IP \fBcenc_decryption_key\fR 4
.IX Item "cenc_decryption_key"
16\-byte key, in hex, to decrypt files encrypted using ISO Common Encryption (CENC/AES\-128 CTR; ISO/IEC 23001\-7).
.SS dvdvideo
.IX Subsection "dvdvideo"
DVD-Video demuxer, powered by libdvdnav and libdvdread.
.PP
Can directly ingest DVD titles, specifically sequential PGCs, into
a conversion pipeline. Menu assets, such as background video or audio,
can also be demuxed given the menu's coordinates (at best effort).
.PP
Block devices (DVD drives), ISO files, and directory structures are accepted.
Activate with \f(CW\*(C`\-f dvdvideo\*(C'\fR in front of one of these inputs.
.PP
This demuxer does NOT have decryption code of any kind. You are on your own
working with encrypted DVDs, and should not expect support on the matter.
.PP
Underlying playback is handled by libdvdnav, and structure parsing by libdvdread.
FFmpeg must be built with GPL library support available as well as the
configure switches \f(CW\*(C`\-\-enable\-libdvdnav\*(C'\fR and \f(CW\*(C`\-\-enable\-libdvdread\*(C'\fR.
.PP
You will need to provide either the desired "title number" or exact PGC/PG coordinates.
Many open-source DVD players and tools can aid in providing this information.
If not specified, the demuxer will default to title 1 which works for many discs.
However, due to the flexibility of the format, it is recommended to check manually.
There are many discs that are authored strangely or with invalid headers.
.PP
If the input is a real DVD drive, please note that there are some drives which may
silently fail on reading bad sectors from the disc, returning random bits instead
which is effectively corrupt data. This is especially prominent on aging or rotting discs.
A second pass and integrity checks would be needed to detect the corruption.
This is not an FFmpeg issue.
.PP
\fIBackground\fR
.IX Subsection "Background"
.PP
DVD-Video is not a directly accessible, linear container format in the
traditional sense. Instead, it allows for complex and programmatic playback of
carefully muxed MPEG-PS streams that are stored in headerless VOB files.
To the end-user, these streams are known simply as "titles", but the actual
logical playback sequence is defined by one or more "PGCs", or Program Group Chains,
within the title. The PGC is in turn comprised of multiple "PGs", or Programs",
which are the actual video segments (and for a typical video feature, sequentially
ordered). The PGC structure, along with stream layout and metadata, are stored in
IFO files that need to be parsed. PGCs can be thought of as playlists in easier terms.
.PP
An actual DVD player relies on user GUI interaction via menus and an internal VM
to drive the direction of demuxing. Generally, the user would either navigate (via menus)
or automatically be redirected to the PGC of their choice. During this process and
the subsequent playback, the DVD player's internal VM also maintains a state and
executes instructions that can create jumps to different sectors during playback.
This is why libdvdnav is involved, as a linear read of the MPEG-PS blobs on the
disc (VOBs) is not enough to produce the right sequence in many cases.
.PP
There are many other DVD structures (a long subject) that will not be discussed here.
NAV packets, in particular, are handled by this demuxer to build accurate timing
but not emitted as a stream. For a good high-level understanding, refer to:
<\fBhttps://code.videolan.org/videolan/libdvdnav/\-/blob/master/doc/dvd_structures\fR>
.PP
\fIOptions\fR
.IX Subsection "Options"
.PP
This demuxer accepts the following options:
.IP "\fBtitle\fR \fIint\fR" 4
.IX Item "title int"
The title number to play. Must be set if \fBpgc\fR and \fBpg\fR are not set.
Not applicable to menus.
Default is 0 (auto), which currently only selects the first available title (title 1)
and notifies the user about the implications.
.IP "\fBchapter_start\fR \fIint\fR" 4
.IX Item "chapter_start int"
The chapter, or PTT (part-of-title), number to start at. Not applicable to menus.
Default is 1.
.IP "\fBchapter_end\fR \fIint\fR" 4
.IX Item "chapter_end int"
The chapter, or PTT (part-of-title), number to end at. Not applicable to menus.
Default is 0, which is a special value to signal end at the last possible chapter.
.IP "\fBangle\fR \fIint\fR" 4
.IX Item "angle int"
The video angle number, referring to what is essentially an additional
video stream that is composed from alternate frames interleaved in the VOBs.
Not applicable to menus.
Default is 1.
.IP "\fBregion\fR \fIint\fR" 4
.IX Item "region int"
The region code to use for playback. Some discs may use this to default playback
at a particular angle in different regions. This option will not affect the region code
of a real DVD drive, if used as an input. Not applicable to menus.
Default is 0, "world".
.IP "\fBmenu\fR \fIbool\fR" 4
.IX Item "menu bool"
Demux menu assets instead of navigating a title. Requires exact coordinates
of the menu (\fBmenu_lu\fR, \fBmenu_vts\fR, \fBpgc\fR, \fBpg\fR).
Default is false.
.IP "\fBmenu_lu\fR \fIint\fR" 4
.IX Item "menu_lu int"
The menu language to demux. In DVD, menus are grouped by language.
Default is 1, the first language unit.
.IP "\fBmenu_vts\fR \fIint\fR" 4
.IX Item "menu_vts int"
The VTS where the menu lives, or 0 if it is a VMG menu (root-level).
Default is 1, menu of the first VTS.
.IP "\fBpgc\fR \fIint\fR" 4
.IX Item "pgc int"
The entry PGC to start playback, in conjunction with \fBpg\fR.
Alternative to setting \fBtitle\fR.
Chapter markers are not supported at this time.
Must be explicitly set for menus.
Default is 0, automatically resolve from value of \fBtitle\fR.
.IP "\fBpg\fR \fIint\fR" 4
.IX Item "pg int"
The entry PG to start playback, in conjunction with \fBpgc\fR.
Alternative to setting \fBtitle\fR.
Chapter markers are not supported at this time.
Default is 1, the first PG of the PGC.
.IP "\fBpreindex\fR \fIbool\fR" 4
.IX Item "preindex bool"
Enable this to have accurate chapter (PTT) markers and duration measurement,
which requires a slow second pass read in order to index the chapter marker
timestamps from NAV packets. This is non-ideal extra work for real optical drives.
It is recommended and faster to use this option with a backup of the DVD structure
stored on a hard drive. Not compatible with \fBpgc\fR and \fBpg\fR.
Default is 0, false.
.IP "\fBtrim\fR \fIbool\fR" 4
.IX Item "trim bool"
Skip padding cells (i.e. cells shorter than 1 second) from the beginning.
There exist many discs with filler segments at the beginning of the PGC,
often with junk data intended for controlling a real DVD player's
buffering speed and with no other material data value.
Not applicable to menus.
Default is 1, true.
.PP
\fIExamples\fR
.IX Subsection "Examples"
.IP \(bu 4
Open title 3 from a given DVD structure:
.Sp
.Vb 1
\&        ffmpeg \-f dvdvideo \-title 3 \-i <path to DVD> ...
.Ve
.IP \(bu 4
Open chapters 3\-6 from title 1 from a given DVD structure:
.Sp
.Vb 1
\&        ffmpeg \-f dvdvideo \-chapter_start 3 \-chapter_end 6 \-title 1 \-i <path to DVD> ...
.Ve
.IP \(bu 4
Open only chapter 5 from title 1 from a given DVD structure:
.Sp
.Vb 1
\&        ffmpeg \-f dvdvideo \-chapter_start 5 \-chapter_end 5 \-title 1 \-i <path to DVD> ...
.Ve
.IP \(bu 4
Demux menu with language 1 from VTS 1, PGC 1, starting at PG 1:
.Sp
.Vb 1
\&        ffmpeg \-f dvdvideo \-menu 1 \-menu_lu 1 \-menu_vts 1 \-pgc 1 \-pg 1 \-i <path to DVD> ...
.Ve
.SS ea
.IX Subsection "ea"
Electronic Arts Multimedia format demuxer.
.PP
This format is used by various Electronic Arts games.
.PP
\fIOptions\fR
.IX Subsection "Options"
.IP "\fBmerge_alpha\fR \fIbool\fR" 4
.IX Item "merge_alpha bool"
Normally the VP6 alpha channel (if exists) is returned as a secondary video
stream, by setting this option you can make the demuxer return a single video
stream which contains the alpha channel in addition to the ordinary video.
.SS imf
.IX Subsection "imf"
Interoperable Master Format demuxer.
.PP
This demuxer presents audio and video streams found in an IMF Composition, as
specified in <\fBhttps://doi.org/10.5594/SMPTE.ST2067\-2.2020\fR>.
.PP
.Vb 1
\&        ffmpeg [\-assetmaps <path of ASSETMAP1>,<path of ASSETMAP2>,...] \-i <path of CPL> ...
.Ve
.PP
If \f(CW\*(C`\-assetmaps\*(C'\fR is not specified, the demuxer looks for a file called
\&\fIASSETMAP.xml\fR in the same directory as the CPL.
.SS "flv, live_flv, kux"
.IX Subsection "flv, live_flv, kux"
Adobe Flash Video Format demuxer.
.PP
This demuxer is used to demux FLV files and RTMP network streams. In case of live network streams, if you force format, you may use live_flv option instead of flv to survive timestamp discontinuities.
KUX is a flv variant used on the Youku platform.
.PP
.Vb 2
\&        ffmpeg \-f flv \-i myfile.flv ...
\&        ffmpeg \-f live_flv \-i rtmp://<any.server>/anything/key ....
.Ve
.IP "\fB\-flv_metadata\fR \fIbool\fR" 4
.IX Item "-flv_metadata bool"
Allocate the streams according to the onMetaData array content.
.IP "\fB\-flv_ignore_prevtag\fR \fIbool\fR" 4
.IX Item "-flv_ignore_prevtag bool"
Ignore the size of previous tag value.
.IP "\fB\-flv_full_metadata\fR \fIbool\fR" 4
.IX Item "-flv_full_metadata bool"
Output all context of the onMetadata.
.SS gif
.IX Subsection "gif"
Animated GIF demuxer.
.PP
It accepts the following options:
.IP \fBmin_delay\fR 4
.IX Item "min_delay"
Set the minimum valid delay between frames in hundredths of seconds.
Range is 0 to 6000. Default value is 2.
.IP \fBmax_gif_delay\fR 4
.IX Item "max_gif_delay"
Set the maximum valid delay between frames in hundredth of seconds.
Range is 0 to 65535. Default value is 65535 (nearly eleven minutes),
the maximum value allowed by the specification.
.IP \fBdefault_delay\fR 4
.IX Item "default_delay"
Set the default delay between frames in hundredths of seconds.
Range is 0 to 6000. Default value is 10.
.IP \fBignore_loop\fR 4
.IX Item "ignore_loop"
GIF files can contain information to loop a certain number of times (or
infinitely). If \fBignore_loop\fR is set to 1, then the loop setting
from the input will be ignored and looping will not occur. If set to 0,
then looping will occur and will cycle the number of times according to
the GIF. Default value is 1.
.PP
For example, with the overlay filter, place an infinitely looping GIF
over another video:
.PP
.Vb 1
\&        ffmpeg \-i input.mp4 \-ignore_loop 0 \-i input.gif \-filter_complex overlay=shortest=1 out.mkv
.Ve
.PP
Note that in the above example the shortest option for overlay filter is
used to end the output video at the length of the shortest input file,
which in this case is \fIinput.mp4\fR as the GIF in this example loops
infinitely.
.SS hls
.IX Subsection "hls"
HLS demuxer
.PP
Apple HTTP Live Streaming demuxer.
.PP
This demuxer presents all AVStreams from all variant streams.
The id field is set to the bitrate variant index number. By setting
the discard flags on AVStreams (by pressing 'a' or 'v' in ffplay),
the caller can decide which variant streams to actually receive.
The total bitrate of the variant that the stream belongs to is
available in a metadata key named "variant_bitrate".
.PP
It accepts the following options:
.IP \fBlive_start_index\fR 4
.IX Item "live_start_index"
segment index to start live streams at (negative values are from the end).
.IP \fBprefer_x_start\fR 4
.IX Item "prefer_x_start"
prefer to use #EXT\-X\-START if it's in playlist instead of live_start_index.
.IP \fBallowed_extensions\fR 4
.IX Item "allowed_extensions"
\&',' separated list of file extensions that hls is allowed to access.
.IP \fBextension_picky\fR 4
.IX Item "extension_picky"
This blocks disallowed extensions from probing
It also requires all available segments to have matching extensions to the format
except mpegts, which is always allowed.
It is recommended to set the whitelists correctly instead of depending on extensions
Enabled by default.
.IP \fBmax_reload\fR 4
.IX Item "max_reload"
Maximum number of times a insufficient list is attempted to be reloaded.
Default value is 1000.
.IP \fBm3u8_hold_counters\fR 4
.IX Item "m3u8_hold_counters"
The maximum number of times to load m3u8 when it refreshes without new segments.
Default value is 1000.
.IP \fBhttp_persistent\fR 4
.IX Item "http_persistent"
Use persistent HTTP connections. Applicable only for HTTP streams.
Enabled by default.
.IP \fBhttp_multiple\fR 4
.IX Item "http_multiple"
Use multiple HTTP connections for downloading HTTP segments.
Enabled by default for HTTP/1.1 servers.
.IP \fBhttp_seekable\fR 4
.IX Item "http_seekable"
Use HTTP partial requests for downloading HTTP segments.
0 = disable, 1 = enable, \-1 = auto, Default is auto.
.IP \fBseg_format_options\fR 4
.IX Item "seg_format_options"
Set options for the demuxer of media segments using a list of key=value pairs separated by \f(CW\*(C`:\*(C'\fR.
.IP \fBseg_max_retry\fR 4
.IX Item "seg_max_retry"
Maximum number of times to reload a segment on error, useful when segment skip on network error is not desired.
Default value is 0.
.SS image2
.IX Subsection "image2"
Image file demuxer.
.PP
This demuxer reads from a list of image files specified by a pattern.
The syntax and meaning of the pattern is specified by the
option \fIpattern_type\fR.
.PP
The pattern may contain a suffix which is used to automatically
determine the format of the images contained in the files.
.PP
The size, the pixel format, and the format of each image must be the
same for all the files in the sequence.
.PP
This demuxer accepts the following options:
.IP \fBframerate\fR 4
.IX Item "framerate"
Set the frame rate for the video stream. It defaults to 25.
.IP \fBloop\fR 4
.IX Item "loop"
If set to 1, loop over the input. Default value is 0.
.IP \fBpattern_type\fR 4
.IX Item "pattern_type"
Select the pattern type used to interpret the provided filename.
.Sp
\&\fIpattern_type\fR accepts one of the following values.
.RS 4
.IP \fBnone\fR 4
.IX Item "none"
Disable pattern matching, therefore the video will only contain the specified
image. You should use this option if you do not want to create sequences from
multiple images and your filenames may contain special pattern characters.
.IP \fBsequence\fR 4
.IX Item "sequence"
Select a sequence pattern type, used to specify a sequence of files
indexed by sequential numbers.
.Sp
A sequence pattern may contain the string "%d" or "%0\fIN\fRd", which
specifies the position of the characters representing a sequential
number in each filename matched by the pattern. If the form
"%d0\fIN\fRd" is used, the string representing the number in each
filename is 0\-padded and \fIN\fR is the total number of 0\-padded
digits representing the number. The literal character '%' can be
specified in the pattern with the string "%%".
.Sp
If the sequence pattern contains "%d" or "%0\fIN\fRd", the first filename of
the file list specified by the pattern must contain a number
inclusively contained between \fIstart_number\fR and
\&\fIstart_number\fR+\fIstart_number_range\fR\-1, and all the following
numbers must be sequential.
.Sp
For example the pattern "img\-%03d.bmp" will match a sequence of
filenames of the form \fIimg\-001.bmp\fR, \fIimg\-002.bmp\fR, ...,
\&\fIimg\-010.bmp\fR, etc.; the pattern "i%%m%%g\-%d.jpg" will match a
sequence of filenames of the form \fIi%m%g\-1.jpg\fR,
\&\fIi%m%g\-2.jpg\fR, ..., \fIi%m%g\-10.jpg\fR, etc.
.Sp
Note that the pattern must not necessarily contain "%d" or
"%0\fIN\fRd", for example to convert a single image file
\&\fIimg.jpeg\fR you can employ the command:
.Sp
.Vb 1
\&        ffmpeg \-i img.jpeg img.png
.Ve
.IP \fBglob\fR 4
.IX Item "glob"
Select a glob wildcard pattern type.
.Sp
The pattern is interpreted like a \f(CWglob()\fR pattern. This is only
selectable if libavformat was compiled with globbing support.
.IP "\fBglob_sequence\fR \fI(deprecated, will be removed)\fR" 4
.IX Item "glob_sequence (deprecated, will be removed)"
Select a mixed glob wildcard/sequence pattern.
.Sp
If your version of libavformat was compiled with globbing support, and
the provided pattern contains at least one glob meta character among
\&\f(CW\*(C`%*?[]{}\*(C'\fR that is preceded by an unescaped "%", the pattern is
interpreted like a \f(CWglob()\fR pattern, otherwise it is interpreted
like a sequence pattern.
.Sp
All glob special characters \f(CW\*(C`%*?[]{}\*(C'\fR must be prefixed
with "%". To escape a literal "%" you shall use "%%".
.Sp
For example the pattern \f(CW\*(C`foo\-%*.jpeg\*(C'\fR will match all the
filenames prefixed by "foo\-" and terminating with ".jpeg", and
\&\f(CW\*(C`foo\-%?%?%?.jpeg\*(C'\fR will match all the filenames prefixed with
"foo\-", followed by a sequence of three characters, and terminating
with ".jpeg".
.Sp
This pattern type is deprecated in favor of \fIglob\fR and
\&\fIsequence\fR.
.RE
.RS 4
.Sp
Default value is \fIglob_sequence\fR.
.RE
.IP \fBpixel_format\fR 4
.IX Item "pixel_format"
Set the pixel format of the images to read. If not specified the pixel
format is guessed from the first image file in the sequence.
.IP \fBstart_number\fR 4
.IX Item "start_number"
Set the index of the file matched by the image file pattern to start
to read from. Default value is 0.
.IP \fBstart_number_range\fR 4
.IX Item "start_number_range"
Set the index interval range to check when looking for the first image
file in the sequence, starting from \fIstart_number\fR. Default value
is 5.
.IP \fBts_from_file\fR 4
.IX Item "ts_from_file"
If set to 1, will set frame timestamp to modification time of image file. Note
that monotonity of timestamps is not provided: images go in the same order as
without this option. Default value is 0.
If set to 2, will set frame timestamp to the modification time of the image file in
nanosecond precision.
.IP \fBvideo_size\fR 4
.IX Item "video_size"
Set the video size of the images to read. If not specified the video
size is guessed from the first image file in the sequence.
.IP \fBexport_path_metadata\fR 4
.IX Item "export_path_metadata"
If set to 1, will add two extra fields to the metadata found in input, making them
also available for other filters (see \fIdrawtext\fR filter for examples). Default
value is 0. The extra fields are described below:
.RS 4
.IP \fBlavf.image2dec.source_path\fR 4
.IX Item "lavf.image2dec.source_path"
Corresponds to the full path to the input file being read.
.IP \fBlavf.image2dec.source_basename\fR 4
.IX Item "lavf.image2dec.source_basename"
Corresponds to the name of the file being read.
.RE
.RS 4
.RE
.PP
\fIExamples\fR
.IX Subsection "Examples"
.IP \(bu 4
Use \fBffmpeg\fR for creating a video from the images in the file
sequence \fIimg\-001.jpeg\fR, \fIimg\-002.jpeg\fR, ..., assuming an
input frame rate of 10 frames per second:
.Sp
.Vb 1
\&        ffmpeg \-framerate 10 \-i \*(Aqimg\-%03d.jpeg\*(Aq out.mkv
.Ve
.IP \(bu 4
As above, but start by reading from a file with index 100 in the sequence:
.Sp
.Vb 1
\&        ffmpeg \-framerate 10 \-start_number 100 \-i \*(Aqimg\-%03d.jpeg\*(Aq out.mkv
.Ve
.IP \(bu 4
Read images matching the "*.png" glob pattern , that is all the files
terminating with the ".png" suffix:
.Sp
.Vb 1
\&        ffmpeg \-framerate 10 \-pattern_type glob \-i "*.png" out.mkv
.Ve
.SS libgme
.IX Subsection "libgme"
The Game Music Emu library is a collection of video game music file emulators.
.PP
See <\fBhttps://bitbucket.org/mpyne/game\-music\-emu/overview\fR> for more information.
.PP
It accepts the following options:
.IP \fBtrack_index\fR 4
.IX Item "track_index"
Set the index of which track to demux. The demuxer can only export one track.
Track indexes start at 0. Default is to pick the first track. Number of tracks
is exported as \fItracks\fR metadata entry.
.IP \fBsample_rate\fR 4
.IX Item "sample_rate"
Set the sampling rate of the exported track. Range is 1000 to 999999. Default is 44100.
.IP "\fBmax_size\fR \fI(bytes)\fR" 4
.IX Item "max_size (bytes)"
The demuxer buffers the entire file into memory. Adjust this value to set the maximum buffer size,
which in turn, acts as a ceiling for the size of files that can be read.
Default is 50 MiB.
.SS libmodplug
.IX Subsection "libmodplug"
ModPlug based module demuxer
.PP
See <\fBhttps://github.com/Konstanty/libmodplug\fR>
.PP
It will export one 2\-channel 16\-bit 44.1 kHz audio stream.
Optionally, a \f(CW\*(C`pal8\*(C'\fR 16\-color video stream can be exported with or without printed metadata.
.PP
It accepts the following options:
.IP \fBnoise_reduction\fR 4
.IX Item "noise_reduction"
Apply a simple low-pass filter. Can be 1 (on) or 0 (off). Default is 0.
.IP \fBreverb_depth\fR 4
.IX Item "reverb_depth"
Set amount of reverb. Range 0\-100. Default is 0.
.IP \fBreverb_delay\fR 4
.IX Item "reverb_delay"
Set delay in ms, clamped to 40\-250 ms. Default is 0.
.IP \fBbass_amount\fR 4
.IX Item "bass_amount"
Apply bass expansion a.k.a. XBass or megabass. Range is 0 (quiet) to 100 (loud). Default is 0.
.IP \fBbass_range\fR 4
.IX Item "bass_range"
Set cutoff i.e. upper-bound for bass frequencies. Range is 10\-100 Hz. Default is 0.
.IP \fBsurround_depth\fR 4
.IX Item "surround_depth"
Apply a Dolby Pro-Logic surround effect. Range is 0 (quiet) to 100 (heavy). Default is 0.
.IP \fBsurround_delay\fR 4
.IX Item "surround_delay"
Set surround delay in ms, clamped to 5\-40 ms. Default is 0.
.IP \fBmax_size\fR 4
.IX Item "max_size"
The demuxer buffers the entire file into memory. Adjust this value to set the maximum buffer size,
which in turn, acts as a ceiling for the size of files that can be read. Range is 0 to 100 MiB.
0 removes buffer size limit (not recommended). Default is 5 MiB.
.IP \fBvideo_stream_expr\fR 4
.IX Item "video_stream_expr"
String which is evaluated using the eval API to assign colors to the generated video stream.
Variables which can be used are \f(CW\*(C`x\*(C'\fR, \f(CW\*(C`y\*(C'\fR, \f(CW\*(C`w\*(C'\fR, \f(CW\*(C`h\*(C'\fR, \f(CW\*(C`t\*(C'\fR, \f(CW\*(C`speed\*(C'\fR,
\&\f(CW\*(C`tempo\*(C'\fR, \f(CW\*(C`order\*(C'\fR, \f(CW\*(C`pattern\*(C'\fR and \f(CW\*(C`row\*(C'\fR.
.IP \fBvideo_stream\fR 4
.IX Item "video_stream"
Generate video stream. Can be 1 (on) or 0 (off). Default is 0.
.IP \fBvideo_stream_w\fR 4
.IX Item "video_stream_w"
Set video frame width in 'chars' where one char indicates 8 pixels. Range is 20\-512. Default is 30.
.IP \fBvideo_stream_h\fR 4
.IX Item "video_stream_h"
Set video frame height in 'chars' where one char indicates 8 pixels. Range is 20\-512. Default is 30.
.IP \fBvideo_stream_ptxt\fR 4
.IX Item "video_stream_ptxt"
Print metadata on video stream. Includes \f(CW\*(C`speed\*(C'\fR, \f(CW\*(C`tempo\*(C'\fR, \f(CW\*(C`order\*(C'\fR, \f(CW\*(C`pattern\*(C'\fR,
\&\f(CW\*(C`row\*(C'\fR and \f(CW\*(C`ts\*(C'\fR (time in ms). Can be 1 (on) or 0 (off). Default is 1.
.SS libopenmpt
.IX Subsection "libopenmpt"
libopenmpt based module demuxer
.PP
See <\fBhttps://lib.openmpt.org/libopenmpt/\fR> for more information.
.PP
Some files have multiple subsongs (tracks) this can be set with the \fBsubsong\fR
option.
.PP
It accepts the following options:
.IP \fBsubsong\fR 4
.IX Item "subsong"
Set the subsong index. This can be either  'all', 'auto', or the index of the
subsong. Subsong indexes start at 0. The default is 'auto'.
.Sp
The default value is to let libopenmpt choose.
.IP \fBlayout\fR 4
.IX Item "layout"
Set the channel layout. Valid values are 1, 2, and 4 channel layouts.
The default value is STEREO.
.IP \fBsample_rate\fR 4
.IX Item "sample_rate"
Set the sample rate for libopenmpt to output.
Range is from 1000 to INT_MAX. The value default is 48000.
.SS mov/mp4/3gp
.IX Subsection "mov/mp4/3gp"
Demuxer for Quicktime File Format & ISO/IEC Base Media File Format (ISO/IEC 14496\-12 or MPEG\-4 Part 12, ISO/IEC 15444\-12 or JPEG 2000 Part 12).
.PP
Registered extensions: mov, mp4, m4a, 3gp, 3g2, mj2, psp, m4b, ism, ismv, isma, f4v
.PP
\fIOptions\fR
.IX Subsection "Options"
.PP
This demuxer accepts the following options:
.IP \fBenable_drefs\fR 4
.IX Item "enable_drefs"
Enable loading of external tracks, disabled by default.
Enabling this can theoretically leak information in some use cases.
.IP \fBuse_absolute_path\fR 4
.IX Item "use_absolute_path"
Allows loading of external tracks via absolute paths, disabled by default.
Enabling this poses a security risk. It should only be enabled if the source
is known to be non-malicious.
.IP \fBseek_streams_individually\fR 4
.IX Item "seek_streams_individually"
When seeking, identify the closest point in each stream individually and demux packets in
that stream from identified point. This can lead to a different sequence of packets compared
to demuxing linearly from the beginning. Default is true.
.IP \fBignore_editlist\fR 4
.IX Item "ignore_editlist"
Ignore any edit list atoms. The demuxer, by default, modifies the stream index to reflect the
timeline described by the edit list. Default is false.
.IP \fBadvanced_editlist\fR 4
.IX Item "advanced_editlist"
Modify the stream index to reflect the timeline described by the edit list. \f(CW\*(C`ignore_editlist\*(C'\fR
must be set to false for this option to be effective.
If both \f(CW\*(C`ignore_editlist\*(C'\fR and this option are set to false, then only the
start of the stream index is modified to reflect initial dwell time or starting timestamp
described by the edit list. Default is true.
.IP \fBignore_chapters\fR 4
.IX Item "ignore_chapters"
Don't parse chapters. This includes GoPro 'HiLight' tags/moments. Note that chapters are
only parsed when input is seekable. Default is false.
.IP \fBuse_mfra_for\fR 4
.IX Item "use_mfra_for"
For seekable fragmented input, set fragment's starting timestamp from media fragment random access box, if present.
.Sp
Following options are available:
.RS 4
.IP \fBauto\fR 4
.IX Item "auto"
Auto-detect whether to set mfra timestamps as PTS or DTS \fI(default)\fR
.IP \fBdts\fR 4
.IX Item "dts"
Set mfra timestamps as DTS
.IP \fBpts\fR 4
.IX Item "pts"
Set mfra timestamps as PTS
.IP \fB0\fR 4
.IX Item "0"
Don't use mfra box to set timestamps
.RE
.RS 4
.RE
.IP \fBuse_tfdt\fR 4
.IX Item "use_tfdt"
For fragmented input, set fragment's starting timestamp to \f(CW\*(C`baseMediaDecodeTime\*(C'\fR from the \f(CW\*(C`tfdt\*(C'\fR box.
Default is enabled, which will prefer to use the \f(CW\*(C`tfdt\*(C'\fR box to set DTS. Disable to use the \f(CW\*(C`earliest_presentation_time\*(C'\fR from the \f(CW\*(C`sidx\*(C'\fR box.
In either case, the timestamp from the \f(CW\*(C`mfra\*(C'\fR box will be used if it's available and \f(CW\*(C`use_mfra_for\*(C'\fR is
set to pts or dts.
.IP \fBexport_all\fR 4
.IX Item "export_all"
Export unrecognized boxes within the \fIudta\fR box as metadata entries. The first four
characters of the box type are set as the key. Default is false.
.IP \fBexport_xmp\fR 4
.IX Item "export_xmp"
Export entire contents of \fIXMP_\fR box and \fIuuid\fR box as a string with key \f(CW\*(C`xmp\*(C'\fR. Note that
if \f(CW\*(C`export_all\*(C'\fR is set and this option isn't, the contents of \fIXMP_\fR box are still exported
but with key \f(CW\*(C`XMP_\*(C'\fR. Default is false.
.IP \fBactivation_bytes\fR 4
.IX Item "activation_bytes"
4\-byte key required to decrypt Audible AAX and AAX+ files. See Audible AAX subsection below.
.IP \fBaudible_fixed_key\fR 4
.IX Item "audible_fixed_key"
Fixed key used for handling Audible AAX/AAX+ files. It has been pre-set so should not be necessary to
specify.
.IP \fBdecryption_key\fR 4
.IX Item "decryption_key"
16\-byte key, in hex, to decrypt files encrypted using ISO Common Encryption (CENC/AES\-128 CTR; ISO/IEC 23001\-7).
.IP \fBmax_stts_delta\fR 4
.IX Item "max_stts_delta"
Very high sample deltas written in a trak's stts box may occasionally be intended but usually they are written in
error or used to store a negative value for dts correction when treated as signed 32\-bit integers. This option lets
the user set an upper limit, beyond which the delta is clamped to 1. Values greater than the limit if negative when
cast to int32 are used to adjust onward dts.
.Sp
Unit is the track time scale. Range is 0 to UINT_MAX. Default is \f(CW\*(C`UINT_MAX \- 48000*10\*(C'\fR which allows up to
a 10 second dts correction for 48 kHz audio streams while accommodating 99.9% of \f(CW\*(C`uint32\*(C'\fR range.
.IP \fBinterleaved_read\fR 4
.IX Item "interleaved_read"
Interleave packets from multiple tracks at demuxer level. For badly interleaved files, this prevents playback issues
caused by large gaps between packets in different tracks, as MOV/MP4 do not have packet placement requirements.
However, this can cause excessive seeking on very badly interleaved files, due to seeking between tracks, so disabling
it may prevent I/O issues, at the expense of playback.
.PP
\fIAudible AAX\fR
.IX Subsection "Audible AAX"
.PP
Audible AAX files are encrypted M4B files, and they can be decrypted by specifying a 4 byte activation secret.
.PP
.Vb 1
\&        ffmpeg \-activation_bytes 1CEB00DA \-i test.aax \-vn \-c:a copy output.mp4
.Ve
.SS mpegts
.IX Subsection "mpegts"
MPEG\-2 transport stream demuxer.
.PP
This demuxer accepts the following options:
.IP \fBresync_size\fR 4
.IX Item "resync_size"
Set size limit for looking up a new synchronization. Default value is
65536.
.IP \fBskip_unknown_pmt\fR 4
.IX Item "skip_unknown_pmt"
Skip PMTs for programs not defined in the PAT. Default value is 0.
.IP \fBfix_teletext_pts\fR 4
.IX Item "fix_teletext_pts"
Override teletext packet PTS and DTS values with the timestamps calculated
from the PCR of the first program which the teletext stream is part of and is
not discarded. Default value is 1, set this option to 0 if you want your
teletext packet PTS and DTS values untouched.
.IP \fBts_packetsize\fR 4
.IX Item "ts_packetsize"
Output option carrying the raw packet size in bytes.
Show the detected raw packet size, cannot be set by the user.
.IP \fBscan_all_pmts\fR 4
.IX Item "scan_all_pmts"
Scan and combine all PMTs. The value is an integer with value from \-1
to 1 (\-1 means automatic setting, 1 means enabled, 0 means
disabled). Default value is \-1.
.IP \fBmerge_pmt_versions\fR 4
.IX Item "merge_pmt_versions"
Reuse existing streams when a PMT's version is updated and elementary
streams move to different PIDs. Default value is 0.
.IP \fBmax_packet_size\fR 4
.IX Item "max_packet_size"
Set maximum size, in bytes, of packet emitted by the demuxer. Payloads above this size
are split across multiple packets. Range is 1 to INT_MAX/2. Default is 204800 bytes.
.SS mpjpeg
.IX Subsection "mpjpeg"
MJPEG encapsulated in multi-part MIME demuxer.
.PP
This demuxer allows reading of MJPEG, where each frame is represented as a part of
multipart/x\-mixed\-replace stream.
.IP \fBstrict_mime_boundary\fR 4
.IX Item "strict_mime_boundary"
Default implementation applies a relaxed standard to multi-part MIME boundary detection,
to prevent regression with numerous existing endpoints not generating a proper MIME
MJPEG stream. Turning this option on by setting it to 1 will result in a stricter check
of the boundary value.
.SS rawvideo
.IX Subsection "rawvideo"
Raw video demuxer.
.PP
This demuxer allows one to read raw video data. Since there is no header
specifying the assumed video parameters, the user must specify them
in order to be able to decode the data correctly.
.PP
This demuxer accepts the following options:
.IP \fBframerate\fR 4
.IX Item "framerate"
Set input video frame rate. Default value is 25.
.IP \fBpixel_format\fR 4
.IX Item "pixel_format"
Set the input video pixel format. Default value is \f(CW\*(C`yuv420p\*(C'\fR.
.IP \fBvideo_size\fR 4
.IX Item "video_size"
Set the input video size. This value must be specified explicitly.
.PP
For example to read a rawvideo file \fIinput.raw\fR with
\&\fBffplay\fR, assuming a pixel format of \f(CW\*(C`rgb24\*(C'\fR, a video
size of \f(CW\*(C`320x240\*(C'\fR, and a frame rate of 10 images per second, use
the command:
.PP
.Vb 1
\&        ffplay \-f rawvideo \-pixel_format rgb24 \-video_size 320x240 \-framerate 10 input.raw
.Ve
.SS rcwt
.IX Subsection "rcwt"
RCWT (Raw Captions With Time) is a format native to ccextractor, a commonly
used open source tool for processing 608/708 Closed Captions (CC) sources.
For more information on the format, see .
.PP
This demuxer implements the specification as of March 2024, which has
been stable and unchanged since April 2014.
.PP
\fIExamples\fR
.IX Subsection "Examples"
.IP \(bu 4
Render CC to ASS using the built-in decoder:
.Sp
.Vb 1
\&        ffmpeg \-i CC.rcwt.bin CC.ass
.Ve
.Sp
Note that if your output appears to be empty, you may have to manually
set the decoder's \fBdata_field\fR option to pick the desired CC substream.
.IP \(bu 4
Convert an RCWT backup to Scenarist (SCC) format:
.Sp
.Vb 1
\&        ffmpeg \-i CC.rcwt.bin \-c:s copy CC.scc
.Ve
.Sp
Note that the SCC format does not support all of the possible CC extensions
that can be stored in RCWT (such as EIA\-708).
.SS sbg
.IX Subsection "sbg"
SBaGen script demuxer.
.PP
This demuxer reads the script language used by SBaGen
<\fBhttp://uazu.net/sbagen/\fR> to generate binaural beats sessions. A SBG
script looks like that:
.PP
.Vb 9
\&        \-SE
\&        a: 300\-2.5/3 440+4.5/0
\&        b: 300\-2.5/0 440+4.5/3
\&        off: \-
\&        NOW      == a
\&        +0:07:00 == b
\&        +0:14:00 == a
\&        +0:21:00 == b
\&        +0:30:00    off
.Ve
.PP
A SBG script can mix absolute and relative timestamps. If the script uses
either only absolute timestamps (including the script start time) or only
relative ones, then its layout is fixed, and the conversion is
straightforward. On the other hand, if the script mixes both kind of
timestamps, then the \fINOW\fR reference for relative timestamps will be
taken from the current time of day at the time the script is read, and the
script layout will be frozen according to that reference. That means that if
the script is directly played, the actual times will match the absolute
timestamps up to the sound controller's clock accuracy, but if the user
somehow pauses the playback or seeks, all times will be shifted accordingly.
.SS tedcaptions
.IX Subsection "tedcaptions"
JSON captions used for <\fBhttp://www.ted.com/\fR>.
.PP
TED does not provide links to the captions, but they can be guessed from the
page. The file \fItools/bookmarklets.html\fR from the FFmpeg source tree
contains a bookmarklet to expose them.
.PP
This demuxer accepts the following option:
.IP \fBstart_time\fR 4
.IX Item "start_time"
Set the start time of the TED talk, in milliseconds. The default is 15000
(15s). It is used to sync the captions with the downloadable videos, because
they include a 15s intro.
.PP
Example: convert the captions to a format most players understand:
.PP
.Vb 1
\&        ffmpeg \-i http://www.ted.com/talks/subtitles/id/1/lang/en talk1\-en.srt
.Ve
.SS vapoursynth
.IX Subsection "vapoursynth"
Vapoursynth wrapper.
.PP
Due to security concerns, Vapoursynth scripts will not
be autodetected so the input format has to be forced. For ff* CLI tools,
add \f(CW\*(C`\-f vapoursynth\*(C'\fR before the input \f(CW\*(C`\-i yourscript.vpy\*(C'\fR.
.PP
This demuxer accepts the following option:
.IP \fBmax_script_size\fR 4
.IX Item "max_script_size"
The demuxer buffers the entire script into memory. Adjust this value to set the maximum buffer size,
which in turn, acts as a ceiling for the size of scripts that can be read.
Default is 1 MiB.
.SS w64
.IX Subsection "w64"
Sony Wave64 Audio demuxer.
.PP
This demuxer accepts the following options:
.IP \fBmax_size\fR 4
.IX Item "max_size"
See the same option for the \fBwav\fR demuxer.
.SS wav
.IX Subsection "wav"
RIFF Wave Audio demuxer.
.PP
This demuxer accepts the following options:
.IP \fBmax_size\fR 4
.IX Item "max_size"
Specify the maximum packet size in bytes for the demuxed packets. By default
this is set to 0, which means that a sensible value is chosen based on the
input format.
.SH MUXERS
.IX Header "MUXERS"
Muxers are configured elements in FFmpeg which allow writing
multimedia streams to a particular type of file.
.PP
When you configure your FFmpeg build, all the supported muxers
are enabled by default. You can list all available muxers using the
configure option \f(CW\*(C`\-\-list\-muxers\*(C'\fR.
.PP
You can disable all the muxers with the configure option
\&\f(CW\*(C`\-\-disable\-muxers\*(C'\fR and selectively enable / disable single muxers
with the options \f(CW\*(C`\-\-enable\-muxer=\fR\f(CIMUXER\fR\f(CW\*(C'\fR /
\&\f(CW\*(C`\-\-disable\-muxer=\fR\f(CIMUXER\fR\f(CW\*(C'\fR.
.PP
The option \f(CW\*(C`\-muxers\*(C'\fR of the ff* tools will display the list of
enabled muxers. Use \f(CW\*(C`\-formats\*(C'\fR to view a combined list of
enabled demuxers and muxers.
.PP
A description of some of the currently available muxers follows.
.SS "Raw muxers"
.IX Subsection "Raw muxers"
This section covers raw muxers. They accept a single stream matching
the designated codec. They do not store timestamps or metadata. The
recognized extension is the same as the muxer name unless indicated
otherwise.
.PP
It comprises the following muxers. The media type and the eventual
extensions used to automatically selects the muxer from the output
extensions are also shown.
.IP "\fBac3\fR \fIaudio\fR" 4
.IX Item "ac3 audio"
Dolby Digital, also known as AC\-3.
.IP "\fBadx\fR \fIaudio\fR" 4
.IX Item "adx audio"
CRI Middleware ADX audio.
.Sp
This muxer will write out the total sample count near the start of the
first packet when the output is seekable and the count can be stored
in 32 bits.
.IP "\fBaptx\fR \fIaudio\fR" 4
.IX Item "aptx audio"
aptX (Audio Processing Technology for Bluetooth)
.IP "\fBaptx_hd\fR \fIaudio\fR \fB(aptxdh)\fR" 4
.IX Item "aptx_hd audio (aptxdh)"
aptX HD (Audio Processing Technology for Bluetooth) audio
.IP "\fBavs2\fR \fIvideo\fR \fB(avs, avs2)\fR" 4
.IX Item "avs2 video (avs, avs2)"
AVS2\-P2 (Audio Video Standard \- Second generation \- Part 2) /
IEEE 1857.4 video
.IP "\fBavs3\fR \fIvideo\fR \fB(avs3)\fR" 4
.IX Item "avs3 video (avs3)"
AVS3\-P2 (Audio Video Standard \- Third generation \- Part 2) /
IEEE 1857.10 video
.IP "\fBcavsvideo\fR \fIvideo\fR \fB(cavs)\fR" 4
.IX Item "cavsvideo video (cavs)"
Chinese AVS (Audio Video Standard \- First generation)
.IP "\fBcodec2raw\fR \fIaudio\fR" 4
.IX Item "codec2raw audio"
Codec 2 audio.
.Sp
No extension is registered so format name has to be supplied e.g. with
the ffmpeg CLI tool \f(CW\*(C`\-f codec2raw\*(C'\fR.
.IP "\fBdata\fR \fIany\fR" 4
.IX Item "data any"
Generic data muxer.
.Sp
This muxer accepts a single stream with any codec of any type. The
input stream has to be selected using the \f(CW\*(C`\-map\*(C'\fR option with the
\&\fBffmpeg\fR CLI tool.
.Sp
No extension is registered so format name has to be supplied e.g. with
the \fBffmpeg\fR CLI tool \f(CW\*(C`\-f data\*(C'\fR.
.IP "\fBdfpwm\fR \fIaudio\fR \fB(dfpwm)\fR" 4
.IX Item "dfpwm audio (dfpwm)"
Raw DFPWM1a (Dynamic Filter Pulse With Modulation) audio muxer.
.IP "\fBdirac\fR \fIvideo\fR \fB(drc, vc2)\fR" 4
.IX Item "dirac video (drc, vc2)"
BBC Dirac video.
.Sp
The Dirac Pro codec is a subset and is standardized as SMPTE VC\-2.
.IP "\fBdnxhd\fR \fIvideo\fR \fB(dnxhd, dnxhr)\fR" 4
.IX Item "dnxhd video (dnxhd, dnxhr)"
Avid DNxHD video.
.Sp
It is standardized as SMPTE VC\-3. Accepts DNxHR streams.
.IP "\fBdts\fR \fIaudio\fR" 4
.IX Item "dts audio"
DTS Coherent Acoustics (DCA) audio
.IP "\fBeac3\fR \fIaudio\fR" 4
.IX Item "eac3 audio"
Dolby Digital Plus, also known as Enhanced AC\-3
.IP "\fBevc\fR \fIvideo\fR \fB(evc)\fR" 4
.IX Item "evc video (evc)"
MPEG\-5 Essential Video Coding (EVC) / EVC / MPEG\-5 Part 1 EVC video
.IP "\fBg722\fR \fIaudio\fR" 4
.IX Item "g722 audio"
ITU-T G.722 audio
.IP "\fBg723_1\fR \fIaudio\fR \fB(tco, rco)\fR" 4
.IX Item "g723_1 audio (tco, rco)"
ITU-T G.723.1 audio
.IP "\fBg726\fR \fIaudio\fR" 4
.IX Item "g726 audio"
ITU-T G.726 big-endian ("left-justified") audio.
.Sp
No extension is registered so format name has to be supplied e.g. with
the \fBffmpeg\fR CLI tool \f(CW\*(C`\-f g726\*(C'\fR.
.IP "\fBg726le\fR \fIaudio\fR" 4
.IX Item "g726le audio"
ITU-T G.726 little-endian ("right-justified") audio.
.Sp
No extension is registered so format name has to be supplied e.g. with
the \fBffmpeg\fR CLI tool \f(CW\*(C`\-f g726le\*(C'\fR.
.IP "\fBgsm\fR \fIaudio\fR" 4
.IX Item "gsm audio"
Global System for Mobile Communications audio
.IP "\fBh261\fR \fIvideo\fR" 4
.IX Item "h261 video"
ITU-T H.261 video
.IP "\fBh263\fR \fIvideo\fR" 4
.IX Item "h263 video"
ITU-T H.263 / H.263\-1996, H.263+ / H.263\-1998 / H.263 version 2 video
.IP "\fBh264\fR \fIvideo\fR \fB(h264, 264)\fR" 4
.IX Item "h264 video (h264, 264)"
ITU-T H.264 / MPEG\-4 Part 10 AVC video. Bitstream shall be converted
to Annex B syntax if it's in length-prefixed mode.
.IP "\fBhevc\fR \fIvideo\fR \fB(hevc, h265, 265)\fR" 4
.IX Item "hevc video (hevc, h265, 265)"
ITU-T H.265 / MPEG-H Part 2 HEVC video. Bitstream shall be converted
to Annex B syntax if it's in length-prefixed mode.
.IP "\fBm4v\fR \fIvideo\fR" 4
.IX Item "m4v video"
MPEG\-4 Part 2 video
.IP "\fBmjpeg\fR \fIvideo\fR \fB(mjpg, mjpeg)\fR" 4
.IX Item "mjpeg video (mjpg, mjpeg)"
Motion JPEG video
.IP "\fBmlp\fR \fIaudio\fR" 4
.IX Item "mlp audio"
Meridian Lossless Packing, also known as Packed PCM
.IP "\fBmp2\fR \fIaudio\fR \fB(mp2, m2a, mpa)\fR" 4
.IX Item "mp2 audio (mp2, m2a, mpa)"
MPEG\-1 Audio Layer II audio
.IP "\fBmpeg1video\fR \fIvideo\fR \fB(mpg, mpeg, m1v)\fR" 4
.IX Item "mpeg1video video (mpg, mpeg, m1v)"
MPEG\-1 Part 2 video.
.IP "\fBmpeg2video\fR \fIvideo\fR \fB(m2v)\fR" 4
.IX Item "mpeg2video video (m2v)"
ITU-T H.262 / MPEG\-2 Part 2 video
.IP "\fBobu\fR \fIvideo\fR" 4
.IX Item "obu video"
AV1 low overhead Open Bitstream Units muxer.
.Sp
Temporal delimiter OBUs will be inserted in all temporal units of the
stream.
.IP "\fBrawvideo\fR \fIvideo\fR \fB(yuv, rgb)\fR" 4
.IX Item "rawvideo video (yuv, rgb)"
Raw uncompressed video.
.IP "\fBsbc\fR \fIaudio\fR \fB(sbc, msbc)\fR" 4
.IX Item "sbc audio (sbc, msbc)"
Bluetooth SIG low-complexity subband codec audio
.IP "\fBtruehd\fR \fIaudio\fR \fB(thd)\fR" 4
.IX Item "truehd audio (thd)"
Dolby TrueHD audio
.IP "\fBvc1\fR \fIvideo\fR" 4
.IX Item "vc1 video"
SMPTE 421M / VC\-1 video
.PP
\fIExamples\fR
.IX Subsection "Examples"
.IP \(bu 4
Store raw video frames with the \fBrawvideo\fR muxer using \fBffmpeg\fR:
.Sp
.Vb 1
\&        ffmpeg \-f lavfi \-i testsrc \-t 10 \-s hd1080p testsrc.yuv
.Ve
.Sp
Since the rawvideo muxer do not store the information related to size
and format, this information must be provided when demuxing the file:
.Sp
.Vb 1
\&        ffplay \-video_size 1920x1080 \-pixel_format rgb24 \-f rawvideo testsrc.rgb
.Ve
.SS "Raw PCM muxers"
.IX Subsection "Raw PCM muxers"
This section covers raw PCM (Pulse-Code Modulation) audio muxers.
.PP
They accept a single stream matching the designated codec. They do not
store timestamps or metadata. The recognized extension is the same as
the muxer name.
.PP
It comprises the following muxers. The optional additional extension
used to automatically select the muxer from the output extension is
also shown in parentheses.
.IP "\fBalaw (al)\fR" 4
.IX Item "alaw (al)"
PCM A\-law
.IP \fBf32be\fR 4
.IX Item "f32be"
PCM 32\-bit floating-point big-endian
.IP \fBf32le\fR 4
.IX Item "f32le"
PCM 32\-bit floating-point little-endian
.IP \fBf64be\fR 4
.IX Item "f64be"
PCM 64\-bit floating-point big-endian
.IP \fBf64le\fR 4
.IX Item "f64le"
PCM 64\-bit floating-point little-endian
.IP "\fBmulaw (ul)\fR" 4
.IX Item "mulaw (ul)"
PCM mu-law
.IP \fBs16be\fR 4
.IX Item "s16be"
PCM signed 16\-bit big-endian
.IP \fBs16le\fR 4
.IX Item "s16le"
PCM signed 16\-bit little-endian
.IP \fBs24be\fR 4
.IX Item "s24be"
PCM signed 24\-bit big-endian
.IP \fBs24le\fR 4
.IX Item "s24le"
PCM signed 24\-bit little-endian
.IP \fBs32be\fR 4
.IX Item "s32be"
PCM signed 32\-bit big-endian
.IP \fBs32le\fR 4
.IX Item "s32le"
PCM signed 32\-bit little-endian
.IP "\fBs8 (sb)\fR" 4
.IX Item "s8 (sb)"
PCM signed 8\-bit
.IP \fBu16be\fR 4
.IX Item "u16be"
PCM unsigned 16\-bit big-endian
.IP \fBu16le\fR 4
.IX Item "u16le"
PCM unsigned 16\-bit little-endian
.IP \fBu24be\fR 4
.IX Item "u24be"
PCM unsigned 24\-bit big-endian
.IP \fBu24le\fR 4
.IX Item "u24le"
PCM unsigned 24\-bit little-endian
.IP \fBu32be\fR 4
.IX Item "u32be"
PCM unsigned 32\-bit big-endian
.IP \fBu32le\fR 4
.IX Item "u32le"
PCM unsigned 32\-bit little-endian
.IP "\fBu8 (ub)\fR" 4
.IX Item "u8 (ub)"
PCM unsigned 8\-bit
.IP \fBvidc\fR 4
.IX Item "vidc"
PCM Archimedes VIDC
.SS "MPEG\-1/MPEG\-2 program stream muxers"
.IX Subsection "MPEG-1/MPEG-2 program stream muxers"
This section covers formats belonging to the MPEG\-1 and MPEG\-2 Systems
family.
.PP
The MPEG\-1 Systems format (also known as ISO/IEEC 11172\-1 or MPEG\-1
program stream) has been adopted for the format of media track stored
in VCD (Video Compact Disc).
.PP
The MPEG\-2 Systems standard (also known as ISO/IEEC 13818\-1) covers
two containers formats, one known as transport stream and one known as
program stream; only the latter is covered here.
.PP
The MPEG\-2 program stream format (also known as VOB due to the
corresponding file extension) is an extension of MPEG\-1 program
stream: in addition to support different codecs for the audio and
video streams, it also stores subtitles and navigation metadata.
MPEG\-2 program stream has been adopted for storing media streams in
SVCD and DVD storage devices.
.PP
This section comprises the following muxers.
.IP "\fBmpeg (mpg,mpeg)\fR" 4
.IX Item "mpeg (mpg,mpeg)"
MPEG\-1 Systems / MPEG\-1 program stream muxer.
.IP \fBvcd\fR 4
.IX Item "vcd"
MPEG\-1 Systems / MPEG\-1 program stream (VCD) muxer.
.Sp
This muxer can be used to generate tracks in the format accepted by
the VCD (Video Compact Disc) storage devices.
.Sp
It is the same as the \fBmpeg\fR muxer with a few differences.
.IP \fBvob\fR 4
.IX Item "vob"
MPEG\-2 program stream (VOB) muxer.
.IP \fBdvd\fR 4
.IX Item "dvd"
MPEG\-2 program stream (DVD VOB) muxer.
.Sp
This muxer can be used to generate tracks in the format accepted by
the DVD (Digital Versatile Disc) storage devices.
.Sp
This is the same as the \fBvob\fR muxer with a few differences.
.IP "\fBsvcd (vob)\fR" 4
.IX Item "svcd (vob)"
MPEG\-2 program stream (SVCD VOB) muxer.
.Sp
This muxer can be used to generate tracks in the format accepted by
the SVCD (Super Video Compact Disc) storage devices.
.Sp
This is the same as the \fBvob\fR muxer with a few differences.
.PP
\fIOptions\fR
.IX Subsection "Options"
.IP "\fBmuxrate\fR \fIrate\fR" 4
.IX Item "muxrate rate"
Set user-defined mux rate expressed as a number of bits/s. If not
specified the automatically computed mux rate is employed. Default value
is \f(CW0\fR.
.IP "\fBpreload\fR \fIdelay\fR" 4
.IX Item "preload delay"
Set initial demux-decode delay in microseconds. Default value is
\&\f(CW500000\fR.
.SS "MOV/MPEG\-4/ISOMBFF muxers"
.IX Subsection "MOV/MPEG-4/ISOMBFF muxers"
This section covers formats belonging to the QuickTime / MOV family,
including the MPEG\-4 Part 14 format and ISO base media file format
(ISOBMFF). These formats share a common structure based on the ISO
base media file format (ISOBMFF).
.PP
The MOV format was originally developed for use with Apple QuickTime.
It was later used as the basis for the MPEG\-4 Part 1 (later Part 14)
format, also known as ISO/IEC 14496\-1. That format was then
generalized into ISOBMFF, also named MPEG\-4 Part 12 format, ISO/IEC
14496\-12, or ISO/IEC 15444\-12.
.PP
It comprises the following muxers.
.IP \fB3gp\fR 4
.IX Item "3gp"
Third Generation Partnership Project (3GPP) format for 3G UMTS
multimedia services
.IP \fB3g2\fR 4
.IX Item "3g2"
Third Generation Partnership Project 2 (3GP2 or 3GPP2) format for 3G
CDMA2000 multimedia services, similar to \fB3gp\fR with extensions
and limitations
.IP \fBf4v\fR 4
.IX Item "f4v"
Adobe Flash Video format
.IP \fBipod\fR 4
.IX Item "ipod"
MPEG\-4 audio file format, as MOV/MP4 but limited to contain only audio
streams, typically played with the Apple ipod device
.IP \fBismv\fR 4
.IX Item "ismv"
Microsoft IIS (Internet Information Services) Smooth Streaming
Audio/Video (ISMV or ISMA) format. This is based on MPEG\-4 Part 14
format with a few incompatible variants, used to stream media files
for the Microsoft IIS server.
.IP \fBmov\fR 4
.IX Item "mov"
QuickTime player format identified by the \f(CW\*(C`.mov\*(C'\fR extension
.IP \fBmp4\fR 4
.IX Item "mp4"
MP4 or MPEG\-4 Part 14 format
.IP \fBpsp\fR 4
.IX Item "psp"
PlayStation Portable MP4/MPEG\-4 Part 14 format variant. This is based
on MPEG\-4 Part 14 format with a few incompatible variants, used to
play files on PlayStation devices.
.PP
\fIFragmentation\fR
.IX Subsection "Fragmentation"
.PP
The \fBmov\fR, \fBmp4\fR, and \fBismv\fR muxers support
fragmentation. Normally, a MOV/MP4 file has all the metadata about all
packets stored in one location.
.PP
This data is usually written at the end of the file, but it can be
moved to the start for better playback by adding \f(CW\*(C`+faststart\*(C'\fR to
the \f(CW\*(C`\-movflags\*(C'\fR, or using the \fBqt-faststart\fR tool).
.PP
A fragmented file consists of a number of fragments, where packets and
metadata about these packets are stored together. Writing a fragmented
file has the advantage that the file is decodable even if the writing
is interrupted (while a normal MOV/MP4 is undecodable if it is not
properly finished), and it requires less memory when writing very long
files (since writing normal MOV/MP4 files stores info about every
single packet in memory until the file is closed). The downside is
that it is less compatible with other applications.
.PP
Fragmentation is enabled by setting one of the options that define
how to cut the file into fragments:
.IP \fBfrag_duration\fR 4
.IX Item "frag_duration"
.PD 0
.IP \fBfrag_size\fR 4
.IX Item "frag_size"
.IP \fBmin_frag_duration\fR 4
.IX Item "min_frag_duration"
.IP "\fBmovflags +frag_keyframe\fR" 4
.IX Item "movflags +frag_keyframe"
.IP "\fBmovflags +frag_custom\fR" 4
.IX Item "movflags +frag_custom"
.PD
.PP
If more than one condition is specified, fragments are cut when one of
the specified conditions is fulfilled. The exception to this is the
option \fBmin_frag_duration\fR, which has to be fulfilled for any
of the other conditions to apply.
.PP
\fIOptions\fR
.IX Subsection "Options"
.IP "\fBbrand\fR \fIbrand_string\fR" 4
.IX Item "brand brand_string"
Override major brand.
.IP "\fBempty_hdlr_name\fR \fIbool\fR" 4
.IX Item "empty_hdlr_name bool"
Enable to skip writing the name inside a \f(CW\*(C`hdlr\*(C'\fR box.
Default is \f(CW\*(C`false\*(C'\fR.
.IP "\fBencryption_key\fR \fIkey\fR" 4
.IX Item "encryption_key key"
set the media encryption key in hexadecimal format
.IP "\fBencryption_kid\fR \fIkid\fR" 4
.IX Item "encryption_kid kid"
set the media encryption key identifier in hexadecimal format
.IP "\fBencryption_scheme\fR \fIscheme\fR" 4
.IX Item "encryption_scheme scheme"
configure the encryption scheme, allowed values are \fBnone\fR, and
\&\fBcenc-aes-ctr\fR
.IP "\fBfrag_duration\fR \fIduration\fR" 4
.IX Item "frag_duration duration"
Create fragments that are \fIduration\fR microseconds long.
.IP "\fBfrag_interleave\fR  \fInumber\fR" 4
.IX Item "frag_interleave number"
Interleave samples within fragments (max number of consecutive
samples, lower is tighter interleaving, but with more overhead. It is
set to \f(CW0\fR by default.
.IP "\fBfrag_size\fR \fIsize\fR" 4
.IX Item "frag_size size"
create fragments that contain up to \fIsize\fR bytes of payload data
.IP "\fBiods_audio_profile\fR \fIprofile\fR" 4
.IX Item "iods_audio_profile profile"
specify iods number for the audio profile atom (from \-1 to 255),
default is \f(CW\-1\fR
.IP "\fBiods_video_profile\fR \fIprofile\fR" 4
.IX Item "iods_video_profile profile"
specify iods number for the video profile atom (from \-1 to 255),
default is \f(CW\-1\fR
.IP "\fBism_lookahead\fR \fInum_entries\fR" 4
.IX Item "ism_lookahead num_entries"
specify number of lookahead entries for ISM files (from 0 to 255),
default is \f(CW0\fR
.IP "\fBmin_frag_duration\fR \fIduration\fR" 4
.IX Item "min_frag_duration duration"
do not create fragments that are shorter than \fIduration\fR microseconds long
.IP "\fBmoov_size\fR \fIbytes\fR" 4
.IX Item "moov_size bytes"
Reserves space for the moov atom at the beginning of the file instead of placing the
moov atom at the end. If the space reserved is insufficient, muxing will fail.
.IP "\fBmov_gamma\fR \fIgamma\fR" 4
.IX Item "mov_gamma gamma"
specify gamma value for gama atom (as a decimal number from 0 to 10),
default is \f(CW0.0\fR, must be set together with \f(CW\*(C`+ movflags\*(C'\fR
.IP "\fBmovflags\fR \fIflags\fR" 4
.IX Item "movflags flags"
Set various muxing switches. The following flags can be used:
.RS 4
.IP \fBcmaf\fR 4
.IX Item "cmaf"
write CMAF (Common Media Application Format) compatible fragmented
MP4 output
.IP \fBdash\fR 4
.IX Item "dash"
write DASH (Dynamic Adaptive Streaming over HTTP) compatible fragmented
MP4 output
.IP \fBdefault_base_moof\fR 4
.IX Item "default_base_moof"
Similarly to the \fBomit_tfhd_offset\fR flag, this flag avoids
writing the absolute base_data_offset field in tfhd atoms, but does so
by using the new default-base-is-moof flag instead. This flag is new
from 14496\-12:2012. This may make the fragments easier to parse in
certain circumstances (avoiding basing track fragment location
calculations on the implicit end of the previous track fragment).
.IP \fBdelay_moov\fR 4
.IX Item "delay_moov"
delay writing the initial moov until the first fragment is cut, or
until the first fragment flush
.IP \fBdisable_chpl\fR 4
.IX Item "disable_chpl"
Disable Nero chapter markers (chpl atom). Normally, both Nero chapters
and a QuickTime chapter track are written to the file. With this
option set, only the QuickTime chapter track will be written. Nero
chapters can cause failures when the file is reprocessed with certain
tagging programs, like mp3Tag 2.61a and iTunes 11.3, most likely other
versions are affected as well.
.IP \fBfaststart\fR 4
.IX Item "faststart"
Run a second pass moving the index (moov atom) to the beginning of the
file. This operation can take a while, and will not work in various
situations such as fragmented output, thus it is not enabled by
default.
.IP \fBfrag_custom\fR 4
.IX Item "frag_custom"
Allow the caller to manually choose when to cut fragments, by calling
\&\f(CW\*(C`av_write_frame(ctx, NULL)\*(C'\fR to write a fragment with the packets
written so far. (This is only useful with other applications
integrating libavformat, not from \fBffmpeg\fR.)
.IP \fBfrag_discont\fR 4
.IX Item "frag_discont"
signal that the next fragment is discontinuous from earlier ones
.IP \fBfrag_every_frame\fR 4
.IX Item "frag_every_frame"
fragment at every frame
.IP \fBfrag_keyframe\fR 4
.IX Item "frag_keyframe"
start a new fragment at each video keyframe
.IP \fBglobal_sidx\fR 4
.IX Item "global_sidx"
write a global sidx index at the start of the file
.IP \fBisml\fR 4
.IX Item "isml"
create a live smooth streaming feed (for pushing to a publishing point)
.IP \fBnegative_cts_offsets\fR 4
.IX Item "negative_cts_offsets"
Enables utilization of version 1 of the CTTS box, in which the CTS offsets can
be negative. This enables the initial sample to have DTS/CTS of zero, and
reduces the need for edit lists for some cases such as video tracks with
B\-frames. Additionally, eases conformance with the DASH-IF interoperability
guidelines.
.Sp
This option is implicitly set when writing \fBismv\fR (Smooth
Streaming) files.
.IP \fBomit_tfhd_offset\fR 4
.IX Item "omit_tfhd_offset"
Do not write any absolute base_data_offset in tfhd atoms. This avoids
tying fragments to absolute byte positions in the file/streams.
.IP \fBprefer_icc\fR 4
.IX Item "prefer_icc"
If writing colr atom prioritise usage of ICC profile if it exists in
stream packet side data.
.IP \fBrtphint\fR 4
.IX Item "rtphint"
add RTP hinting tracks to the output file
.IP \fBseparate_moof\fR 4
.IX Item "separate_moof"
Write a separate moof (movie fragment) atom for each track. Normally,
packets for all tracks are written in a moof atom (which is slightly
more efficient), but with this option set, the muxer writes one
moof/mdat pair for each track, making it easier to separate tracks.
.IP \fBskip_sidx\fR 4
.IX Item "skip_sidx"
Skip writing of sidx atom. When bitrate overhead due to sidx atom is
high, this option could be used for cases where sidx atom is not
mandatory. When the \fBglobal_sidx\fR flag is enabled, this option
is ignored.
.IP \fBskip_trailer\fR 4
.IX Item "skip_trailer"
skip writing the mfra/tfra/mfro trailer for fragmented files
.IP \fBuse_metadata_tags\fR 4
.IX Item "use_metadata_tags"
use mdta atom for metadata
.IP \fBwrite_colr\fR 4
.IX Item "write_colr"
write colr atom even if the color info is unspecified. This flag is
experimental, may be renamed or changed, do not use from scripts.
.IP \fBwrite_gama\fR 4
.IX Item "write_gama"
write deprecated gama atom
.IP \fBhybrid_fragmented\fR 4
.IX Item "hybrid_fragmented"
For recoverability \- write the output file as a fragmented file.
This allows the intermediate file to be read while being written
(in particular, if the writing process is aborted uncleanly). When
writing is finished, the file is converted to a regular, non-fragmented
file, which is more compatible and allows easier and quicker seeking.
.Sp
If writing is aborted, the intermediate file can manually be
remuxed to get a regular, non-fragmented file of what had been
written into the unfinished file.
.RE
.RS 4
.RE
.IP "\fBmovie_timescale\fR \fIscale\fR" 4
.IX Item "movie_timescale scale"
Set the timescale written in the movie header box (\f(CW\*(C`mvhd\*(C'\fR).
Range is 1 to INT_MAX. Default is \f(CW1000\fR.
.IP "\fBrtpflags\fR \fIflags\fR" 4
.IX Item "rtpflags flags"
Add RTP hinting tracks to the output file.
.Sp
The following flags can be used:
.RS 4
.IP \fBh264_mode0\fR 4
.IX Item "h264_mode0"
use mode 0 for H.264 in RTP
.IP \fBlatm\fR 4
.IX Item "latm"
use MP4A\-LATM packetization instead of MPEG4\-GENERIC for AAC
.IP \fBrfc2190\fR 4
.IX Item "rfc2190"
use RFC 2190 packetization instead of RFC 4629 for H.263
.IP \fBsend_bye\fR 4
.IX Item "send_bye"
send RTCP BYE packets when finishing
.IP \fBskip_rtcp\fR 4
.IX Item "skip_rtcp"
do not send RTCP sender reports
.RE
.RS 4
.RE
.IP "\fBskip_iods\fR \fIbool\fR" 4
.IX Item "skip_iods bool"
skip writing iods atom (default value is \f(CW\*(C`true\*(C'\fR)
.IP "\fBuse_editlist\fR \fIbool\fR" 4
.IX Item "use_editlist bool"
use edit list (default value is \f(CW\*(C`auto\*(C'\fR)
.IP "\fBuse_stream_ids_as_track_ids\fR \fIbool\fR" 4
.IX Item "use_stream_ids_as_track_ids bool"
use stream ids as track ids (default value is \f(CW\*(C`false\*(C'\fR)
.IP "\fBvideo_track_timescale\fR \fIscale\fR" 4
.IX Item "video_track_timescale scale"
Set the timescale used for video tracks. Range is \f(CW0\fR to INT_MAX. If
set to \f(CW0\fR, the timescale is automatically set based on the
native stream time base. Default is \f(CW0\fR.
.IP "\fBwrite_btrt\fR \fIbool\fR" 4
.IX Item "write_btrt bool"
Force or disable writing bitrate box inside stsd box of a track. The
box contains decoding buffer size (in bytes), maximum bitrate and
average bitrate for the track. The box will be skipped if none of
these values can be computed.  Default is \f(CW\-1\fR or \f(CW\*(C`auto\*(C'\fR,
which will write the box only in MP4 mode.
.IP "\fBwrite_prft\fR \fIoption\fR" 4
.IX Item "write_prft option"
Write producer time reference box (PRFT) with a specified time source for the
NTP field in the PRFT box. Set value as \fBwallclock\fR to specify timesource
as wallclock time and \fBpts\fR to specify timesource as input packets' PTS
values.
.IP "\fBwrite_tmcd\fR \fIbool\fR" 4
.IX Item "write_tmcd bool"
Specify \f(CW\*(C`on\*(C'\fR to force writing a timecode track, \f(CW\*(C`off\*(C'\fR to disable it
and \f(CW\*(C`auto\*(C'\fR to write a timecode track only for mov and mp4 output (default).
.Sp
Setting value to \fBpts\fR is applicable only for a live encoding use case,
where PTS values are set as as wallclock time at the source. For example, an
encoding use case with decklink capture source where \fBvideo_pts\fR and
\&\fBaudio_pts\fR are set to \fBabs_wallclock\fR.
.PP
\fIExamples\fR
.IX Subsection "Examples"
.IP \(bu 4
Push Smooth Streaming content in real time to a publishing point on
IIS with the \fBismv\fR muxer using \fBffmpeg\fR:
.Sp
.Vb 1
\&        ffmpeg \-re <<normal input/transcoding options>> \-movflags isml+frag_keyframe \-f ismv http://server/publishingpoint.isml/Streams(Encoder1)
.Ve
.SS a64
.IX Subsection "a64"
A64 Commodore 64 video muxer.
.PP
This muxer accepts a single \f(CW\*(C`a64_multi\*(C'\fR or \f(CW\*(C`a64_multi5\*(C'\fR
codec video stream.
.SS ac4
.IX Subsection "ac4"
Raw AC\-4 audio muxer.
.PP
This muxer accepts a single \f(CW\*(C`ac4\*(C'\fR audio stream.
.PP
\fIOptions\fR
.IX Subsection "Options"
.IP "\fBwrite_crc\fR \fIbool\fR" 4
.IX Item "write_crc bool"
when enabled, write a CRC checksum for each packet to the output,
default is \f(CW\*(C`false\*(C'\fR
.SS adts
.IX Subsection "adts"
Audio Data Transport Stream muxer.
.PP
It accepts a single AAC stream.
.PP
\fIOptions\fR
.IX Subsection "Options"
.IP "\fBwrite_id3v2\fR \fIbool\fR" 4
.IX Item "write_id3v2 bool"
Enable to write ID3v2.4 tags at the start of the stream. Default is
disabled.
.IP "\fBwrite_apetag\fR \fIbool\fR" 4
.IX Item "write_apetag bool"
Enable to write APE tags at the end of the stream. Default is
disabled.
.IP "\fBwrite_mpeg2\fR \fIbool\fR" 4
.IX Item "write_mpeg2 bool"
Enable to set MPEG version bit in the ADTS frame header to 1 which
indicates MPEG\-2. Default is 0, which indicates MPEG\-4.
.SS aea
.IX Subsection "aea"
MD STUDIO audio muxer.
.PP
This muxer accepts a single ATRAC1 audio stream with either one or two channels
and a sample rate of 44100Hz.
.PP
As AEA supports storing the track title, this muxer will also write
the title from stream's metadata to the container.
.SS aiff
.IX Subsection "aiff"
Audio Interchange File Format muxer.
.PP
\fIOptions\fR
.IX Subsection "Options"
.IP "\fBwrite_id3v2\fR \fIbool\fR" 4
.IX Item "write_id3v2 bool"
Enable ID3v2 tags writing when set to 1. Default is 0 (disabled).
.IP "\fBid3v2_version\fR \fIbool\fR" 4
.IX Item "id3v2_version bool"
Select ID3v2 version to write. Currently only version 3 and 4 (aka.
ID3v2.3 and ID3v2.4) are supported. The default is version 4.
.SS alp
.IX Subsection "alp"
High Voltage Software's Lego Racers game audio muxer.
.PP
It accepts a single ADPCM_IMA_ALP stream with no more than 2 channels
and a sample rate not greater than 44100 Hz.
.PP
Extensions: \f(CW\*(C`tun\*(C'\fR, \f(CW\*(C`pcm\*(C'\fR
.PP
\fIOptions\fR
.IX Subsection "Options"
.IP "\fBtype\fR \fItype\fR" 4
.IX Item "type type"
Set file type.
.Sp
\&\fItype\fR accepts the following values:
.RS 4
.IP \fBtun\fR 4
.IX Item "tun"
Set file type as music. Must have a sample rate of 22050 Hz.
.IP \fBpcm\fR 4
.IX Item "pcm"
Set file type as sfx.
.IP \fBauto\fR 4
.IX Item "auto"
Set file type as per output file extension. \f(CW\*(C`.pcm\*(C'\fR results in
type \f(CW\*(C`pcm\*(C'\fR else type \f(CW\*(C`tun\*(C'\fR is set. \fI(default)\fR
.RE
.RS 4
.RE
.SS amr
.IX Subsection "amr"
3GPP AMR (Adaptive Multi-Rate) audio muxer.
.PP
It accepts a single audio stream containing an AMR NB stream.
.SS amv
.IX Subsection "amv"
AMV (Actions Media Video) format muxer.
.SS apm
.IX Subsection "apm"
Ubisoft Rayman 2 APM audio muxer.
.PP
It accepts a single ADPCM IMA APM audio stream.
.SS apng
.IX Subsection "apng"
Animated Portable Network Graphics muxer.
.PP
It accepts a single APNG video stream.
.PP
\fIOptions\fR
.IX Subsection "Options"
.IP "\fBfinal_delay\fR \fIdelay\fR" 4
.IX Item "final_delay delay"
Force a delay expressed in seconds after the last frame of each
repetition. Default value is \f(CW0.0\fR.
.IP "\fBplays\fR \fIrepetitions\fR" 4
.IX Item "plays repetitions"
specify how many times to play the content, \f(CW0\fR causes an infinite
loop, with \f(CW1\fR there is no loop
.PP
\fIExamples\fR
.IX Subsection "Examples"
.IP \(bu 4
Use \fBffmpeg\fR to generate an APNG output with 2 repetitions,
and with a delay of half a second after the first repetition:
.Sp
.Vb 1
\&        ffmpeg \-i INPUT \-final_delay 0.5 \-plays 2 out.apng
.Ve
.SS argo_asf
.IX Subsection "argo_asf"
Argonaut Games ASF audio muxer.
.PP
It accepts a single ADPCM audio stream.
.PP
\fIOptions\fR
.IX Subsection "Options"
.IP "\fBversion_major\fR \fIversion\fR" 4
.IX Item "version_major version"
override file major version, specified as an integer, default value is
\&\f(CW2\fR
.IP "\fBversion_minor\fR \fIversion\fR" 4
.IX Item "version_minor version"
override file minor version, specified as an integer, default value is
\&\f(CW1\fR
.IP "\fBname\fR \fIname\fR" 4
.IX Item "name name"
Embed file name into file, if not specified use the output file
name. The name is truncated to 8 characters.
.SS argo_cvg
.IX Subsection "argo_cvg"
Argonaut Games CVG audio muxer.
.PP
It accepts a single one-channel ADPCM 22050Hz audio stream.
.PP
The \fBloop\fR and \fBreverb\fR options set the corresponding
flags in the header which can be later retrieved to process the audio
stream accordingly.
.PP
\fIOptions\fR
.IX Subsection "Options"
.IP "\fBskip_rate_check\fR \fIbool\fR" 4
.IX Item "skip_rate_check bool"
skip sample rate check (default is \f(CW\*(C`false\*(C'\fR)
.IP "\fBloop\fR \fIbool\fR" 4
.IX Item "loop bool"
set loop flag (default is \f(CW\*(C`false\*(C'\fR)
.IP "\fBreverb\fR \fIboolean\fR" 4
.IX Item "reverb boolean"
set reverb flag (default is \f(CW\*(C`true\*(C'\fR)
.SS "asf, asf_stream"
.IX Subsection "asf, asf_stream"
Advanced / Active Systems (or Streaming) Format audio muxer.
.PP
The \fBasf_stream\fR variant should be selected for streaming.
.PP
Note that Windows Media Audio (wma) and Windows Media Video (wmv) use this
muxer too.
.PP
\fIOptions\fR
.IX Subsection "Options"
.IP "\fBpacket_size\fR \fIsize\fR" 4
.IX Item "packet_size size"
Set the muxer packet size as a number of bytes. By tuning this setting
you may reduce data fragmentation or muxer overhead depending on your
source. Default value is \f(CW3200\fR, minimum is \f(CW100\fR, maximum
is \f(CW\*(C`64Ki\*(C'\fR.
.SS ass
.IX Subsection "ass"
ASS/SSA (SubStation Alpha) subtitles muxer.
.PP
It accepts a single ASS subtitles stream.
.PP
\fIOptions\fR
.IX Subsection "Options"
.IP "\fBignore_readorder\fR \fIbool\fR" 4
.IX Item "ignore_readorder bool"
Write dialogue events immediately, even if they are out-of-order,
default is \f(CW\*(C`false\*(C'\fR, otherwise they are cached until the expected
time event is found.
.SS ast
.IX Subsection "ast"
AST (Audio Stream) muxer.
.PP
This format is used to play audio on some Nintendo Wii games.
.PP
It accepts a single audio stream.
.PP
The \fBloopstart\fR and \fBloopend\fR options can be used to
define a section of the file to loop for players honoring such
options.
.PP
\fIOptions\fR
.IX Subsection "Options"
.IP "\fBloopstart\fR \fIstart\fR" 4
.IX Item "loopstart start"
Specify loop start position expressesd in milliseconds, from \f(CW\-1\fR
to \f(CW\*(C`INT_MAX\*(C'\fR, in case \f(CW\-1\fR is set then no loop is specified
(default \-1) and the \fBloopend\fR value is ignored.
.IP "\fBloopend\fR \fIend\fR" 4
.IX Item "loopend end"
Specify loop end position expressed in milliseconds, from \f(CW0\fR to
\&\f(CW\*(C`INT_MAX\*(C'\fR, default is \f(CW0\fR, in case \f(CW0\fR is set it
assumes the total stream duration.
.SS au
.IX Subsection "au"
SUN AU audio muxer.
.PP
It accepts a single audio stream.
.SS avi
.IX Subsection "avi"
Audio Video Interleaved muxer.
.PP
AVI is a proprietary format developed by Microsoft, and later formally specified
through the Open DML specification.
.PP
Because of differences in players implementations, it might be required to set
some options to make sure that the generated output can be correctly played by
the target player.
.PP
\fIOptions\fR
.IX Subsection "Options"
.IP "\fBflipped_raw_rgb\fR \fIbool\fR" 4
.IX Item "flipped_raw_rgb bool"
If set to \f(CW\*(C`true\*(C'\fR, store positive height for raw RGB bitmaps, which
indicates bitmap is stored bottom-up. Note that this option does not flip the
bitmap which has to be done manually beforehand, e.g. by using the \fBvflip\fR
filter. Default is \f(CW\*(C`false\*(C'\fR and indicates bitmap is stored top down.
.IP "\fBreserve_index_space\fR \fIsize\fR" 4
.IX Item "reserve_index_space size"
Reserve the specified amount of bytes for the OpenDML master index of each
stream within the file header. By default additional master indexes are
embedded within the data packets if there is no space left in the first master
index and are linked together as a chain of indexes. This index structure can
cause problems for some use cases, e.g. third-party software strictly relying
on the OpenDML index specification or when file seeking is slow. Reserving
enough index space in the file header avoids these problems.
.Sp
The required index space depends on the output file size and should be about 16
bytes per gigabyte. When this option is omitted or set to zero the necessary
index space is guessed.
.Sp
Default value is \f(CW0\fR.
.IP "\fBwrite_channel_mask\fR \fIbool\fR" 4
.IX Item "write_channel_mask bool"
Write the channel layout mask into the audio stream header.
.Sp
This option is enabled by default. Disabling the channel mask can be useful in
specific scenarios, e.g. when merging multiple audio streams into one for
compatibility with software that only supports a single audio stream in AVI
(see \fBthe "amerge" section in the ffmpeg-filters manual\fR).
.SS avif
.IX Subsection "avif"
AV1 (Alliance for Open Media Video codec 1) image format muxer.
.PP
This muxers stores images encoded using the AV1 codec.
.PP
It accepts one or two video streams. In case two video streams are
provided, the second one shall contain a single plane storing the
alpha mask.
.PP
In case more than one image is provided, the generated output is
considered an animated AVIF and the number of loops can be specified
with the \fBloop\fR option.
.PP
This is based on the specification by Alliance for Open Media at url
<\fBhttps://aomediacodec.github.io/av1\-avif\fR>.
.PP
\fIOptions\fR
.IX Subsection "Options"
.IP "\fBloop\fR \fIcount\fR" 4
.IX Item "loop count"
number of times to loop an animated AVIF, \f(CW0\fR specify an infinite
loop, default is \f(CW0\fR
.IP "\fBmovie_timescale\fR \fItimescale\fR" 4
.IX Item "movie_timescale timescale"
Set the timescale written in the movie header box (\f(CW\*(C`mvhd\*(C'\fR).
Range is 1 to INT_MAX. Default is \f(CW1000\fR.
.SS avm2
.IX Subsection "avm2"
ShockWave Flash (SWF) / ActionScript Virtual Machine 2 (AVM2) format muxer.
.PP
It accepts one audio stream, one video stream, or both.
.SS bit
.IX Subsection "bit"
G.729 (.bit) file format muxer.
.PP
It accepts a single G.729 audio stream.
.SS caf
.IX Subsection "caf"
Apple CAF (Core Audio Format) muxer.
.PP
It accepts a single audio stream.
.SS codec2
.IX Subsection "codec2"
Codec2 audio audio muxer.
.PP
It accepts a single codec2 audio stream.
.SS chromaprint
.IX Subsection "chromaprint"
Chromaprint fingerprinter muxers.
.PP
To enable compilation of this filter you need to configure FFmpeg with
\&\f(CW\*(C`\-\-enable\-chromaprint\*(C'\fR.
.PP
This muxer feeds audio data to the Chromaprint library, which
generates a fingerprint for the provided audio data. See:
<\fBhttps://acoustid.org/chromaprint\fR>
.PP
It takes a single signed native-endian 16\-bit raw audio stream of at
most 2 channels.
.PP
\fIOptions\fR
.IX Subsection "Options"
.IP "\fBalgorithm\fR \fIversion\fR" 4
.IX Item "algorithm version"
Select version of algorithm to fingerprint with. Range is \f(CW0\fR to
\&\f(CW4\fR. Version \f(CW3\fR enables silence detection. Default is \f(CW1\fR.
.IP "\fBfp_format\fR \fIformat\fR" 4
.IX Item "fp_format format"
Format to output the fingerprint as. Accepts the following options:
.RS 4
.IP \fBbase64\fR 4
.IX Item "base64"
Base64 compressed fingerprint \fI(default)\fR
.IP \fBcompressed\fR 4
.IX Item "compressed"
Binary compressed fingerprint
.IP \fBraw\fR 4
.IX Item "raw"
Binary raw fingerprint
.RE
.RS 4
.RE
.IP "\fBsilence_threshold\fR \fIthreshold\fR" 4
.IX Item "silence_threshold threshold"
Threshold for detecting silence. Range is from \f(CW\-1\fR to
\&\f(CW32767\fR, where \f(CW\-1\fR disables silence detection. Silence
detection can only be used with version \f(CW3\fR of the algorithm.
.Sp
Silence detection must be disabled for use with the AcoustID
service. Default is \f(CW\-1\fR.
.SS crc
.IX Subsection "crc"
CRC (Cyclic Redundancy Check) muxer.
.PP
This muxer computes and prints the Adler\-32 CRC of all the input audio
and video frames. By default audio frames are converted to signed
16\-bit raw audio and video frames to raw video before computing the
CRC.
.PP
The output of the muxer consists of a single line of the form:
CRC=0x\fICRC\fR, where \fICRC\fR is a hexadecimal number 0\-padded to
8 digits containing the CRC for all the decoded input frames.
.PP
See also the \fBframecrc\fR muxer.
.PP
\fIExamples\fR
.IX Subsection "Examples"
.IP \(bu 4
Use \fBffmpeg\fR to compute the CRC of the input, and store it in
the file \fIout.crc\fR:
.Sp
.Vb 1
\&        ffmpeg \-i INPUT \-f crc out.crc
.Ve
.IP \(bu 4
Use \fBffmpeg\fR to print the CRC to stdout with the command:
.Sp
.Vb 1
\&        ffmpeg \-i INPUT \-f crc \-
.Ve
.IP \(bu 4
You can select the output format of each frame with \fBffmpeg\fR by
specifying the audio and video codec and format. For example, to
compute the CRC of the input audio converted to PCM unsigned 8\-bit
and the input video converted to MPEG\-2 video, use the command:
.Sp
.Vb 1
\&        ffmpeg \-i INPUT \-c:a pcm_u8 \-c:v mpeg2video \-f crc \-
.Ve
.SS dash
.IX Subsection "dash"
Dynamic Adaptive Streaming over HTTP (DASH) muxer.
.PP
This muxer creates segments and manifest files according to the
MPEG-DASH standard ISO/IEC 23009\-1:2014 and following standard
updates.
.PP
For more information see:
.IP \(bu 4
ISO DASH Specification: <\fBhttp://standards.iso.org/ittf/PubliclyAvailableStandards/c065274_ISO_IEC_23009\-1_2014.zip\fR>
.IP \(bu 4
WebM DASH Specification: <\fBhttps://sites.google.com/a/webmproject.org/wiki/adaptive\-streaming/webm\-dash\-specification\fR>
.PP
This muxer creates an MPD (Media Presentation Description) manifest
file and segment files for each stream. Segment files are placed in
the same directory of the MPD manifest file.
.PP
The segment filename might contain pre-defined identifiers used in the
manifest \f(CW\*(C`SegmentTemplate\*(C'\fR section as defined in section
*******.4 of the standard.
.PP
Available identifiers are \f(CW\*(C`$RepresentationID$\*(C'\fR, \f(CW\*(C`$Number$\*(C'\fR,
\&\f(CW\*(C`$Bandwidth$\*(C'\fR, and \f(CW\*(C`$Time$\*(C'\fR. In addition to the standard
identifiers, an ffmpeg-specific \f(CW\*(C`$ext$\*(C'\fR identifier is also
supported. When specified, \fBffmpeg\fR will replace \f(CW\*(C`$ext$\*(C'\fR
in the file name with muxing format's extensions such as \f(CW\*(C`mp4\*(C'\fR,
\&\f(CW\*(C`webm\*(C'\fR etc.
.PP
\fIOptions\fR
.IX Subsection "Options"
.IP "\fBadaptation_sets\fR \fIadaptation_sets\fR" 4
.IX Item "adaptation_sets adaptation_sets"
Assign streams to adaptation sets, specified in the MPD manifest
\&\f(CW\*(C`AdaptationSets\*(C'\fR section.
.Sp
An adaptation set contains a set of one or more streams accessed as a
single subset, e.g. corresponding streams encoded at different size
selectable by the user depending on the available bandwidth, or to
different audio streams with a different language.
.Sp
Each adaptation set is specified with the syntax:
.Sp
.Vb 1
\&        id=<index>,streams=<streams>
.Ve
.Sp
where \fIindex\fR must be a numerical index, and \fIstreams\fR is a
sequence of \f(CW\*(C`,\*(C'\fR\-separated stream indices. Multiple adaptation
sets can be specified, separated by spaces.
.Sp
To map all video (or audio) streams to an adaptation set, \f(CW\*(C`v\*(C'\fR (or
\&\f(CW\*(C`a\*(C'\fR) can be used as stream identifier instead of IDs.
.Sp
When no assignment is defined, this defaults to an adaptation set for
each stream.
.Sp
The following optional fields can also be specified:
.RS 4
.IP \fBdescriptor\fR 4
.IX Item "descriptor"
Define the descriptor as defined by ISO/IEC 23009\-1:2014/Amd.2:2015.
.Sp
For example:
.Sp
.Vb 1
\&        <SupplementalProperty schemeIdUri=\e"urn:mpeg:dash:srd:2014\e" value=\e"0,0,0,1,1,2,2\e"/>
.Ve
.Sp
The descriptor string should be a self-closing XML tag.
.IP \fBfrag_duration\fR 4
.IX Item "frag_duration"
Override the global fragment duration specified with the
\&\fBfrag_duration\fR option.
.IP \fBfrag_type\fR 4
.IX Item "frag_type"
Override the global fragment type specified with the
\&\fBfrag_type\fR option.
.IP \fBseg_duration\fR 4
.IX Item "seg_duration"
Override the global segment duration specified with the
\&\fBseg_duration\fR option.
.IP \fBtrick_id\fR 4
.IX Item "trick_id"
Mark an adaptation set as containing streams meant to be used for
Trick Mode for the referenced adaptation set.
.RE
.RS 4
.Sp
A few examples of possible values for the \fBadaptation_sets\fR
option follow:
.Sp
.Vb 1
\&        id=0,seg_duration=2,frag_duration=1,frag_type=duration,streams=v id=1,seg_duration=2,frag_type=none,streams=a
\&
\&
\&        
\&        id=0,seg_duration=2,frag_type=none,streams=0 id=1,seg_duration=10,frag_type=none,trick_id=0,streams=1
.Ve
.RE
.IP "\fBdash_segment_type\fR \fItype\fR" 4
.IX Item "dash_segment_type type"
Set DASH segment files type.
.Sp
Possible values:
.RS 4
.IP \fBauto\fR 4
.IX Item "auto"
The dash segment files format will be selected based on the stream
codec. This is the default mode.
.IP \fBmp4\fR 4
.IX Item "mp4"
the dash segment files will be in ISOBMFF/MP4 format
.IP \fBwebm\fR 4
.IX Item "webm"
the dash segment files will be in WebM format
.RE
.RS 4
.RE
.IP "\fBextra_window_size\fR \fIsize\fR" 4
.IX Item "extra_window_size size"
Set the maximum number of segments kept outside of the manifest before
removing from disk.
.IP "\fBformat_options\fR \fIoptions_list\fR" 4
.IX Item "format_options options_list"
Set container format (mp4/webm) options using a \f(CW\*(C`:\*(C'\fR\-separated list of
key=value parameters. Values containing \f(CW\*(C`:\*(C'\fR special characters must be
escaped.
.IP "\fBfrag_duration\fR \fIduration\fR" 4
.IX Item "frag_duration duration"
Set the length in seconds of fragments within segments, fractional
value can also be set.
.IP "\fBfrag_type\fR \fItype\fR" 4
.IX Item "frag_type type"
Set the type of interval for fragmentation.
.Sp
Possible values:
.RS 4
.IP \fBauto\fR 4
.IX Item "auto"
set one fragment per segment
.IP \fBevery_frame\fR 4
.IX Item "every_frame"
fragment at every frame
.IP \fBduration\fR 4
.IX Item "duration"
fragment at specific time intervals
.IP \fBpframes\fR 4
.IX Item "pframes"
fragment at keyframes and following P\-Frame reordering (Video only,
experimental)
.RE
.RS 4
.RE
.IP "\fBglobal_sidx\fR \fIbool\fR" 4
.IX Item "global_sidx bool"
Write global \f(CW\*(C`SIDX\*(C'\fR atom. Applicable only for single file, mp4
output, non-streaming mode.
.IP "\fBhls_master_name\fR \fIfile_name\fR" 4
.IX Item "hls_master_name file_name"
HLS master playlist name. Default is \fImaster.m3u8\fR.
.IP "\fBhls_playlist\fR \fIbool\fR" 4
.IX Item "hls_playlist bool"
Generate HLS playlist files. The master playlist is generated with
filename specified by the \fBhls_master_name\fR option. One media
playlist file is generated for each stream with filenames
\&\fImedia_0.m3u8\fR, \fImedia_1.m3u8\fR, etc.
.IP "\fBhttp_opts\fR \fIhttp_opts\fR" 4
.IX Item "http_opts http_opts"
Specify a list of \f(CW\*(C`:\*(C'\fR\-separated key=value options to pass to the
underlying HTTP protocol. Applicable only for HTTP output.
.IP "\fBhttp_persistent\fR \fIbool\fR" 4
.IX Item "http_persistent bool"
Use persistent HTTP connections. Applicable only for HTTP output.
.IP "\fBhttp_user_agent\fR \fIuser_agent\fR" 4
.IX Item "http_user_agent user_agent"
Override User-Agent field in HTTP header. Applicable only for HTTP
output.
.IP "\fBignore_io_errors\fR \fIbool\fR" 4
.IX Item "ignore_io_errors bool"
Ignore IO errors during open and write. Useful for long-duration runs
with network output. This is disabled by default.
.IP "\fBindex_correction\fR \fIbool\fR" 4
.IX Item "index_correction bool"
Enable or disable segment index correction logic. Applicable only when
\&\fBuse_template\fR is enabled and \fBuse_timeline\fR is
disabled. This is disabled by default.
.Sp
When enabled, the logic monitors the flow of segment indexes. If a
streams's segment index value is not at the expected real time
position, then the logic corrects that index value.
.Sp
Typically this logic is needed in live streaming use cases. The
network bandwidth fluctuations are common during long run
streaming. Each fluctuation can cause the segment indexes fall behind
the expected real time position.
.IP "\fBinit_seg_name\fR \fIinit_name\fR" 4
.IX Item "init_seg_name init_name"
DASH-templated name to use for the initialization segment. Default is
\&\f(CW\*(C`init\-stream$RepresentationID$.$ext$\*(C'\fR. \f(CW\*(C`$ext$\*(C'\fR is replaced
with the file name extension specific for the segment format.
.IP "\fBldash\fR \fIbool\fR" 4
.IX Item "ldash bool"
Enable Low-latency Dash by constraining the presence and values of
some elements. This is disabled by default.
.IP "\fBlhls\fR \fIbool\fR" 4
.IX Item "lhls bool"
Enable Low-latency HLS (LHLS). Add \f(CW\*(C`#EXT\-X\-PREFETCH\*(C'\fR tag with
current segment's URI. hls.js player folks are trying to standardize
an open LHLS spec. The draft spec is available at
<\fBhttps://github.com/video\-dev/hlsjs\-rfcs/blob/lhls\-spec/proposals/0001\-lhls.md\fR>.
.Sp
This option tries to comply with the above open spec. It enables
\&\fBstreaming\fR and \fBhls_playlist\fR options automatically.
This is an experimental feature.
.Sp
Note: This is not Apple's version LHLS. See
<\fBhttps://datatracker.ietf.org/doc/html/draft\-pantos\-hls\-rfc8216bis\fR>
.IP "\fBmaster_m3u8_publish_rate\fR \fIsegment_intervals_count\fR" 4
.IX Item "master_m3u8_publish_rate segment_intervals_count"
Publish master playlist repeatedly every after specified number of
segment intervals.
.IP "\fBmax_playback_rate\fR \fIrate\fR" 4
.IX Item "max_playback_rate rate"
Set the maximum playback rate indicated as appropriate for the
purposes of automatically adjusting playback latency and buffer
occupancy during normal playback by clients.
.IP "\fBmedia_seg_name\fR \fIsegment_name\fR" 4
.IX Item "media_seg_name segment_name"
DASH-templated name to use for the media segments. Default is
\&\f(CW\*(C`chunk\-stream$RepresentationID$\-$Number%05d$.$ext$\*(C'\fR. \f(CW\*(C`$ext$\*(C'\fR
is replaced with the file name extension specific for the segment
format.
.IP "\fBmethod\fR \fImethod\fR" 4
.IX Item "method method"
Use the given HTTP method to create output files. Generally set to \f(CW\*(C`PUT\*(C'\fR
or \f(CW\*(C`POST\*(C'\fR.
.IP "\fBmin_playback_rate\fR \fIrate\fR" 4
.IX Item "min_playback_rate rate"
Set the minimum playback rate indicated as appropriate for the
purposes of automatically adjusting playback latency and buffer
occupancy during normal playback by clients.
.IP "\fBmpd_profile\fR \fIflags\fR" 4
.IX Item "mpd_profile flags"
Set one or more MPD manifest profiles.
.Sp
Possible values:
.RS 4
.IP \fBdash\fR 4
.IX Item "dash"
MPEG-DASH ISO Base media file format live profile
.IP \fBdvb_dash\fR 4
.IX Item "dvb_dash"
DVB-DASH profile
.RE
.RS 4
.Sp
Default value is \f(CW\*(C`dash\*(C'\fR.
.RE
.IP "\fBremove_at_exit\fR \fIbool\fR" 4
.IX Item "remove_at_exit bool"
Enable or disable removal of all segments when finished. This is
disabled by default.
.IP "\fBseg_duration\fR \fIduration\fR" 4
.IX Item "seg_duration duration"
Set the segment length in seconds (fractional value can be set). The
value is treated as average segment duration when the
\&\fBuse_template\fR option is enabled and the \fBuse_timeline\fR
option is disabled and as minimum segment duration for all the other
use cases.
.Sp
Default value is \f(CW5\fR.
.IP "\fBsingle_file\fR \fIbool\fR" 4
.IX Item "single_file bool"
Enable or disable storing all segments in one file, accessed using
byte ranges. This is disabled by default.
.Sp
The name of the single file can be specified with the
\&\fBsingle_file_name\fR option, if not specified assume the basename
of the manifest file with the output format extension.
.IP "\fBsingle_file_name\fR \fIfile_name\fR" 4
.IX Item "single_file_name file_name"
DASH-templated name to use for the manifest \f(CW\*(C`baseURL\*(C'\fR
element. Imply that the \fBsingle_file\fR option is set to
\&\fItrue\fR. In the template, \f(CW\*(C`$ext$\*(C'\fR is replaced with the file
name extension specific for the segment format.
.IP "\fBstreaming\fR \fIbool\fR" 4
.IX Item "streaming bool"
Enable or disable chunk streaming mode of output. In chunk streaming
mode, each frame will be a \f(CW\*(C`moof\*(C'\fR fragment which forms a
chunk. This is disabled by default.
.IP "\fBtarget_latency\fR \fItarget_latency\fR" 4
.IX Item "target_latency target_latency"
Set an intended target latency in seconds for serving (fractional
value can be set). Applicable only when the \fBstreaming\fR and
\&\fBwrite_prft\fR options are enabled. This is an informative fields
clients can use to measure the latency of the service.
.IP "\fBtimeout\fR \fItimeout\fR" 4
.IX Item "timeout timeout"
Set timeout for socket I/O operations expressed in seconds (fractional
value can be set). Applicable only for HTTP output.
.IP "\fBupdate_period\fR \fIperiod\fR" 4
.IX Item "update_period period"
Set the MPD update period, for dynamic content. The unit is
second. If set to \f(CW0\fR, the period is automatically computed.
.Sp
Default value is \f(CW0\fR.
.IP "\fBuse_template\fR \fIbool\fR" 4
.IX Item "use_template bool"
Enable or disable use of \f(CW\*(C`SegmentTemplate\*(C'\fR instead of
\&\f(CW\*(C`SegmentList\*(C'\fR in the manifest. This is enabled by default.
.IP "\fBuse_timeline\fR \fIbool\fR" 4
.IX Item "use_timeline bool"
Enable or disable use of \f(CW\*(C`SegmentTimeline\*(C'\fR within the
\&\f(CW\*(C`SegmentTemplate\*(C'\fR manifest section. This is enabled by default.
.IP "\fButc_timing_url\fR \fIurl\fR" 4
.IX Item "utc_timing_url url"
URL of the page that will return the UTC timestamp in ISO
format, for example \f(CW\*(C`https://time.akamai.com/?iso\*(C'\fR
.IP "\fBwindow_size\fR \fIsize\fR" 4
.IX Item "window_size size"
Set the maximum number of segments kept in the manifest, discard the
oldest one. This is useful for live streaming.
.Sp
If the value is \f(CW0\fR, all segments are kept in the
manifest. Default value is \f(CW0\fR.
.IP "\fBwrite_prft\fR \fIwrite_prft\fR" 4
.IX Item "write_prft write_prft"
Write Producer Reference Time elements on supported streams. This also
enables writing prft boxes in the underlying muxer. Applicable only
when the \fIutc_url\fR option is enabled. It is set to \fIauto\fR by
default, in which case the muxer will attempt to enable it only in
modes that require it.
.PP
\fIExample\fR
.IX Subsection "Example"
.PP
Generate a DASH output reading from an input source in realtime using
\&\fBffmpeg\fR.
.PP
Two multimedia streams are generated from the input file, both
containing a video stream encoded through \fBlibx264\fR, and an audio
stream encoded with \fBlibfdk_aac\fR. The first multimedia stream
contains video with a bitrate of 800k and audio at the default rate,
the second with video scaled to 320x170 pixels at 300k and audio
resampled at 22005 Hz.
.PP
The \fBwindow_size\fR option keeps only the latest 5 segments with
the default duration of 5 seconds.
.PP
.Vb 7
\&        ffmpeg \-re \-i <input> \-map 0 \-map 0 \-c:a libfdk_aac \-c:v libx264 \e
\&        \-b:v:0 800k \-profile:v:0 main \e
\&        \-b:v:1 300k \-s:v:1 320x170 \-profile:v:1 baseline \-ar:a:1 22050 \e
\&        \-bf 1 \-keyint_min 120 \-g 120 \-sc_threshold 0 \-b_strategy 0 \e
\&        \-use_timeline 1 \-use_template 1 \-window_size 5 \e
\&        \-adaptation_sets "id=0,streams=v id=1,streams=a" \e
\&        \-f dash /path/to/out.mpd
.Ve
.SS daud
.IX Subsection "daud"
D\-Cinema audio muxer.
.PP
It accepts a single 6\-channels audio stream resampled at 96000 Hz
encoded with the \fBpcm_24daud\fR codec.
.PP
\fIExample\fR
.IX Subsection "Example"
.PP
Use \fBffmpeg\fR to mux input audio to a \fB5.1\fR channel layout
resampled at 96000Hz:
.PP
.Vb 1
\&        ffmpeg \-i INPUT \-af aresample=96000,pan=5.1 slow.302
.Ve
.PP
For ffmpeg versions before 7.0 you might have to use the \fBasetnsamples\fR
filter to limit the muxed packet size, because this format does not support
muxing packets larger than 65535 bytes (3640 samples). For newer ffmpeg
versions audio is automatically packetized to 36000 byte (2000 sample) packets.
.SS dv
.IX Subsection "dv"
DV (Digital Video) muxer.
.PP
It accepts exactly one \fBdvvideo\fR video stream and at most two
\&\fBpcm_s16\fR audio streams. More constraints are defined by the
property of the video, which must correspond to a DV video supported
profile, and on the framerate.
.PP
\fIExample\fR
.IX Subsection "Example"
.PP
Use \fBffmpeg\fR to convert the input:
.PP
.Vb 1
\&        ffmpeg \-i INPUT \-s:v 720x480 \-pix_fmt yuv411p \-r 29.97 \-ac 2 \-ar 48000 \-y out.dv
.Ve
.SS ffmetadata
.IX Subsection "ffmetadata"
FFmpeg metadata muxer.
.PP
This muxer writes the streams metadata in the \fBffmetadata\fR
format.
.PP
See \fBthe Metadata chapter\fR for
information about the format.
.PP
\fIExample\fR
.IX Subsection "Example"
.PP
Use \fBffmpeg\fR to extract metadata from an input file to a \fImetadata.ffmeta\fR
file in \fBffmetadata\fR format:
.PP
.Vb 1
\&        ffmpeg \-i INPUT \-f ffmetadata metadata.ffmeta
.Ve
.SS fifo
.IX Subsection "fifo"
FIFO (First-In First-Out) muxer.
.PP
The \fBfifo\fR pseudo-muxer allows the separation of encoding and
muxing by using a first-in-first-out queue and running the actual muxer
in a separate thread.
.PP
This is especially useful in combination with
the \fBtee\fR muxer and can be used to send data to several
destinations with different reliability/writing speed/latency.
.PP
The target muxer is either selected from the output name or specified
through the \fBfifo_format\fR option.
.PP
The behavior of the \fBfifo\fR muxer if the queue fills up or if the
output fails (e.g. if a packet cannot be written to the output) is
selectable:
.IP \(bu 4
Output can be transparently restarted with configurable delay between
retries based on real time or time of the processed stream.
.IP \(bu 4
Encoding can be blocked during temporary failure, or continue transparently
dropping packets in case the FIFO queue fills up.
.PP
API users should be aware that callback functions
(\f(CW\*(C`interrupt_callback\*(C'\fR, \f(CW\*(C`io_open\*(C'\fR and \f(CW\*(C`io_close\*(C'\fR) used
within its \f(CW\*(C`AVFormatContext\*(C'\fR must be thread-safe.
.PP
\fIOptions\fR
.IX Subsection "Options"
.IP "\fBattempt_recovery\fR \fIbool\fR" 4
.IX Item "attempt_recovery bool"
If failure occurs, attempt to recover the output. This is especially
useful when used with network output, since it makes it possible to
restart streaming transparently. By default this option is set to
\&\f(CW\*(C`false\*(C'\fR.
.IP "\fBdrop_pkts_on_overflow\fR \fIbool\fR" 4
.IX Item "drop_pkts_on_overflow bool"
If set to \f(CW\*(C`true\*(C'\fR, in case the fifo queue fills up, packets will
be dropped rather than blocking the encoder. This makes it possible to
continue streaming without delaying the input, at the cost of omitting
part of the stream. By default this option is set to \f(CW\*(C`false\*(C'\fR, so in
such cases the encoder will be blocked until the muxer processes some
of the packets and none of them is lost.
.IP "\fBfifo_format\fR \fIformat_name\fR" 4
.IX Item "fifo_format format_name"
Specify the format name. Useful if it cannot be guessed from the
output name suffix.
.IP "\fBformat_opts\fR \fIoptions\fR" 4
.IX Item "format_opts options"
Specify format options for the underlying muxer. Muxer options can be
specified as a list of \fIkey\fR=\fIvalue\fR pairs separated by ':'.
.IP "\fBmax_recovery_attempts\fR \fIcount\fR" 4
.IX Item "max_recovery_attempts count"
Set maximum number of successive unsuccessful recovery attempts after
which the output fails permanently. By default this option is set to
\&\f(CW0\fR (unlimited).
.IP "\fBqueue_size\fR \fIsize\fR" 4
.IX Item "queue_size size"
Specify size of the queue as a number of packets. Default value is
\&\f(CW60\fR.
.IP "\fBrecover_any_error\fR \fIbool\fR" 4
.IX Item "recover_any_error bool"
If set to \f(CW\*(C`true\*(C'\fR, recovery will be attempted regardless of type
of the error causing the failure. By default this option is set to
\&\f(CW\*(C`false\*(C'\fR and in case of certain (usually permanent) errors the
recovery is not attempted even when the \fBattempt_recovery\fR
option is set to \f(CW\*(C`true\*(C'\fR.
.IP "\fBrecovery_wait_streamtime\fR \fIbool\fR" 4
.IX Item "recovery_wait_streamtime bool"
If set to \f(CW\*(C`false\*(C'\fR, the real time is used when waiting for the
recovery attempt (i.e. the recovery will be attempted after the time
specified by the \fBrecovery_wait_time\fR option).
.Sp
If set to \f(CW\*(C`true\*(C'\fR, the time of the processed stream is taken into
account instead (i.e. the recovery will be attempted after discarding
the packets corresponding to the \fBrecovery_wait_time\fR option).
.Sp
By default this option is set to \f(CW\*(C`false\*(C'\fR.
.IP "\fBrecovery_wait_time\fR \fIduration\fR" 4
.IX Item "recovery_wait_time duration"
Specify waiting time in seconds before the next recovery attempt after
previous unsuccessful recovery attempt. Default value is \f(CW5\fR.
.IP "\fBrestart_with_keyframe\fR \fIbool\fR" 4
.IX Item "restart_with_keyframe bool"
Specify whether to wait for the keyframe after recovering from
queue overflow or failure. This option is set to \f(CW\*(C`false\*(C'\fR by default.
.IP "\fBtimeshift\fR \fIduration\fR" 4
.IX Item "timeshift duration"
Buffer the specified amount of packets and delay writing the
output. Note that the value of the \fBqueue_size\fR option must be
big enough to store the packets for timeshift. At the end of the input
the fifo buffer is flushed at realtime speed.
.PP
\fIExample\fR
.IX Subsection "Example"
.PP
Use \fBffmpeg\fR to stream to an RTMP server, continue processing
the stream at real-time rate even in case of temporary failure
(network outage) and attempt to recover streaming every second
indefinitely:
.PP
.Vb 3
\&        ffmpeg \-re \-i ... \-c:v libx264 \-c:a aac \-f fifo \-fifo_format flv \e
\&          \-drop_pkts_on_overflow 1 \-attempt_recovery 1 \-recovery_wait_time 1 \e
\&          \-map 0:v \-map 0:a rtmp://example.com/live/stream_name
.Ve
.SS film_cpk
.IX Subsection "film_cpk"
Sega film (.cpk) muxer.
.PP
This format was used as internal format for several Sega games.
.PP
For more information regarding the Sega film file format, visit
<\fBhttp://wiki.multimedia.cx/index.php?title=Sega_FILM\fR>.
.PP
It accepts at maximum one \fBcinepak\fR or raw video stream, and at
maximum one audio stream.
.SS filmstrip
.IX Subsection "filmstrip"
Adobe Filmstrip muxer.
.PP
This format is used by several Adobe tools to store a generated filmstrip export. It
accepts a single raw video stream.
.SS fits
.IX Subsection "fits"
Flexible Image Transport System (FITS) muxer.
.PP
This image format is used to store astronomical data.
.PP
For more information regarding the format, visit
<\fBhttps://fits.gsfc.nasa.gov\fR>.
.SS flac
.IX Subsection "flac"
Raw FLAC audio muxer.
.PP
This muxer accepts exactly one FLAC audio stream. Additionally, it is possible to add
images with disposition \fBattached_pic\fR.
.PP
\fIOptions\fR
.IX Subsection "Options"
.IP "\fBwrite_header\fR \fIbool\fR" 4
.IX Item "write_header bool"
write the file header if set to \f(CW\*(C`true\*(C'\fR, default is \f(CW\*(C`true\*(C'\fR
.PP
\fIExample\fR
.IX Subsection "Example"
.PP
Use \fBffmpeg\fR to store the audio stream from an input file,
together with several pictures used with \fBattached_pic\fR
disposition:
.PP
.Vb 1
\&        ffmpeg \-i INPUT \-i pic1.png \-i pic2.jpg \-map 0:a \-map 1 \-map 2 \-disposition:v attached_pic OUTPUT
.Ve
.SS flv
.IX Subsection "flv"
Adobe Flash Video Format muxer.
.PP
\fIOptions\fR
.IX Subsection "Options"
.IP "\fBflvflags\fR \fIflags\fR" 4
.IX Item "flvflags flags"
Possible values:
.RS 4
.IP \fBaac_seq_header_detect\fR 4
.IX Item "aac_seq_header_detect"
Place AAC sequence header based on audio stream data.
.IP \fBno_sequence_end\fR 4
.IX Item "no_sequence_end"
Disable sequence end tag.
.IP \fBno_metadata\fR 4
.IX Item "no_metadata"
Disable metadata tag.
.IP \fBno_duration_filesize\fR 4
.IX Item "no_duration_filesize"
Disable duration and filesize in metadata when they are equal to zero
at the end of stream. (Be used to non-seekable living stream).
.IP \fBadd_keyframe_index\fR 4
.IX Item "add_keyframe_index"
Used to facilitate seeking; particularly for HTTP pseudo streaming.
.RE
.RS 4
.RE
.SS framecrc
.IX Subsection "framecrc"
Per-packet CRC (Cyclic Redundancy Check) testing format.
.PP
This muxer computes and prints the Adler\-32 CRC for each audio
and video packet. By default audio frames are converted to signed
16\-bit raw audio and video frames to raw video before computing the
CRC.
.PP
The output of the muxer consists of a line for each audio and video
packet of the form:
.PP
.Vb 1
\&        <stream_index>, <packet_dts>, <packet_pts>, <packet_duration>, <packet_size>, 0x<CRC>
.Ve
.PP
\&\fICRC\fR is a hexadecimal number 0\-padded to 8 digits containing the
CRC of the packet.
.PP
\fIExamples\fR
.IX Subsection "Examples"
.PP
For example to compute the CRC of the audio and video frames in
\&\fIINPUT\fR, converted to raw audio and video packets, and store it
in the file \fIout.crc\fR:
.PP
.Vb 1
\&        ffmpeg \-i INPUT \-f framecrc out.crc
.Ve
.PP
To print the information to stdout, use the command:
.PP
.Vb 1
\&        ffmpeg \-i INPUT \-f framecrc \-
.Ve
.PP
With \fBffmpeg\fR, you can select the output format to which the
audio and video frames are encoded before computing the CRC for each
packet by specifying the audio and video codec. For example, to
compute the CRC of each decoded input audio frame converted to PCM
unsigned 8\-bit and of each decoded input video frame converted to
MPEG\-2 video, use the command:
.PP
.Vb 1
\&        ffmpeg \-i INPUT \-c:a pcm_u8 \-c:v mpeg2video \-f framecrc \-
.Ve
.PP
See also the \fBcrc\fR muxer.
.SS framehash
.IX Subsection "framehash"
Per-packet hash testing format.
.PP
This muxer computes and prints a cryptographic hash for each audio
and video packet. This can be used for packet-by-packet equality
checks without having to individually do a binary comparison on each.
.PP
By default audio frames are converted to signed 16\-bit raw audio and
video frames to raw video before computing the hash, but the output
of explicit conversions to other codecs can also be used. It uses the
SHA\-256 cryptographic hash function by default, but supports several
other algorithms.
.PP
The output of the muxer consists of a line for each audio and video
packet of the form:
.PP
.Vb 1
\&        <stream_index>, <packet_dts>, <packet_pts>, <packet_duration>, <packet_size>, <hash>
.Ve
.PP
\&\fIhash\fR is a hexadecimal number representing the computed hash
for the packet.
.IP "\fBhash\fR \fIalgorithm\fR" 4
.IX Item "hash algorithm"
Use the cryptographic hash function specified by the string \fIalgorithm\fR.
Supported values include \f(CW\*(C`MD5\*(C'\fR, \f(CW\*(C`murmur3\*(C'\fR, \f(CW\*(C`RIPEMD128\*(C'\fR,
\&\f(CW\*(C`RIPEMD160\*(C'\fR, \f(CW\*(C`RIPEMD256\*(C'\fR, \f(CW\*(C`RIPEMD320\*(C'\fR, \f(CW\*(C`SHA160\*(C'\fR,
\&\f(CW\*(C`SHA224\*(C'\fR, \f(CW\*(C`SHA256\*(C'\fR (default), \f(CW\*(C`SHA512/224\*(C'\fR, \f(CW\*(C`SHA512/256\*(C'\fR,
\&\f(CW\*(C`SHA384\*(C'\fR, \f(CW\*(C`SHA512\*(C'\fR, \f(CW\*(C`CRC32\*(C'\fR and \f(CW\*(C`adler32\*(C'\fR.
.PP
\fIExamples\fR
.IX Subsection "Examples"
.PP
To compute the SHA\-256 hash of the audio and video frames in \fIINPUT\fR,
converted to raw audio and video packets, and store it in the file
\&\fIout.sha256\fR:
.PP
.Vb 1
\&        ffmpeg \-i INPUT \-f framehash out.sha256
.Ve
.PP
To print the information to stdout, using the MD5 hash function, use
the command:
.PP
.Vb 1
\&        ffmpeg \-i INPUT \-f framehash \-hash md5 \-
.Ve
.PP
See also the \fBhash\fR muxer.
.SS framemd5
.IX Subsection "framemd5"
Per-packet MD5 testing format.
.PP
This is a variant of the \fBframehash\fR muxer. Unlike that muxer,
it defaults to using the MD5 hash function.
.PP
\fIExamples\fR
.IX Subsection "Examples"
.PP
To compute the MD5 hash of the audio and video frames in \fIINPUT\fR,
converted to raw audio and video packets, and store it in the file
\&\fIout.md5\fR:
.PP
.Vb 1
\&        ffmpeg \-i INPUT \-f framemd5 out.md5
.Ve
.PP
To print the information to stdout, use the command:
.PP
.Vb 1
\&        ffmpeg \-i INPUT \-f framemd5 \-
.Ve
.PP
See also the \fBframehash\fR and \fBmd5\fR muxers.
.SS gif
.IX Subsection "gif"
Animated GIF muxer.
.PP
Note that the GIF format has a very large time base: the delay between two frames can
therefore not be smaller than one centi second.
.PP
\fIOptions\fR
.IX Subsection "Options"
.IP "\fBloop\fR \fIbool\fR" 4
.IX Item "loop bool"
Set the number of times to loop the output. Use \f(CW\-1\fR for no loop, \f(CW0\fR
for looping indefinitely (default).
.IP "\fBfinal_delay\fR \fIdelay\fR" 4
.IX Item "final_delay delay"
Force the delay (expressed in centiseconds) after the last frame. Each frame
ends with a delay until the next frame. The default is \f(CW\-1\fR, which is a
special value to tell the muxer to reuse the previous delay. In case of a
loop, you might want to customize this value to mark a pause for instance.
.PP
\fIExample\fR
.IX Subsection "Example"
.PP
Encode a gif looping 10 times, with a 5 seconds delay between
the loops:
.PP
.Vb 1
\&        ffmpeg \-i INPUT \-loop 10 \-final_delay 500 out.gif
.Ve
.PP
Note 1: if you wish to extract the frames into separate GIF files, you need to
force the \fBimage2\fR muxer:
.PP
.Vb 1
\&        ffmpeg \-i INPUT \-c:v gif \-f image2 "out%d.gif"
.Ve
.SS gxf
.IX Subsection "gxf"
General eXchange Format (GXF) muxer.
.PP
GXF was developed by Grass Valley Group, then standardized by SMPTE as SMPTE
360M and was extended in SMPTE RDD 14\-2007 to include high-definition video
resolutions.
.PP
It accepts at most one video stream with codec \fBmjpeg\fR, or
\&\fBmpeg1video\fR, or \fBmpeg2video\fR, or \fBdvvideo\fR with resolution
\&\fB512x480\fR or \fB608x576\fR, and several audio streams with rate 48000Hz
and codec \fBpcm16_le\fR.
.SS hash
.IX Subsection "hash"
Hash testing format.
.PP
This muxer computes and prints a cryptographic hash of all the input
audio and video frames. This can be used for equality checks without
having to do a complete binary comparison.
.PP
By default audio frames are converted to signed 16\-bit raw audio and
video frames to raw video before computing the hash, but the output
of explicit conversions to other codecs can also be used. Timestamps
are ignored. It uses the SHA\-256 cryptographic hash function by default,
but supports several other algorithms.
.PP
The output of the muxer consists of a single line of the form:
\&\fIalgo\fR=\fIhash\fR, where \fIalgo\fR is a short string representing
the hash function used, and \fIhash\fR is a hexadecimal number
representing the computed hash.
.IP "\fBhash\fR \fIalgorithm\fR" 4
.IX Item "hash algorithm"
Use the cryptographic hash function specified by the string \fIalgorithm\fR.
Supported values include \f(CW\*(C`MD5\*(C'\fR, \f(CW\*(C`murmur3\*(C'\fR, \f(CW\*(C`RIPEMD128\*(C'\fR,
\&\f(CW\*(C`RIPEMD160\*(C'\fR, \f(CW\*(C`RIPEMD256\*(C'\fR, \f(CW\*(C`RIPEMD320\*(C'\fR, \f(CW\*(C`SHA160\*(C'\fR,
\&\f(CW\*(C`SHA224\*(C'\fR, \f(CW\*(C`SHA256\*(C'\fR (default), \f(CW\*(C`SHA512/224\*(C'\fR, \f(CW\*(C`SHA512/256\*(C'\fR,
\&\f(CW\*(C`SHA384\*(C'\fR, \f(CW\*(C`SHA512\*(C'\fR, \f(CW\*(C`CRC32\*(C'\fR and \f(CW\*(C`adler32\*(C'\fR.
.PP
\fIExamples\fR
.IX Subsection "Examples"
.PP
To compute the SHA\-256 hash of the input converted to raw audio and
video, and store it in the file \fIout.sha256\fR:
.PP
.Vb 1
\&        ffmpeg \-i INPUT \-f hash out.sha256
.Ve
.PP
To print an MD5 hash to stdout use the command:
.PP
.Vb 1
\&        ffmpeg \-i INPUT \-f hash \-hash md5 \-
.Ve
.PP
See also the \fBframehash\fR muxer.
.SS hds
.IX Subsection "hds"
HTTP Dynamic Streaming (HDS) muxer.
.PP
HTTP dynamic streaming, or HDS, is an adaptive bitrate streaming method
developed by Adobe. HDS delivers MP4 video content over HTTP connections. HDS
can be used for on-demand streaming or live streaming.
.PP
This muxer creates an .f4m (Adobe Flash Media Manifest File) manifest, an .abst
(Adobe Bootstrap File) for each stream, and segment files in a directory
specified as the output.
.PP
These needs to be accessed by an HDS player through HTTPS for it to be able to
perform playback on the generated stream.
.PP
\fIOptions\fR
.IX Subsection "Options"
.IP "\fBextra_window_size\fR \fIint\fR" 4
.IX Item "extra_window_size int"
number of fragments kept outside of the manifest before removing from disk
.IP "\fBmin_frag_duration\fR \fImicroseconds\fR" 4
.IX Item "min_frag_duration microseconds"
minimum fragment duration (in microseconds), default value is 1 second
(\f(CW10000000\fR)
.IP "\fBremove_at_exit\fR \fIbool\fR" 4
.IX Item "remove_at_exit bool"
remove all fragments when finished when set to \f(CW\*(C`true\*(C'\fR
.IP "\fBwindow_size\fR \fIint\fR" 4
.IX Item "window_size int"
number of fragments kept in the manifest, if set to a value different from
\&\f(CW0\fR. By default all segments are kept in the output directory.
.PP
\fIExample\fR
.IX Subsection "Example"
.PP
Use \fBffmpeg\fR to generate HDS files to the \fIoutput.hds\fR directory in
real-time rate:
.PP
.Vb 1
\&        ffmpeg \-re \-i INPUT \-f hds \-b:v 200k output.hds
.Ve
.SS hls
.IX Subsection "hls"
Apple HTTP Live Streaming muxer that segments MPEG-TS according to
the HTTP Live Streaming (HLS) specification.
.PP
It creates a playlist file, and one or more segment files. The output filename
specifies the playlist filename.
.PP
By default, the muxer creates a file for each segment produced. These files
have the same name as the playlist, followed by a sequential number and a
\&.ts extension.
.PP
Make sure to require a closed GOP when encoding and to set the GOP
size to fit your segment time constraint.
.PP
For example, to convert an input file with \fBffmpeg\fR:
.PP
.Vb 1
\&        ffmpeg \-i in.mkv \-c:v h264 \-flags +cgop \-g 30 \-hls_time 1 out.m3u8
.Ve
.PP
This example will produce the playlist, \fIout.m3u8\fR, and segment files:
\&\fIout0.ts\fR, \fIout1.ts\fR, \fIout2.ts\fR, etc.
.PP
See also the \fBsegment\fR muxer, which provides a more generic and
flexible implementation of a segmenter, and can be used to perform HLS
segmentation.
.PP
\fIOptions\fR
.IX Subsection "Options"
.IP "\fBhls_init_time\fR \fIduration\fR" 4
.IX Item "hls_init_time duration"
Set the initial target segment length. Default value is \fI0\fR.
.Sp
\&\fIduration\fR must be a time duration specification,
see \fBthe Time duration section in the ffmpeg\-utils\|(1) manual\fR.
.Sp
Segment will be cut on the next key frame after this time has passed on the
first m3u8 list. After the initial playlist is filled, \fBffmpeg\fR will cut
segments at duration equal to \fBhls_time\fR.
.IP "\fBhls_time\fR \fIduration\fR" 4
.IX Item "hls_time duration"
Set the target segment length. Default value is 2.
.Sp
\&\fIduration\fR must be a time duration specification,
see \fBthe Time duration section in the ffmpeg\-utils\|(1) manual\fR.
Segment will be cut on the next key frame after this time has passed.
.IP "\fBhls_list_size\fR \fIsize\fR" 4
.IX Item "hls_list_size size"
Set the maximum number of playlist entries. If set to 0 the list file
will contain all the segments. Default value is 5.
.IP "\fBhls_delete_threshold\fR \fIsize\fR" 4
.IX Item "hls_delete_threshold size"
Set the number of unreferenced segments to keep on disk before \f(CW\*(C`hls_flags delete_segments\*(C'\fR
deletes them. Increase this to allow continue clients to download segments which
were recently referenced in the playlist. Default value is 1, meaning segments older than
\&\fBhls_list_size+1\fR will be deleted.
.IP "\fBhls_start_number_source\fR \fIsource\fR" 4
.IX Item "hls_start_number_source source"
Start the playlist sequence number (\f(CW\*(C`#EXT\-X\-MEDIA\-SEQUENCE\*(C'\fR) according to the specified source.
Unless \fBhls_flags single_file\fR is set, it also specifies source of starting sequence numbers of
segment and subtitle filenames. In any case, if \fBhls_flags append_list\fR
is set and read playlist sequence number is greater than the specified start sequence number,
then that value will be used as start value.
.Sp
It accepts the following values:
.RS 4
.IP "\fBgeneric (default)\fR" 4
.IX Item "generic (default)"
Set the start numbers according to the \fBstart_number\fR option value.
.IP \fBepoch\fR 4
.IX Item "epoch"
Set the start number as the seconds since epoch (1970\-01\-01 00:00:00).
.IP \fBepoch_us\fR 4
.IX Item "epoch_us"
Set the start number as the microseconds since epoch (1970\-01\-01 00:00:00).
.IP \fBdatetime\fR 4
.IX Item "datetime"
Set the start number based on the current date/time as YYYYmmddHHMMSS. e.g. 20161231235759.
.RE
.RS 4
.RE
.IP "\fBstart_number\fR \fInumber\fR" 4
.IX Item "start_number number"
Start the playlist sequence number (\f(CW\*(C`#EXT\-X\-MEDIA\-SEQUENCE\*(C'\fR) from the specified \fInumber\fR
when \fBhls_start_number_source\fR value is \fIgeneric\fR. (This is the default case.)
Unless \fBhls_flags single_file\fR is set, it also specifies starting sequence numbers of segment and subtitle filenames.
Default value is 0.
.IP "\fBhls_allow_cache\fR \fIbool\fR" 4
.IX Item "hls_allow_cache bool"
Explicitly set whether the client MAY (1) or MUST NOT (0) cache media segments.
.IP "\fBhls_base_url\fR \fIbaseurl\fR" 4
.IX Item "hls_base_url baseurl"
Append \fIbaseurl\fR to every entry in the playlist.
Useful to generate playlists with absolute paths.
.Sp
Note that the playlist sequence number must be unique for each segment
and it is not to be confused with the segment filename sequence number
which can be cyclic, for example if the \fBwrap\fR option is
specified.
.IP "\fBhls_segment_filename\fR \fIfilename\fR" 4
.IX Item "hls_segment_filename filename"
Set the segment filename. Unless the \fBhls_flags\fR option is set with
\&\fBsingle_file\fR, \fIfilename\fR is used as a string format with the
segment number appended.
.Sp
For example:
.Sp
.Vb 1
\&        ffmpeg \-i in.nut \-hls_segment_filename \*(Aqfile%03d.ts\*(Aq out.m3u8
.Ve
.Sp
will produce the playlist, \fIout.m3u8\fR, and segment files:
\&\fIfile000.ts\fR, \fIfile001.ts\fR, \fIfile002.ts\fR, etc.
.Sp
\&\fIfilename\fR may contain a full path or relative path specification,
but only the file name part without any path will be contained in the m3u8 segment list.
Should a relative path be specified, the path of the created segment
files will be relative to the current working directory.
When \fBstrftime_mkdir\fR is set, the whole expanded value of \fIfilename\fR will be written into the m3u8 segment list.
.Sp
When \fBvar_stream_map\fR is set with two or more variant streams, the
\&\fIfilename\fR pattern must contain the string "%v", and this string will be
expanded to the position of variant stream index in the generated segment file
names.
.Sp
For example:
.Sp
.Vb 3
\&        ffmpeg \-i in.ts \-b:v:0 1000k \-b:v:1 256k \-b:a:0 64k \-b:a:1 32k \e
\&          \-map 0:v \-map 0:a \-map 0:v \-map 0:a \-f hls \-var_stream_map "v:0,a:0 v:1,a:1" \e
\&          \-hls_segment_filename \*(Aqfile_%v_%03d.ts\*(Aq out_%v.m3u8
.Ve
.Sp
will produce the playlists segment file sets:
\&\fIfile_0_000.ts\fR, \fIfile_0_001.ts\fR, \fIfile_0_002.ts\fR, etc. and
\&\fIfile_1_000.ts\fR, \fIfile_1_001.ts\fR, \fIfile_1_002.ts\fR, etc.
.Sp
The string "%v" may be present in the filename or in the last directory name
containing the file, but only in one of them. (Additionally, \f(CW%v\fR may appear multiple times in the last
sub-directory or filename.) If the string \f(CW%v\fR is present in the directory name, then
sub-directories are created after expanding the directory name pattern. This
enables creation of segments corresponding to different variant streams in
subdirectories.
.Sp
For example:
.Sp
.Vb 3
\&        ffmpeg \-i in.ts \-b:v:0 1000k \-b:v:1 256k \-b:a:0 64k \-b:a:1 32k \e
\&          \-map 0:v \-map 0:a \-map 0:v \-map 0:a \-f hls \-var_stream_map "v:0,a:0 v:1,a:1" \e
\&          \-hls_segment_filename \*(Aqvs%v/file_%03d.ts\*(Aq vs%v/out.m3u8
.Ve
.Sp
will produce the playlists segment file sets:
\&\fIvs0/file_000.ts\fR, \fIvs0/file_001.ts\fR, \fIvs0/file_002.ts\fR, etc. and
\&\fIvs1/file_000.ts\fR, \fIvs1/file_001.ts\fR, \fIvs1/file_002.ts\fR, etc.
.IP "\fBstrftime\fR \fIbool\fR" 4
.IX Item "strftime bool"
Use \f(CWstrftime()\fR on \fIfilename\fR to expand the segment filename with
localtime. The segment number is also available in this mode, but to use it,
you need to set \fBsecond_level_segment_index\fR in the \fBhls_flag\fR and
%%d will be the specifier.
.Sp
For example:
.Sp
.Vb 1
\&        ffmpeg \-i in.nut \-strftime 1 \-hls_segment_filename \*(Aqfile\-%Y%m%d\-%s.ts\*(Aq out.m3u8
.Ve
.Sp
will produce the playlist, \fIout.m3u8\fR, and segment files:
\&\fIfile\-20160215\-1455569023.ts\fR, \fIfile\-20160215\-1455569024.ts\fR, etc.
Note: On some systems/environments, the \f(CW%s\fR specifier is not
available. See \f(CWstrftime()\fR documentation.
.Sp
For example:
.Sp
.Vb 1
\&        ffmpeg \-i in.nut \-strftime 1 \-hls_flags second_level_segment_index \-hls_segment_filename \*(Aqfile\-%Y%m%d\-%%04d.ts\*(Aq out.m3u8
.Ve
.Sp
will produce the playlist, \fIout.m3u8\fR, and segment files:
\&\fIfile\-20160215\-0001.ts\fR, \fIfile\-20160215\-0002.ts\fR, etc.
.IP "\fBstrftime_mkdir\fR \fIbool\fR" 4
.IX Item "strftime_mkdir bool"
Used together with \fBstrftime\fR, it will create all subdirectories which
are present in the expanded values of option \fBhls_segment_filename\fR.
.Sp
For example:
.Sp
.Vb 1
\&        ffmpeg \-i in.nut \-strftime 1 \-strftime_mkdir 1 \-hls_segment_filename \*(Aq%Y%m%d/file\-%Y%m%d\-%s.ts\*(Aq out.m3u8
.Ve
.Sp
will create a directory \fI201560215\fR (if it does not exist), and then
produce the playlist, \fIout.m3u8\fR, and segment files:
\&\fI20160215/file\-20160215\-1455569023.ts\fR,
\&\fI20160215/file\-20160215\-1455569024.ts\fR, etc.
.Sp
For example:
.Sp
.Vb 1
\&        ffmpeg \-i in.nut \-strftime 1 \-strftime_mkdir 1 \-hls_segment_filename \*(Aq%Y/%m/%d/file\-%Y%m%d\-%s.ts\*(Aq out.m3u8
.Ve
.Sp
will create a directory hierarchy \fI2016/02/15\fR (if any of them do not
exist), and then produce the playlist, \fIout.m3u8\fR, and segment files:
\&\fI2016/02/15/file\-20160215\-1455569023.ts\fR,
\&\fI2016/02/15/file\-20160215\-1455569024.ts\fR, etc.
.IP "\fBhls_segment_options\fR \fIoptions_list\fR" 4
.IX Item "hls_segment_options options_list"
Set output format options using a :\-separated list of key=value
parameters. Values containing \f(CW\*(C`:\*(C'\fR special characters must be
escaped.
.IP "\fBhls_key_info_file\fR \fIkey_info_file\fR" 4
.IX Item "hls_key_info_file key_info_file"
Use the information in \fIkey_info_file\fR for segment encryption. The first
line of \fIkey_info_file\fR specifies the key URI written to the playlist. The
key URL is used to access the encryption key during playback. The second line
specifies the path to the key file used to obtain the key during the encryption
process. The key file is read as a single packed array of 16 octets in binary
format. The optional third line specifies the initialization vector (IV) as a
hexadecimal string to be used instead of the segment sequence number (default)
for encryption. Changes to \fIkey_info_file\fR will result in segment
encryption with the new key/IV and an entry in the playlist for the new key
URI/IV if \fBhls_flags periodic_rekey\fR is enabled.
.Sp
Key info file format:
.Sp
.Vb 3
\&        <key URI>
\&        <key file path>
\&        <IV> (optional)
.Ve
.Sp
Example key URIs:
.Sp
.Vb 3
\&        http://server/file.key
\&        /path/to/file.key
\&        file.key
.Ve
.Sp
Example key file paths:
.Sp
.Vb 2
\&        file.key
\&        /path/to/file.key
.Ve
.Sp
Example IV:
.Sp
.Vb 1
\&        0123456789ABCDEF0123456789ABCDEF
.Ve
.Sp
Key info file example:
.Sp
.Vb 3
\&        http://server/file.key
\&        /path/to/file.key
\&        0123456789ABCDEF0123456789ABCDEF
.Ve
.Sp
Example shell script:
.Sp
.Vb 8
\&        #!/bin/sh
\&        BASE_URL=${1:\-\*(Aq.\*(Aq}
\&        openssl rand 16 > file.key
\&        echo $BASE_URL/file.key > file.keyinfo
\&        echo file.key >> file.keyinfo
\&        echo $(openssl rand \-hex 16) >> file.keyinfo
\&        ffmpeg \-f lavfi \-re \-i testsrc \-c:v h264 \-hls_flags delete_segments \e
\&          \-hls_key_info_file file.keyinfo out.m3u8
.Ve
.IP "\fBhls_enc\fR \fIbool\fR" 4
.IX Item "hls_enc bool"
Enable (1) or disable (0) the AES128 encryption.
When enabled every segment generated is encrypted and the encryption key
is saved as \fIplaylist name\fR.key.
.IP "\fBhls_enc_key\fR \fIkey\fR" 4
.IX Item "hls_enc_key key"
Specify a 16\-octet key to encrypt the segments, by default it is randomly
generated.
.IP "\fBhls_enc_key_url\fR \fIkeyurl\fR" 4
.IX Item "hls_enc_key_url keyurl"
If set, \fIkeyurl\fR is prepended instead of \fIbaseurl\fR to the key filename
in the playlist.
.IP "\fBhls_enc_iv\fR \fIiv\fR" 4
.IX Item "hls_enc_iv iv"
Specify the 16\-octet initialization vector for every segment instead of the
autogenerated ones.
.IP "\fBhls_segment_type\fR \fIflags\fR" 4
.IX Item "hls_segment_type flags"
Possible values:
.RS 4
.IP \fBmpegts\fR 4
.IX Item "mpegts"
Output segment files in MPEG\-2 Transport Stream format. This is
compatible with all HLS versions.
.IP \fBfmp4\fR 4
.IX Item "fmp4"
Output segment files in fragmented MP4 format, similar to MPEG-DASH.
fmp4 files may be used in HLS version 7 and above.
.RE
.RS 4
.RE
.IP "\fBhls_fmp4_init_filename\fR \fIfilename\fR" 4
.IX Item "hls_fmp4_init_filename filename"
Set filename for the fragment files header file, default filename is \fIinit.mp4\fR.
.Sp
When \fBstrftime\fR is enabled, \fIfilename\fR is expanded to the segment filename with localtime.
.Sp
For example:
.Sp
.Vb 1
\&        ffmpeg \-i in.nut \-hls_segment_type fmp4 \-strftime 1 \-hls_fmp4_init_filename "%s_init.mp4" out.m3u8
.Ve
.Sp
will produce init like this \fI1602678741_init.mp4\fR.
.IP "\fBhls_fmp4_init_resend\fR \fIbool\fR" 4
.IX Item "hls_fmp4_init_resend bool"
Resend init file after m3u8 file refresh every time, default is \fI0\fR.
.Sp
When \fBvar_stream_map\fR is set with two or more variant streams, the
\&\fIfilename\fR pattern must contain the string "%v", this string specifies
the position of variant stream index in the generated init file names.
The string "%v" may be present in the filename or in the last directory name
containing the file. If the string is present in the directory name, then
sub-directories are created after expanding the directory name pattern. This
enables creation of init files corresponding to different variant streams in
subdirectories.
.IP "\fBhls_flags\fR \fIflags\fR" 4
.IX Item "hls_flags flags"
Possible values:
.RS 4
.IP \fBsingle_file\fR 4
.IX Item "single_file"
If this flag is set, the muxer will store all segments in a single MPEG-TS
file, and will use byte ranges in the playlist. HLS playlists generated with
this way will have the version number 4.
.Sp
For example:
.Sp
.Vb 1
\&        ffmpeg \-i in.nut \-hls_flags single_file out.m3u8
.Ve
.Sp
will produce the playlist, \fIout.m3u8\fR, and a single segment file,
\&\fIout.ts\fR.
.IP \fBdelete_segments\fR 4
.IX Item "delete_segments"
Segment files removed from the playlist are deleted after a period of time
equal to the duration of the segment plus the duration of the playlist.
.IP \fBappend_list\fR 4
.IX Item "append_list"
Append new segments into the end of old segment list,
and remove the \f(CW\*(C`#EXT\-X\-ENDLIST\*(C'\fR from the old segment list.
.IP \fBround_durations\fR 4
.IX Item "round_durations"
Round the duration info in the playlist file segment info to integer
values, instead of using floating point.
If there are no other features requiring higher HLS versions be used,
then this will allow \fBffmpeg\fR to output a HLS version 2 m3u8.
.IP \fBdiscont_start\fR 4
.IX Item "discont_start"
Add the \f(CW\*(C`#EXT\-X\-DISCONTINUITY\*(C'\fR tag to the playlist, before the
first segment's information.
.IP \fBomit_endlist\fR 4
.IX Item "omit_endlist"
Do not append the \f(CW\*(C`EXT\-X\-ENDLIST\*(C'\fR tag at the end of the playlist.
.IP \fBperiodic_rekey\fR 4
.IX Item "periodic_rekey"
The file specified by \f(CW\*(C`hls_key_info_file\*(C'\fR will be checked periodically and
detect updates to the encryption info. Be sure to replace this file atomically,
including the file containing the AES encryption key.
.IP \fBindependent_segments\fR 4
.IX Item "independent_segments"
Add the \f(CW\*(C`#EXT\-X\-INDEPENDENT\-SEGMENTS\*(C'\fR tag to playlists that has video segments
and when all the segments of that playlist are guaranteed to start with a key frame.
.IP \fBiframes_only\fR 4
.IX Item "iframes_only"
Add the \f(CW\*(C`#EXT\-X\-I\-FRAMES\-ONLY\*(C'\fR tag to playlists that has video segments
and can play only I\-frames in the \f(CW\*(C`#EXT\-X\-BYTERANGE\*(C'\fR mode.
.IP \fBsplit_by_time\fR 4
.IX Item "split_by_time"
Allow segments to start on frames other than key frames. This improves
behavior on some players when the time between key frames is inconsistent,
but may make things worse on others, and can cause some oddities during
seeking. This flag should be used with the \fBhls_time\fR option.
.IP \fBprogram_date_time\fR 4
.IX Item "program_date_time"
Generate \f(CW\*(C`EXT\-X\-PROGRAM\-DATE\-TIME\*(C'\fR tags.
.IP \fBsecond_level_segment_index\fR 4
.IX Item "second_level_segment_index"
Make it possible to use segment indexes as %%d in the
\&\fBhls_segment_filename\fR option expression besides date/time values when
\&\fBstrftime\fR option is on. To get fixed width numbers with trailing zeroes, %%0xd format
is available where x is the required width.
.IP \fBsecond_level_segment_size\fR 4
.IX Item "second_level_segment_size"
Make it possible to use segment sizes (counted in bytes) as %%s in
\&\fBhls_segment_filename\fR option expression besides date/time values when
strftime is on. To get fixed width numbers with trailing zeroes, %%0xs format
is available where x is the required width.
.IP \fBsecond_level_segment_duration\fR 4
.IX Item "second_level_segment_duration"
Make it possible to use segment duration (calculated in microseconds) as %%t in
\&\fBhls_segment_filename\fR option expression besides date/time values when
strftime is on. To get fixed width numbers with trailing zeroes, %%0xt format
is available where x is the required width.
.Sp
For example:
.Sp
.Vb 4
\&        ffmpeg \-i sample.mpeg \e
\&           \-f hls \-hls_time 3 \-hls_list_size 5 \e
\&           \-hls_flags second_level_segment_index+second_level_segment_size+second_level_segment_duration \e
\&           \-strftime 1 \-strftime_mkdir 1 \-hls_segment_filename "segment_%Y%m%d%H%M%S_%%04d_%%08s_%%013t.ts" stream.m3u8
.Ve
.Sp
will produce segments like this:
\&\fIsegment_20170102194334_0003_00122200_0000003000000.ts\fR, \fIsegment_20170102194334_0004_00120072_0000003000000.ts\fR etc.
.IP \fBtemp_file\fR 4
.IX Item "temp_file"
Write segment data to \fIfilename.tmp\fR and rename to filename only once the
segment is complete.
.Sp
A webserver serving up segments can be configured to reject requests to *.tmp to
prevent access to in-progress segments before they have been added to the m3u8
playlist.
.Sp
This flag also affects how m3u8 playlist files are created. If this flag is set,
all playlist files will be written into a temporary file and renamed after they
are complete, similarly as segments are handled. But playlists with \f(CW\*(C`file\*(C'\fR
protocol and with \fBhls_playlist_type\fR type other than \fBvod\fR are
always written into a temporary file regardless of this flag.
.Sp
Master playlist files specified with \fBmaster_pl_name\fR, if any, with
\&\f(CW\*(C`file\*(C'\fR protocol, are always written into temporary file regardless of this
flag if \fBmaster_pl_publish_rate\fR value is other than zero.
.RE
.RS 4
.RE
.IP "\fBhls_playlist_type\fR \fItype\fR" 4
.IX Item "hls_playlist_type type"
If type is \fBevent\fR, emit \f(CW\*(C`#EXT\-X\-PLAYLIST\-TYPE:EVENT\*(C'\fR in the m3u8
header. This forces \fBhls_list_size\fR to 0; the playlist can only be
appended to.
.Sp
If type is \fBvod\fR, emit \f(CW\*(C`#EXT\-X\-PLAYLIST\-TYPE:VOD\*(C'\fR in the m3u8
header. This forces \fBhls_list_size\fR to 0; the playlist must not change.
.IP "\fBmethod\fR \fImethod\fR" 4
.IX Item "method method"
Use the given HTTP method to create the hls files.
.Sp
For example:
.Sp
.Vb 1
\&        ffmpeg \-re \-i in.ts \-f hls \-method PUT http://example.com/live/out.m3u8
.Ve
.Sp
will upload all the mpegts segment files to the HTTP server using the HTTP PUT
method, and update the m3u8 files every \f(CW\*(C`refresh\*(C'\fR times using the same
method. Note that the HTTP server must support the given method for uploading
files.
.IP "\fBhttp_user_agent\fR \fIagent\fR" 4
.IX Item "http_user_agent agent"
Override User-Agent field in HTTP header. Applicable only for HTTP output.
.IP "\fBvar_stream_map\fR \fIstream_map\fR" 4
.IX Item "var_stream_map stream_map"
Specify a map string defining how to group the audio, video and subtitle streams
into different variant streams. The variant stream groups are separated by
space.
.Sp
Expected string format is like this "a:0,v:0 a:1,v:1 ....". Here a:, v:, s: are
the keys to specify audio, video and subtitle streams respectively.
Allowed values are 0 to 9 (limited just based on practical usage).
.Sp
When there are two or more variant streams, the output filename pattern must
contain the string "%v": this string specifies the position of variant stream
index in the output media playlist filenames. The string "%v" may be present in
the filename or in the last directory name containing the file. If the string is
present in the directory name, then sub-directories are created after expanding
the directory name pattern. This enables creation of variant streams in
subdirectories.
.Sp
A few examples follow.
.RS 4
.IP \(bu 4
Create two hls variant streams. The first variant stream will contain video
stream of bitrate 1000k and audio stream of bitrate 64k and the second variant
stream will contain video stream of bitrate 256k and audio stream of bitrate
32k. Here, two media playlist with file names \fIout_0.m3u8\fR and
\&\fIout_1.m3u8\fR will be created.
.Sp
.Vb 3
\&        ffmpeg \-re \-i in.ts \-b:v:0 1000k \-b:v:1 256k \-b:a:0 64k \-b:a:1 32k \e
\&          \-map 0:v \-map 0:a \-map 0:v \-map 0:a \-f hls \-var_stream_map "v:0,a:0 v:1,a:1" \e
\&          http://example.com/live/out_%v.m3u8
.Ve
.IP \(bu 4
If you want something meaningful text instead of indexes in result names, you
may specify names for each or some of the variants. The following example will
create two hls variant streams as in the previous one. But here, the two media
playlist with file names \fIout_my_hd.m3u8\fR and \fIout_my_sd.m3u8\fR will be
created.
.Sp
.Vb 3
\&        ffmpeg \-re \-i in.ts \-b:v:0 1000k \-b:v:1 256k \-b:a:0 64k \-b:a:1 32k \e
\&          \-map 0:v \-map 0:a \-map 0:v \-map 0:a \-f hls \-var_stream_map "v:0,a:0,name:my_hd v:1,a:1,name:my_sd" \e
\&          http://example.com/live/out_%v.m3u8
.Ve
.IP \(bu 4
Create three hls variant streams. The first variant stream will be a video only
stream with video bitrate 1000k, the second variant stream will be an audio only
stream with bitrate 64k and the third variant stream will be a video only stream
with bitrate 256k. Here, three media playlist with file names \fIout_0.m3u8\fR,
\&\fIout_1.m3u8\fR and \fIout_2.m3u8\fR will be created.
.Sp
.Vb 3
\&        ffmpeg \-re \-i in.ts \-b:v:0 1000k \-b:v:1 256k \-b:a:0 64k \e
\&          \-map 0:v \-map 0:a \-map 0:v \-f hls \-var_stream_map "v:0 a:0 v:1" \e
\&          http://example.com/live/out_%v.m3u8
.Ve
.IP \(bu 4
Create the variant streams in subdirectories. Here, the first media playlist is
created at \fIhttp://example.com/live/vs_0/out.m3u8\fR and the second one at
\&\fIhttp://example.com/live/vs_1/out.m3u8\fR.
.Sp
.Vb 3
\&        ffmpeg \-re \-i in.ts \-b:v:0 1000k \-b:v:1 256k \-b:a:0 64k \-b:a:1 32k \e
\&          \-map 0:v \-map 0:a \-map 0:v \-map 0:a \-f hls \-var_stream_map "v:0,a:0 v:1,a:1" \e
\&          http://example.com/live/vs_%v/out.m3u8
.Ve
.IP \(bu 4
Create two audio only and two video only variant streams. In addition to the
\&\f(CW\*(C`#EXT\-X\-STREAM\-INF\*(C'\fR tag for each variant stream in the master playlist, the
\&\f(CW\*(C`#EXT\-X\-MEDIA\*(C'\fR tag is also added for the two audio only variant streams and
they are mapped to the two video only variant streams with audio group names
\&'aud_low' and 'aud_high'.
By default, a single hls variant containing all the encoded streams is created.
.Sp
.Vb 5
\&        ffmpeg \-re \-i in.ts \-b:a:0 32k \-b:a:1 64k \-b:v:0 1000k \-b:v:1 3000k  \e
\&          \-map 0:a \-map 0:a \-map 0:v \-map 0:v \-f hls \e
\&          \-var_stream_map "a:0,agroup:aud_low a:1,agroup:aud_high v:0,agroup:aud_low v:1,agroup:aud_high" \e
\&          \-master_pl_name master.m3u8 \e
\&          http://example.com/live/out_%v.m3u8
.Ve
.IP \(bu 4
Create two audio only and one video only variant streams. In addition to the
\&\f(CW\*(C`#EXT\-X\-STREAM\-INF\*(C'\fR tag for each variant stream in the master playlist, the
\&\f(CW\*(C`#EXT\-X\-MEDIA\*(C'\fR tag is also added for the two audio only variant streams and
they are mapped to the one video only variant streams with audio group name
\&'aud_low', and the audio group have default stat is NO or YES.
By default, a single hls variant containing all the encoded streams is created.
.Sp
.Vb 5
\&        ffmpeg \-re \-i in.ts \-b:a:0 32k \-b:a:1 64k \-b:v:0 1000k \e
\&          \-map 0:a \-map 0:a \-map 0:v \-f hls \e
\&          \-var_stream_map "a:0,agroup:aud_low,default:yes a:1,agroup:aud_low v:0,agroup:aud_low" \e
\&          \-master_pl_name master.m3u8 \e
\&          http://example.com/live/out_%v.m3u8
.Ve
.IP \(bu 4
Create two audio only and one video only variant streams. In addition to the
\&\f(CW\*(C`#EXT\-X\-STREAM\-INF\*(C'\fR tag for each variant stream in the master playlist, the
\&\f(CW\*(C`#EXT\-X\-MEDIA\*(C'\fR tag is also added for the two audio only variant streams and
they are mapped to the one video only variant streams with audio group name
\&'aud_low', and the audio group have default stat is NO or YES, and one audio
have and language is named ENG, the other audio language is named CHN. By
default, a single hls variant containing all the encoded streams is created.
.Sp
.Vb 5
\&        ffmpeg \-re \-i in.ts \-b:a:0 32k \-b:a:1 64k \-b:v:0 1000k \e
\&          \-map 0:a \-map 0:a \-map 0:v \-f hls \e
\&          \-var_stream_map "a:0,agroup:aud_low,default:yes,language:ENG a:1,agroup:aud_low,language:CHN v:0,agroup:aud_low" \e
\&          \-master_pl_name master.m3u8 \e
\&          http://example.com/live/out_%v.m3u8
.Ve
.IP \(bu 4
Create a single variant stream. Add the \f(CW\*(C`#EXT\-X\-MEDIA\*(C'\fR tag with
\&\f(CW\*(C`TYPE=SUBTITLES\*(C'\fR in the master playlist with webvtt subtitle group name
\&'subtitle' and optional subtitle name, e.g. 'English'. Make sure the input
file has one text subtitle stream at least.
.Sp
.Vb 8
\&        ffmpeg \-y \-i input_with_subtitle.mkv \e
\&         \-b:v:0 5250k \-c:v h264 \-pix_fmt yuv420p \-profile:v main \-level 4.1 \e
\&         \-b:a:0 256k \e
\&         \-c:s webvtt \-c:a mp2 \-ar 48000 \-ac 2 \-map 0:v \-map 0:a:0 \-map 0:s:0 \e
\&         \-f hls \-var_stream_map "v:0,a:0,s:0,sgroup:subtitle,sname:English" \e
\&         \-master_pl_name master.m3u8 \-t 300 \-hls_time 10 \-hls_init_time 4 \-hls_list_size \e
\&         10 \-master_pl_publish_rate 10 \-hls_flags \e
\&         delete_segments+discont_start+split_by_time ./tmp/video.m3u8
.Ve
.RE
.RS 4
.RE
.IP "\fBcc_stream_map\fR \fIcc_stream_map\fR" 4
.IX Item "cc_stream_map cc_stream_map"
Map string which specifies different closed captions groups and their
attributes. The closed captions stream groups are separated by space.
.Sp
Expected string format is like this
"ccgroup:<group name>,instreamid:<INSTREAM\-ID>,language:<language code> ....".
\&'ccgroup' and 'instreamid' are mandatory attributes. 'language' is an optional
attribute.
.Sp
The closed captions groups configured using this option are mapped to different
variant streams by providing the same 'ccgroup' name in the
\&\fBvar_stream_map\fR string.
.Sp
For example:
.Sp
.Vb 7
\&        ffmpeg \-re \-i in.ts \-b:v:0 1000k \-b:v:1 256k \-b:a:0 64k \-b:a:1 32k \e
\&          \-a53cc:0 1 \-a53cc:1 1 \e
\&          \-map 0:v \-map 0:a \-map 0:v \-map 0:a \-f hls \e
\&          \-cc_stream_map "ccgroup:cc,instreamid:CC1,language:en ccgroup:cc,instreamid:CC2,language:sp" \e
\&          \-var_stream_map "v:0,a:0,ccgroup:cc v:1,a:1,ccgroup:cc" \e
\&          \-master_pl_name master.m3u8 \e
\&          http://example.com/live/out_%v.m3u8
.Ve
.Sp
will add two \f(CW\*(C`#EXT\-X\-MEDIA\*(C'\fR tags with \f(CW\*(C`TYPE=CLOSED\-CAPTIONS\*(C'\fR in the
master playlist for the INSTREAM-IDs 'CC1' and 'CC2'. Also, it will add
\&\f(CW\*(C`CLOSED\-CAPTIONS\*(C'\fR attribute with group name 'cc' for the two output variant
streams.
.Sp
If \fBvar_stream_map\fR is not set, then the first available ccgroup in
\&\fBcc_stream_map\fR is mapped to the output variant stream.
.Sp
For example:
.Sp
.Vb 4
\&        ffmpeg \-re \-i in.ts \-b:v 1000k \-b:a 64k \-a53cc 1 \-f hls \e
\&          \-cc_stream_map "ccgroup:cc,instreamid:CC1,language:en" \e
\&          \-master_pl_name master.m3u8 \e
\&          http://example.com/live/out.m3u8
.Ve
.Sp
this will add \f(CW\*(C`#EXT\-X\-MEDIA\*(C'\fR tag with \f(CW\*(C`TYPE=CLOSED\-CAPTIONS\*(C'\fR in the
master playlist with group name 'cc', language 'en' (english) and INSTREAM-ID
\&'CC1'. Also, it will add \f(CW\*(C`CLOSED\-CAPTIONS\*(C'\fR attribute with group name 'cc'
for the output variant stream.
.IP "\fBmaster_pl_name\fR \fIname\fR" 4
.IX Item "master_pl_name name"
Create HLS master playlist with the given name.
.Sp
For example:
.Sp
.Vb 1
\&        ffmpeg \-re \-i in.ts \-f hls \-master_pl_name master.m3u8 http://example.com/live/out.m3u8
.Ve
.Sp
creates an HLS master playlist with name \fImaster.m3u8\fR which is published
at <\fBhttp://example.com/live/\fR>.
.IP "\fBmaster_pl_publish_rate\fR \fIcount\fR" 4
.IX Item "master_pl_publish_rate count"
Publish master play list repeatedly every after specified number of segment intervals.
.Sp
For example:
.Sp
.Vb 2
\&        ffmpeg \-re \-i in.ts \-f hls \-master_pl_name master.m3u8 \e
\&        \-hls_time 2 \-master_pl_publish_rate 30 http://example.com/live/out.m3u8
.Ve
.Sp
creates an HLS master playlist with name \fImaster.m3u8\fR and keeps
publishing it repeatedly every after 30 segments i.e. every after 60s.
.IP "\fBhttp_persistent\fR \fIbool\fR" 4
.IX Item "http_persistent bool"
Use persistent HTTP connections. Applicable only for HTTP output.
.IP "\fBtimeout\fR \fItimeout\fR" 4
.IX Item "timeout timeout"
Set timeout for socket I/O operations. Applicable only for HTTP output.
.IP "\fBignore_io_errors\fR \fIbool\fR" 4
.IX Item "ignore_io_errors bool"
Ignore IO errors during open, write and delete. Useful for long-duration runs with network output.
.IP "\fBheaders\fR \fIheaders\fR" 4
.IX Item "headers headers"
Set custom HTTP headers, can override built in default headers. Applicable only for HTTP output.
.SS iamf
.IX Subsection "iamf"
Immersive Audio Model and Formats (IAMF) muxer.
.PP
IAMF is used to provide immersive audio content for presentation on a wide range
of devices in both streaming and offline applications. These applications
include internet audio streaming, multicasting/broadcasting services, file
download, gaming, communication, virtual and augmented reality, and others. In
these applications, audio may be played back on a wide range of devices, e.g.,
headphones, mobile phones, tablets, TVs, sound bars, home theater systems, and
big screens.
.PP
This format was promoted and designed by Alliance for Open Media.
.PP
For more information about this format, see <\fBhttps://aomedia.org/iamf/\fR>.
.SS ico
.IX Subsection "ico"
ICO file muxer.
.PP
Microsoft's icon file format (ICO) has some strict limitations that should be noted:
.IP \(bu 4
Size cannot exceed 256 pixels in any dimension
.IP \(bu 4
Only BMP and PNG images can be stored
.IP \(bu 4
If a BMP image is used, it must be one of the following pixel formats:
.Sp
.Vb 7
\&        BMP Bit Depth      FFmpeg Pixel Format
\&        1bit               pal8
\&        4bit               pal8
\&        8bit               pal8
\&        16bit              rgb555le
\&        24bit              bgr24
\&        32bit              bgra
.Ve
.IP \(bu 4
If a BMP image is used, it must use the BITMAPINFOHEADER DIB header
.IP \(bu 4
If a PNG image is used, it must use the rgba pixel format
.SS ilbc
.IX Subsection "ilbc"
Internet Low Bitrate Codec (iLBC) raw muxer.
.PP
It accepts a single \fBilbc\fR audio stream.
.SS "image2, image2pipe"
.IX Subsection "image2, image2pipe"
Image file muxer.
.PP
The \fBimage2\fR muxer writes video frames to image files.
.PP
The output filenames are specified by a pattern, which can be used to
produce sequentially numbered series of files.
The pattern may contain the string "%d" or "%0\fIN\fRd", this string
specifies the position of the characters representing a numbering in
the filenames. If the form "%0\fIN\fRd" is used, the string
representing the number in each filename is 0\-padded to \fIN\fR
digits. The literal character '%' can be specified in the pattern with
the string "%%".
.PP
If the pattern contains "%d" or "%0\fIN\fRd", the first filename of
the file list specified will contain the number 1, all the following
numbers will be sequential.
.PP
The pattern may contain a suffix which is used to automatically
determine the format of the image files to write.
.PP
For example the pattern "img\-%03d.bmp" will specify a sequence of
filenames of the form \fIimg\-001.bmp\fR, \fIimg\-002.bmp\fR, ...,
\&\fIimg\-010.bmp\fR, etc.
The pattern "img%%\-%d.jpg" will specify a sequence of filenames of the
form \fIimg%\-1.jpg\fR, \fIimg%\-2.jpg\fR, ..., \fIimg%\-10.jpg\fR,
etc.
.PP
The image muxer supports the .Y.U.V image file format. This format is
special in that each image frame consists of three files, for
each of the YUV420P components. To read or write this image file format,
specify the name of the '.Y' file. The muxer will automatically open the
\&'.U' and '.V' files as required.
.PP
The \fBimage2pipe\fR muxer accepts the same options as the \fBimage2\fR muxer,
but ignores the pattern verification and expansion, as it is supposed to write
to the command output rather than to an actual stored file.
.PP
\fIOptions\fR
.IX Subsection "Options"
.IP "\fBframe_pts\fR \fIbool\fR" 4
.IX Item "frame_pts bool"
If set to 1, expand the filename with the packet PTS (presentation time stamp).
Default value is 0.
.IP "\fBstart_number\fR \fIcount\fR" 4
.IX Item "start_number count"
Start the sequence from the specified number. Default value is 1.
.IP "\fBupdate\fR \fIbool\fR" 4
.IX Item "update bool"
If set to 1, the filename will always be interpreted as just a
filename, not a pattern, and the corresponding file will be continuously
overwritten with new images. Default value is 0.
.IP "\fBstrftime\fR \fIbool\fR" 4
.IX Item "strftime bool"
If set to 1, expand the filename with date and time information from
\&\f(CWstrftime()\fR. Default value is 0.
.IP "\fBatomic_writing\fR \fIbool\fR" 4
.IX Item "atomic_writing bool"
Write output to a temporary file, which is renamed to target filename once
writing is completed. Default is disabled.
.IP "\fBprotocol_opts\fR \fIoptions_list\fR" 4
.IX Item "protocol_opts options_list"
Set protocol options as a :\-separated list of key=value parameters. Values
containing the \f(CW\*(C`:\*(C'\fR special character must be escaped.
.PP
\fIExamples\fR
.IX Subsection "Examples"
.IP \(bu 4
Use \fBffmpeg\fR for creating a sequence of files \fIimg\-001.jpeg\fR,
\&\fIimg\-002.jpeg\fR, ..., taking one image every second from the input video:
.Sp
.Vb 1
\&        ffmpeg \-i in.avi \-vsync cfr \-r 1 \-f image2 \*(Aqimg\-%03d.jpeg\*(Aq
.Ve
.Sp
Note that with \fBffmpeg\fR, if the format is not specified with the
\&\f(CW\*(C`\-f\*(C'\fR option and the output filename specifies an image file
format, the image2 muxer is automatically selected, so the previous
command can be written as:
.Sp
.Vb 1
\&        ffmpeg \-i in.avi \-vsync cfr \-r 1 \*(Aqimg\-%03d.jpeg\*(Aq
.Ve
.Sp
Note also that the pattern must not necessarily contain "%d" or
"%0\fIN\fRd", for example to create a single image file
\&\fIimg.jpeg\fR from the start of the input video you can employ the command:
.Sp
.Vb 1
\&        ffmpeg \-i in.avi \-f image2 \-frames:v 1 img.jpeg
.Ve
.IP \(bu 4
The \fBstrftime\fR option allows you to expand the filename with
date and time information. Check the documentation of
the \f(CWstrftime()\fR function for the syntax.
.Sp
To generate image files from the \f(CWstrftime()\fR "%Y\-%m\-%d_%H\-%M\-%S" pattern,
the following \fBffmpeg\fR command can be used:
.Sp
.Vb 1
\&        ffmpeg \-f v4l2 \-r 1 \-i /dev/video0 \-f image2 \-strftime 1 "%Y\-%m\-%d_%H\-%M\-%S.jpg"
.Ve
.IP \(bu 4
Set the file name with current frame's PTS:
.Sp
.Vb 1
\&        ffmpeg \-f v4l2 \-r 1 \-i /dev/video0 \-copyts \-f image2 \-frame_pts true %d.jpg
.Ve
.IP \(bu 4
Publish contents of your desktop directly to a WebDAV server every second:
.Sp
.Vb 1
\&        ffmpeg \-f x11grab \-framerate 1 \-i :0.0 \-q:v 6 \-update 1 \-protocol_opts method=PUT http://example.com/desktop.jpg
.Ve
.SS ircam
.IX Subsection "ircam"
Berkeley / IRCAM / CARL Sound Filesystem (BICSF) format muxer.
.PP
The Berkeley/IRCAM/CARL Sound Format, developed in the 1980s, is a result of the
merging of several different earlier sound file formats and systems including
the csound system developed by Dr Gareth Loy at the Computer Audio Research Lab
(CARL) at UC San Diego, the IRCAM sound file system developed by Rob Gross and
Dan Timis at the Institut de Recherche et Coordination Acoustique / Musique in
Paris and the Berkeley Fast Filesystem.
.PP
It was developed initially as part of the Berkeley/IRCAM/CARL Sound Filesystem,
a suite of programs designed to implement a filesystem for audio applications
running under Berkeley UNIX. It was particularly popular in academic music
research centres, and was used a number of times in the creation of early
computer-generated compositions.
.PP
This muxer accepts a single audio stream containing PCM data.
.SS ivf
.IX Subsection "ivf"
On2 IVF muxer.
.PP
IVF was developed by On2 Technologies (formerly known as Duck
Corporation), to store internally developed codecs.
.PP
This muxer accepts a single \fBvp8\fR, \fBvp9\fR, or \fBav1\fR
video stream.
.SS jacosub
.IX Subsection "jacosub"
JACOsub subtitle format muxer.
.PP
This muxer accepts a single \fBjacosub\fR subtitles stream.
.PP
For more information about the format, see
<\fBhttp://unicorn.us.com/jacosub/jscripts.html\fR>.
.SS kvag
.IX Subsection "kvag"
Simon & Schuster Interactive VAG muxer.
.PP
This custom VAG container is used by some Simon & Schuster Interactive
games such as "Real War", and "Real War: Rogue States".
.PP
This muxer accepts a single \fBadpcm_ima_ssi\fR audio stream.
.SS lc3
.IX Subsection "lc3"
Bluetooth SIG Low Complexity Communication Codec audio (LC3), or
ETSI TS 103 634 Low Complexity Communication Codec plus (LC3plus).
.PP
This muxer accepts a single \fBlc3\fR audio stream.
.SS lrc
.IX Subsection "lrc"
LRC lyrics file format muxer.
.PP
LRC (short for LyRiCs) is a computer file format that synchronizes
song lyrics with an audio file, such as MP3, Vorbis, or MIDI.
.PP
This muxer accepts a single \fBsubrip\fR or \fBtext\fR subtitles stream.
.PP
\fIMetadata\fR
.IX Subsection "Metadata"
.PP
The following metadata tags are converted to the format corresponding
metadata:
.IP \fBtitle\fR 4
.IX Item "title"
.PD 0
.IP \fBalbum\fR 4
.IX Item "album"
.IP \fBartist\fR 4
.IX Item "artist"
.IP \fBauthor\fR 4
.IX Item "author"
.IP \fBcreator\fR 4
.IX Item "creator"
.IP \fBencoder\fR 4
.IX Item "encoder"
.IP \fBencoder_version\fR 4
.IX Item "encoder_version"
.PD
.PP
If \fBencoder_version\fR is not explicitly set, it is automatically
set to the libavformat version.
.SS matroska
.IX Subsection "matroska"
Matroska container muxer.
.PP
This muxer implements the matroska and webm container specs.
.PP
\fIMetadata\fR
.IX Subsection "Metadata"
.PP
The recognized metadata settings in this muxer are:
.IP \fBtitle\fR 4
.IX Item "title"
Set title name provided to a single track. This gets mapped to
the FileDescription element for a stream written as attachment.
.IP \fBlanguage\fR 4
.IX Item "language"
Specify the language of the track in the Matroska languages form.
.Sp
The language can be either the 3 letters bibliographic ISO\-639\-2 (ISO
639\-2/B) form (like "fre" for French), or a language code mixed with a
country code for specialities in languages (like "fre-ca" for Canadian
French).
.IP \fBstereo_mode\fR 4
.IX Item "stereo_mode"
Set stereo 3D video layout of two views in a single video track.
.Sp
The following values are recognized:
.RS 4
.IP \fBmono\fR 4
.IX Item "mono"
video is not stereo
.IP \fBleft_right\fR 4
.IX Item "left_right"
Both views are arranged side by side, Left-eye view is on the left
.IP \fBbottom_top\fR 4
.IX Item "bottom_top"
Both views are arranged in top-bottom orientation, Left-eye view is at bottom
.IP \fBtop_bottom\fR 4
.IX Item "top_bottom"
Both views are arranged in top-bottom orientation, Left-eye view is on top
.IP \fBcheckerboard_rl\fR 4
.IX Item "checkerboard_rl"
Each view is arranged in a checkerboard interleaved pattern, Left-eye view being first
.IP \fBcheckerboard_lr\fR 4
.IX Item "checkerboard_lr"
Each view is arranged in a checkerboard interleaved pattern, Right-eye view being first
.IP \fBrow_interleaved_rl\fR 4
.IX Item "row_interleaved_rl"
Each view is constituted by a row based interleaving, Right-eye view is first row
.IP \fBrow_interleaved_lr\fR 4
.IX Item "row_interleaved_lr"
Each view is constituted by a row based interleaving, Left-eye view is first row
.IP \fBcol_interleaved_rl\fR 4
.IX Item "col_interleaved_rl"
Both views are arranged in a column based interleaving manner, Right-eye view is first column
.IP \fBcol_interleaved_lr\fR 4
.IX Item "col_interleaved_lr"
Both views are arranged in a column based interleaving manner, Left-eye view is first column
.IP \fBanaglyph_cyan_red\fR 4
.IX Item "anaglyph_cyan_red"
All frames are in anaglyph format viewable through red-cyan filters
.IP \fBright_left\fR 4
.IX Item "right_left"
Both views are arranged side by side, Right-eye view is on the left
.IP \fBanaglyph_green_magenta\fR 4
.IX Item "anaglyph_green_magenta"
All frames are in anaglyph format viewable through green-magenta filters
.IP \fBblock_lr\fR 4
.IX Item "block_lr"
Both eyes laced in one Block, Left-eye view is first
.IP \fBblock_rl\fR 4
.IX Item "block_rl"
Both eyes laced in one Block, Right-eye view is first
.RE
.RS 4
.RE
.PP
For example a 3D WebM clip can be created using the following command line:
.PP
.Vb 1
\&        ffmpeg \-i sample_left_right_clip.mpg \-an \-c:v libvpx \-metadata stereo_mode=left_right \-y stereo_clip.webm
.Ve
.PP
\fIOptions\fR
.IX Subsection "Options"
.IP "\fBreserve_index_space\fR \fIsize\fR" 4
.IX Item "reserve_index_space size"
By default, this muxer writes the index for seeking (called cues in Matroska
terms) at the end of the file, because it cannot know in advance how much space
to leave for the index at the beginning of the file. However for some use cases
\&\-\- e.g.  streaming where seeking is possible but slow \-\- it is useful to put the
index at the beginning of the file.
.Sp
If this option is set to a non-zero value, the muxer will reserve \fIsize\fR bytes
of space in the file header and then try to write the cues there when the muxing
finishes. If the reserved space does not suffice, no Cues will be written, the
file will be finalized and writing the trailer will return an error.
A safe size for most use cases should be about 50kB per hour of video.
.Sp
Note that cues are only written if the output is seekable and this option will
have no effect if it is not.
.IP "\fBcues_to_front\fR \fIbool\fR" 4
.IX Item "cues_to_front bool"
If set, the muxer will write the index at the beginning of the file
by shifting the main data if necessary. This can be combined with
reserve_index_space in which case the data is only shifted if
the initially reserved space turns out to be insufficient.
.Sp
This option is ignored if the output is unseekable.
.IP "\fBcluster_size_limit\fR \fIsize\fR" 4
.IX Item "cluster_size_limit size"
Store at most the provided amount of bytes in a cluster.
.Sp
If not specified, the limit is set automatically to a sensible
hardcoded fixed value.
.IP "\fBcluster_time_limit\fR \fIduration\fR" 4
.IX Item "cluster_time_limit duration"
Store at most the provided number of milliseconds in a cluster.
.Sp
If not specified, the limit is set automatically to a sensible
hardcoded fixed value.
.IP "\fBdash\fR \fIbool\fR" 4
.IX Item "dash bool"
Create a WebM file conforming to WebM DASH specification. By default
it is set to \f(CW\*(C`false\*(C'\fR.
.IP "\fBdash_track_number\fR \fIindex\fR" 4
.IX Item "dash_track_number index"
Track number for the DASH stream. By default it is set to \f(CW1\fR.
.IP "\fBlive\fR \fIbool\fR" 4
.IX Item "live bool"
Write files assuming it is a live stream. By default it is set to
\&\f(CW\*(C`false\*(C'\fR.
.IP "\fBallow_raw_vfw\fR \fIbool\fR" 4
.IX Item "allow_raw_vfw bool"
Allow raw VFW mode. By default it is set to \f(CW\*(C`false\*(C'\fR.
.IP "\fBflipped_raw_rgb\fR \fIbool\fR" 4
.IX Item "flipped_raw_rgb bool"
If set to \f(CW\*(C`true\*(C'\fR, store positive height for raw RGB bitmaps, which indicates
bitmap is stored bottom-up. Note that this option does not flip the bitmap
which has to be done manually beforehand, e.g. by using the \fBvflip\fR filter.
Default is \f(CW\*(C`false\*(C'\fR and indicates bitmap is stored top down.
.IP "\fBwrite_crc32\fR \fIbool\fR" 4
.IX Item "write_crc32 bool"
Write a CRC32 element inside every Level 1 element. By default it is
set to \f(CW\*(C`true\*(C'\fR. This option is ignored for WebM.
.IP "\fBdefault_mode\fR \fImode\fR" 4
.IX Item "default_mode mode"
Control how the FlagDefault of the output tracks will be set.
It influences which tracks players should play by default. The default mode
is \fBpassthrough\fR.
.RS 4
.IP \fBinfer\fR 4
.IX Item "infer"
Every track with disposition default will have the FlagDefault set.
Additionally, for each type of track (audio, video or subtitle), if no track
with disposition default of this type exists, then the first track of this type
will be marked as default (if existing). This ensures that the default flag
is set in a sensible way even if the input originated from containers that
lack the concept of default tracks.
.IP \fBinfer_no_subs\fR 4
.IX Item "infer_no_subs"
This mode is the same as infer except that if no subtitle track with
disposition default exists, no subtitle track will be marked as default.
.IP \fBpassthrough\fR 4
.IX Item "passthrough"
In this mode the FlagDefault is set if and only if the AV_DISPOSITION_DEFAULT
flag is set in the disposition of the corresponding stream.
.RE
.RS 4
.RE
.SS md5
.IX Subsection "md5"
MD5 testing format.
.PP
This is a variant of the \fBhash\fR muxer. Unlike that muxer, it
defaults to using the MD5 hash function.
.PP
See also the \fBhash\fR and \fBframemd5\fR muxers.
.PP
\fIExamples\fR
.IX Subsection "Examples"
.IP \(bu 4
To compute the MD5 hash of the input converted to raw
audio and video, and store it in the file \fIout.md5\fR:
.Sp
.Vb 1
\&        ffmpeg \-i INPUT \-f md5 out.md5
.Ve
.IP \(bu 4
To print the MD5 hash to stdout:
.Sp
.Vb 1
\&        ffmpeg \-i INPUT \-f md5 \-
.Ve
.SS microdvd
.IX Subsection "microdvd"
MicroDVD subtitle format muxer.
.PP
This muxer accepts a single \fBmicrodvd\fR subtitles stream.
.SS mmf
.IX Subsection "mmf"
Synthetic music Mobile Application Format (SMAF) format muxer.
.PP
SMAF is a music data format specified by Yamaha for portable
electronic devices, such as mobile phones and personal digital
assistants.
.PP
This muxer accepts a single \fBadpcm_yamaha\fR audio stream.
.SS mp3
.IX Subsection "mp3"
The MP3 muxer writes a raw MP3 stream with the following optional features:
.IP \(bu 4
An ID3v2 metadata header at the beginning (enabled by default). Versions 2.3 and
2.4 are supported, the \f(CW\*(C`id3v2_version\*(C'\fR private option controls which one is
used (3 or 4). Setting \f(CW\*(C`id3v2_version\*(C'\fR to 0 disables the ID3v2 header
completely.
.Sp
The muxer supports writing attached pictures (APIC frames) to the ID3v2 header.
The pictures are supplied to the muxer in form of a video stream with a single
packet. There can be any number of those streams, each will correspond to a
single APIC frame.  The stream metadata tags \fItitle\fR and \fIcomment\fR map
to APIC \fIdescription\fR and \fIpicture type\fR respectively. See
<\fBhttp://id3.org/id3v2.4.0\-frames\fR> for allowed picture types.
.Sp
Note that the APIC frames must be written at the beginning, so the muxer will
buffer the audio frames until it gets all the pictures. It is therefore advised
to provide the pictures as soon as possible to avoid excessive buffering.
.IP \(bu 4
A Xing/LAME frame right after the ID3v2 header (if present). It is enabled by
default, but will be written only if the output is seekable. The
\&\f(CW\*(C`write_xing\*(C'\fR private option can be used to disable it.  The frame contains
various information that may be useful to the decoder, like the audio duration
or encoder delay.
.IP \(bu 4
A legacy ID3v1 tag at the end of the file (disabled by default). It may be
enabled with the \f(CW\*(C`write_id3v1\*(C'\fR private option, but as its capabilities are
very limited, its usage is not recommended.
.PP
Examples:
.PP
Write an mp3 with an ID3v2.3 header and an ID3v1 footer:
.PP
.Vb 1
\&        ffmpeg \-i INPUT \-id3v2_version 3 \-write_id3v1 1 out.mp3
.Ve
.PP
To attach a picture to an mp3 file select both the audio and the picture stream
with \f(CW\*(C`map\*(C'\fR:
.PP
.Vb 2
\&        ffmpeg \-i input.mp3 \-i cover.png \-c copy \-map 0 \-map 1
\&        \-metadata:s:v title="Album cover" \-metadata:s:v comment="Cover (Front)" out.mp3
.Ve
.PP
Write a "clean" MP3 without any extra features:
.PP
.Vb 1
\&        ffmpeg \-i input.wav \-write_xing 0 \-id3v2_version 0 out.mp3
.Ve
.SS mpegts
.IX Subsection "mpegts"
MPEG transport stream muxer.
.PP
This muxer implements ISO 13818\-1 and part of ETSI EN 300 468.
.PP
The recognized metadata settings in mpegts muxer are \f(CW\*(C`service_provider\*(C'\fR
and \f(CW\*(C`service_name\*(C'\fR. If they are not set the default for
\&\f(CW\*(C`service_provider\*(C'\fR is \fBFFmpeg\fR and the default for
\&\f(CW\*(C`service_name\*(C'\fR is \fBService01\fR.
.PP
\fIOptions\fR
.IX Subsection "Options"
.PP
The muxer options are:
.IP "\fBmpegts_transport_stream_id\fR \fIinteger\fR" 4
.IX Item "mpegts_transport_stream_id integer"
Set the \fBtransport_stream_id\fR. This identifies a transponder in DVB.
Default is \f(CW0x0001\fR.
.IP "\fBmpegts_original_network_id\fR \fIinteger\fR" 4
.IX Item "mpegts_original_network_id integer"
Set the \fBoriginal_network_id\fR. This is unique identifier of a
network in DVB. Its main use is in the unique identification of a service
through the path \fBOriginal_Network_ID, Transport_Stream_ID\fR. Default
is \f(CW0x0001\fR.
.IP "\fBmpegts_service_id\fR \fIinteger\fR" 4
.IX Item "mpegts_service_id integer"
Set the \fBservice_id\fR, also known as program in DVB. Default is
\&\f(CW0x0001\fR.
.IP "\fBmpegts_service_type\fR \fIinteger\fR" 4
.IX Item "mpegts_service_type integer"
Set the program \fBservice_type\fR. Default is \f(CW\*(C`digital_tv\*(C'\fR.
Accepts the following options:
.RS 4
.IP \fBhex_value\fR 4
.IX Item "hex_value"
Any hexadecimal value between \f(CW0x01\fR and \f(CW0xff\fR as defined in
ETSI 300 468.
.IP \fBdigital_tv\fR 4
.IX Item "digital_tv"
Digital TV service.
.IP \fBdigital_radio\fR 4
.IX Item "digital_radio"
Digital Radio service.
.IP \fBteletext\fR 4
.IX Item "teletext"
Teletext service.
.IP \fBadvanced_codec_digital_radio\fR 4
.IX Item "advanced_codec_digital_radio"
Advanced Codec Digital Radio service.
.IP \fBmpeg2_digital_hdtv\fR 4
.IX Item "mpeg2_digital_hdtv"
MPEG2 Digital HDTV service.
.IP \fBadvanced_codec_digital_sdtv\fR 4
.IX Item "advanced_codec_digital_sdtv"
Advanced Codec Digital SDTV service.
.IP \fBadvanced_codec_digital_hdtv\fR 4
.IX Item "advanced_codec_digital_hdtv"
Advanced Codec Digital HDTV service.
.RE
.RS 4
.RE
.IP "\fBmpegts_pmt_start_pid\fR \fIinteger\fR" 4
.IX Item "mpegts_pmt_start_pid integer"
Set the first PID for PMTs. Default is \f(CW0x1000\fR, minimum is \f(CW0x0020\fR,
maximum is \f(CW0x1ffa\fR. This option has no effect in m2ts mode where the PMT
PID is fixed \f(CW0x0100\fR.
.IP "\fBmpegts_start_pid\fR \fIinteger\fR" 4
.IX Item "mpegts_start_pid integer"
Set the first PID for elementary streams. Default is \f(CW0x0100\fR, minimum is
\&\f(CW0x0020\fR, maximum is \f(CW0x1ffa\fR. This option has no effect in m2ts mode
where the elementary stream PIDs are fixed.
.IP "\fBmpegts_m2ts_mode\fR \fIboolean\fR" 4
.IX Item "mpegts_m2ts_mode boolean"
Enable m2ts mode if set to \f(CW1\fR. Default value is \f(CW\-1\fR which
disables m2ts mode.
.IP "\fBmuxrate\fR \fIinteger\fR" 4
.IX Item "muxrate integer"
Set a constant muxrate. Default is VBR.
.IP "\fBpes_payload_size\fR \fIinteger\fR" 4
.IX Item "pes_payload_size integer"
Set minimum PES packet payload in bytes. Default is \f(CW2930\fR.
.IP "\fBmpegts_flags\fR \fIflags\fR" 4
.IX Item "mpegts_flags flags"
Set mpegts flags. Accepts the following options:
.RS 4
.IP \fBresend_headers\fR 4
.IX Item "resend_headers"
Reemit PAT/PMT before writing the next packet.
.IP \fBlatm\fR 4
.IX Item "latm"
Use LATM packetization for AAC.
.IP \fBpat_pmt_at_frames\fR 4
.IX Item "pat_pmt_at_frames"
Reemit PAT and PMT at each video frame.
.IP \fBsystem_b\fR 4
.IX Item "system_b"
Conform to System B (DVB) instead of System A (ATSC).
.IP \fBinitial_discontinuity\fR 4
.IX Item "initial_discontinuity"
Mark the initial packet of each stream as discontinuity.
.IP \fBnit\fR 4
.IX Item "nit"
Emit NIT table.
.IP \fBomit_rai\fR 4
.IX Item "omit_rai"
Disable writing of random access indicator.
.RE
.RS 4
.RE
.IP "\fBmpegts_copyts\fR \fIboolean\fR" 4
.IX Item "mpegts_copyts boolean"
Preserve original timestamps, if value is set to \f(CW1\fR. Default value
is \f(CW\-1\fR, which results in shifting timestamps so that they start from 0.
.IP "\fBomit_video_pes_length\fR \fIboolean\fR" 4
.IX Item "omit_video_pes_length boolean"
Omit the PES packet length for video packets. Default is \f(CW1\fR (true).
.IP "\fBpcr_period\fR \fIinteger\fR" 4
.IX Item "pcr_period integer"
Override the default PCR retransmission time in milliseconds. Default is
\&\f(CW\-1\fR which means that the PCR interval will be determined automatically:
20 ms is used for CBR streams, the highest multiple of the frame duration which
is less than 100 ms is used for VBR streams.
.IP "\fBpat_period\fR \fIduration\fR" 4
.IX Item "pat_period duration"
Maximum time in seconds between PAT/PMT tables. Default is \f(CW0.1\fR.
.IP "\fBsdt_period\fR \fIduration\fR" 4
.IX Item "sdt_period duration"
Maximum time in seconds between SDT tables. Default is \f(CW0.5\fR.
.IP "\fBnit_period\fR \fIduration\fR" 4
.IX Item "nit_period duration"
Maximum time in seconds between NIT tables. Default is \f(CW0.5\fR.
.IP "\fBtables_version\fR \fIinteger\fR" 4
.IX Item "tables_version integer"
Set PAT, PMT, SDT and NIT version (default \f(CW0\fR, valid values are from 0 to 31, inclusively).
This option allows updating stream structure so that standard consumer may
detect the change. To do so, reopen output \f(CW\*(C`AVFormatContext\*(C'\fR (in case of API
usage) or restart \fBffmpeg\fR instance, cyclically changing
\&\fBtables_version\fR value:
.Sp
.Vb 7
\&        ffmpeg \-i source1.ts \-codec copy \-f mpegts \-tables_version 0 udp://*******:1111
\&        ffmpeg \-i source2.ts \-codec copy \-f mpegts \-tables_version 1 udp://*******:1111
\&        ...
\&        ffmpeg \-i source3.ts \-codec copy \-f mpegts \-tables_version 31 udp://*******:1111
\&        ffmpeg \-i source1.ts \-codec copy \-f mpegts \-tables_version 0 udp://*******:1111
\&        ffmpeg \-i source2.ts \-codec copy \-f mpegts \-tables_version 1 udp://*******:1111
\&        ...
.Ve
.PP
\fIExample\fR
.IX Subsection "Example"
.PP
.Vb 9
\&        ffmpeg \-i file.mpg \-c copy \e
\&             \-mpegts_original_network_id 0x1122 \e
\&             \-mpegts_transport_stream_id 0x3344 \e
\&             \-mpegts_service_id 0x5566 \e
\&             \-mpegts_pmt_start_pid 0x1500 \e
\&             \-mpegts_start_pid 0x150 \e
\&             \-metadata service_provider="Some provider" \e
\&             \-metadata service_name="Some Channel" \e
\&             out.ts
.Ve
.SS "mxf, mxf_d10, mxf_opatom"
.IX Subsection "mxf, mxf_d10, mxf_opatom"
MXF muxer.
.PP
\fIOptions\fR
.IX Subsection "Options"
.PP
The muxer options are:
.IP "\fBstore_user_comments\fR \fIbool\fR" 4
.IX Item "store_user_comments bool"
Set if user comments should be stored if available or never.
IRT D\-10 does not allow user comments. The default is thus to write them for
mxf and mxf_opatom but not for mxf_d10
.SS null
.IX Subsection "null"
Null muxer.
.PP
This muxer does not generate any output file, it is mainly useful for
testing or benchmarking purposes.
.PP
For example to benchmark decoding with \fBffmpeg\fR you can use the
command:
.PP
.Vb 1
\&        ffmpeg \-benchmark \-i INPUT \-f null out.null
.Ve
.PP
Note that the above command does not read or write the \fIout.null\fR
file, but specifying the output file is required by the \fBffmpeg\fR
syntax.
.PP
Alternatively you can write the command as:
.PP
.Vb 1
\&        ffmpeg \-benchmark \-i INPUT \-f null \-
.Ve
.SS nut
.IX Subsection "nut"
.IP "\fB\-syncpoints\fR \fIflags\fR" 4
.IX Item "-syncpoints flags"
Change the syncpoint usage in nut:
.RS 4
.IP "\fIdefault\fR \fBuse the normal low-overhead seeking aids.\fR" 4
.IX Item "default use the normal low-overhead seeking aids."
.PD 0
.IP "\fInone\fR \fBdo not use the syncpoints at all, reducing the overhead but making the stream non-seekable;\fR" 4
.IX Item "none do not use the syncpoints at all, reducing the overhead but making the stream non-seekable;"
.PD
.Vb 5
\&    Use of this option is not recommended, as the resulting files are very damage
\&    sensitive and seeking is not possible. Also in general the overhead from
\&    syncpoints is negligible. Note, \-C<write_index> 0 can be used to disable
\&    all growing data tables, allowing to mux endless streams with limited memory
\&    and without these disadvantages.
.Ve
.IP "\fItimestamped\fR \fBextend the syncpoint with a wallclock field.\fR" 4
.IX Item "timestamped extend the syncpoint with a wallclock field."
.RE
.RS 4
.Sp
The \fInone\fR and \fItimestamped\fR flags are experimental.
.RE
.IP "\fB\-write_index\fR \fIbool\fR" 4
.IX Item "-write_index bool"
Write index at the end, the default is to write an index.
.PP
.Vb 1
\&        ffmpeg \-i INPUT \-f_strict experimental \-syncpoints none \- | processor
.Ve
.SS ogg
.IX Subsection "ogg"
Ogg container muxer.
.IP "\fB\-page_duration\fR \fIduration\fR" 4
.IX Item "-page_duration duration"
Preferred page duration, in microseconds. The muxer will attempt to create
pages that are approximately \fIduration\fR microseconds long. This allows the
user to compromise between seek granularity and container overhead. The default
is 1 second. A value of 0 will fill all segments, making pages as large as
possible. A value of 1 will effectively use 1 packet-per-page in most
situations, giving a small seek granularity at the cost of additional container
overhead.
.IP "\fB\-serial_offset\fR \fIvalue\fR" 4
.IX Item "-serial_offset value"
Serial value from which to set the streams serial number.
Setting it to different and sufficiently large values ensures that the produced
ogg files can be safely chained.
.SS rcwt
.IX Subsection "rcwt"
RCWT (Raw Captions With Time) is a format native to ccextractor, a commonly
used open source tool for processing 608/708 Closed Captions (CC) sources.
It can be used to archive the original extracted CC bitstream and to produce
a source file for later processing or conversion. The format allows
for interoperability between ccextractor and FFmpeg, is simple to parse,
and can be used to create a backup of the CC presentation.
.PP
This muxer implements the specification as of March 2024, which has
been stable and unchanged since April 2014.
.PP
This muxer will have some nuances from the way that ccextractor muxes RCWT.
No compatibility issues when processing the output with ccextractor
have been observed as a result of this so far, but mileage may vary
and outputs will not be a bit-exact match.
.PP
A free specification of RCWT can be found here:
<\fBhttps://github.com/CCExtractor/ccextractor/blob/master/docs/BINARY_FILE_FORMAT.TXT\fR>
.PP
\fIExamples\fR
.IX Subsection "Examples"
.IP \(bu 4
Extract Closed Captions to RCWT using lavfi:
.Sp
.Vb 1
\&        ffmpeg \-f lavfi \-i "movie=INPUT.mkv[out+subcc]" \-map 0:s:0 \-c:s copy \-f rcwt CC.rcwt.bin
.Ve
.SS "segment, stream_segment, ssegment"
.IX Subsection "segment, stream_segment, ssegment"
Basic stream segmenter.
.PP
This muxer outputs streams to a number of separate files of nearly
fixed duration. Output filename pattern can be set in a fashion
similar to \fBimage2\fR, or by using a \f(CW\*(C`strftime\*(C'\fR template if
the \fBstrftime\fR option is enabled.
.PP
\&\f(CW\*(C`stream_segment\*(C'\fR is a variant of the muxer used to write to
streaming output formats, i.e. which do not require global headers,
and is recommended for outputting e.g. to MPEG transport stream segments.
\&\f(CW\*(C`ssegment\*(C'\fR is a shorter alias for \f(CW\*(C`stream_segment\*(C'\fR.
.PP
Every segment starts with a keyframe of the selected reference stream,
which is set through the \fBreference_stream\fR option.
.PP
Note that if you want accurate splitting for a video file, you need to
make the input key frames correspond to the exact splitting times
expected by the segmenter, or the segment muxer will start the new
segment with the key frame found next after the specified start
time.
.PP
The segment muxer works best with a single constant frame rate video.
.PP
Optionally it can generate a list of the created segments, by setting
the option \fIsegment_list\fR. The list type is specified by the
\&\fIsegment_list_type\fR option. The entry filenames in the segment
list are set by default to the basename of the corresponding segment
files.
.PP
See also the \fBhls\fR muxer, which provides a more specific
implementation for HLS segmentation.
.PP
\fIOptions\fR
.IX Subsection "Options"
.PP
The segment muxer supports the following options:
.IP "\fBincrement_tc\fR \fI1|0\fR" 4
.IX Item "increment_tc 1|0"
if set to \f(CW1\fR, increment timecode between each segment
If this is selected, the input need to have
a timecode in the first video stream. Default value is
\&\f(CW0\fR.
.IP "\fBreference_stream\fR \fIspecifier\fR" 4
.IX Item "reference_stream specifier"
Set the reference stream, as specified by the string \fIspecifier\fR.
If \fIspecifier\fR is set to \f(CW\*(C`auto\*(C'\fR, the reference is chosen
automatically. Otherwise it must be a stream specifier (see the ``Stream
specifiers'' chapter in the ffmpeg manual) which specifies the
reference stream. The default value is \f(CW\*(C`auto\*(C'\fR.
.IP "\fBsegment_format\fR \fIformat\fR" 4
.IX Item "segment_format format"
Override the inner container format, by default it is guessed by the filename
extension.
.IP "\fBsegment_format_options\fR \fIoptions_list\fR" 4
.IX Item "segment_format_options options_list"
Set output format options using a :\-separated list of key=value
parameters. Values containing the \f(CW\*(C`:\*(C'\fR special character must be
escaped.
.IP "\fBsegment_list\fR \fIname\fR" 4
.IX Item "segment_list name"
Generate also a listfile named \fIname\fR. If not specified no
listfile is generated.
.IP "\fBsegment_list_flags\fR \fIflags\fR" 4
.IX Item "segment_list_flags flags"
Set flags affecting the segment list generation.
.Sp
It currently supports the following flags:
.RS 4
.IP \fBcache\fR 4
.IX Item "cache"
Allow caching (only affects M3U8 list files).
.IP \fBlive\fR 4
.IX Item "live"
Allow live-friendly file generation.
.RE
.RS 4
.RE
.IP "\fBsegment_list_size\fR \fIsize\fR" 4
.IX Item "segment_list_size size"
Update the list file so that it contains at most \fIsize\fR
segments. If 0 the list file will contain all the segments. Default
value is 0.
.IP "\fBsegment_list_entry_prefix\fR \fIprefix\fR" 4
.IX Item "segment_list_entry_prefix prefix"
Prepend \fIprefix\fR to each entry. Useful to generate absolute paths.
By default no prefix is applied.
.IP "\fBsegment_list_type\fR \fItype\fR" 4
.IX Item "segment_list_type type"
Select the listing format.
.Sp
The following values are recognized:
.RS 4
.IP \fBflat\fR 4
.IX Item "flat"
Generate a flat list for the created segments, one segment per line.
.IP "\fBcsv, ext\fR" 4
.IX Item "csv, ext"
Generate a list for the created segments, one segment per line,
each line matching the format (comma-separated values):
.Sp
.Vb 1
\&        <segment_filename>,<segment_start_time>,<segment_end_time>
.Ve
.Sp
\&\fIsegment_filename\fR is the name of the output file generated by the
muxer according to the provided pattern. CSV escaping (according to
RFC4180) is applied if required.
.Sp
\&\fIsegment_start_time\fR and \fIsegment_end_time\fR specify
the segment start and end time expressed in seconds.
.Sp
A list file with the suffix \f(CW".csv"\fR or \f(CW".ext"\fR will
auto-select this format.
.Sp
\&\fBext\fR is deprecated in favor or \fBcsv\fR.
.IP \fBffconcat\fR 4
.IX Item "ffconcat"
Generate an ffconcat file for the created segments. The resulting file
can be read using the FFmpeg \fBconcat\fR demuxer.
.Sp
A list file with the suffix \f(CW".ffcat"\fR or \f(CW".ffconcat"\fR will
auto-select this format.
.IP \fBm3u8\fR 4
.IX Item "m3u8"
Generate an extended M3U8 file, version 3, compliant with
<\fBhttp://tools.ietf.org/id/draft\-pantos\-http\-live\-streaming\fR>.
.Sp
A list file with the suffix \f(CW".m3u8"\fR will auto-select this format.
.RE
.RS 4
.Sp
If not specified the type is guessed from the list file name suffix.
.RE
.IP "\fBsegment_time\fR \fItime\fR" 4
.IX Item "segment_time time"
Set segment duration to \fItime\fR, the value must be a duration
specification. Default value is "2". See also the
\&\fBsegment_times\fR option.
.Sp
Note that splitting may not be accurate, unless you force the
reference stream key-frames at the given time. See the introductory
notice and the examples below.
.IP "\fBmin_seg_duration\fR \fItime\fR" 4
.IX Item "min_seg_duration time"
Set minimum segment duration to \fItime\fR, the value must be a duration
specification. This prevents the muxer ending segments at a duration below
this value. Only effective with \f(CW\*(C`segment_time\*(C'\fR. Default value is "0".
.IP "\fBsegment_atclocktime\fR \fI1|0\fR" 4
.IX Item "segment_atclocktime 1|0"
If set to "1" split at regular clock time intervals starting from 00:00
o'clock. The \fItime\fR value specified in \fBsegment_time\fR is
used for setting the length of the splitting interval.
.Sp
For example with \fBsegment_time\fR set to "900" this makes it possible
to create files at 12:00 o'clock, 12:15, 12:30, etc.
.Sp
Default value is "0".
.IP "\fBsegment_clocktime_offset\fR \fIduration\fR" 4
.IX Item "segment_clocktime_offset duration"
Delay the segment splitting times with the specified duration when using
\&\fBsegment_atclocktime\fR.
.Sp
For example with \fBsegment_time\fR set to "900" and
\&\fBsegment_clocktime_offset\fR set to "300" this makes it possible to
create files at 12:05, 12:20, 12:35, etc.
.Sp
Default value is "0".
.IP "\fBsegment_clocktime_wrap_duration\fR \fIduration\fR" 4
.IX Item "segment_clocktime_wrap_duration duration"
Force the segmenter to only start a new segment if a packet reaches the muxer
within the specified duration after the segmenting clock time. This way you
can make the segmenter more resilient to backward local time jumps, such as
leap seconds or transition to standard time from daylight savings time.
.Sp
Default is the maximum possible duration which means starting a new segment
regardless of the elapsed time since the last clock time.
.IP "\fBsegment_time_delta\fR \fIdelta\fR" 4
.IX Item "segment_time_delta delta"
Specify the accuracy time when selecting the start time for a
segment, expressed as a duration specification. Default value is "0".
.Sp
When delta is specified a key-frame will start a new segment if its
PTS satisfies the relation:
.Sp
.Vb 1
\&        PTS >= start_time \- time_delta
.Ve
.Sp
This option is useful when splitting video content, which is always
split at GOP boundaries, in case a key frame is found just before the
specified split time.
.Sp
In particular may be used in combination with the \fIffmpeg\fR option
\&\fIforce_key_frames\fR. The key frame times specified by
\&\fIforce_key_frames\fR may not be set accurately because of rounding
issues, with the consequence that a key frame time may result set just
before the specified time. For constant frame rate videos a value of
1/(2*\fIframe_rate\fR) should address the worst case mismatch between
the specified time and the time set by \fIforce_key_frames\fR.
.IP "\fBsegment_times\fR \fItimes\fR" 4
.IX Item "segment_times times"
Specify a list of split points. \fItimes\fR contains a list of comma
separated duration specifications, in increasing order. See also
the \fBsegment_time\fR option.
.IP "\fBsegment_frames\fR \fIframes\fR" 4
.IX Item "segment_frames frames"
Specify a list of split video frame numbers. \fIframes\fR contains a
list of comma separated integer numbers, in increasing order.
.Sp
This option specifies to start a new segment whenever a reference
stream key frame is found and the sequential number (starting from 0)
of the frame is greater or equal to the next value in the list.
.IP "\fBsegment_wrap\fR \fIlimit\fR" 4
.IX Item "segment_wrap limit"
Wrap around segment index once it reaches \fIlimit\fR.
.IP "\fBsegment_start_number\fR \fInumber\fR" 4
.IX Item "segment_start_number number"
Set the sequence number of the first segment. Defaults to \f(CW0\fR.
.IP "\fBstrftime\fR \fI1|0\fR" 4
.IX Item "strftime 1|0"
Use the \f(CW\*(C`strftime\*(C'\fR function to define the name of the new
segments to write. If this is selected, the output segment name must
contain a \f(CW\*(C`strftime\*(C'\fR function template. Default value is
\&\f(CW0\fR.
.IP "\fBbreak_non_keyframes\fR \fI1|0\fR" 4
.IX Item "break_non_keyframes 1|0"
If enabled, allow segments to start on frames other than keyframes. This
improves behavior on some players when the time between keyframes is
inconsistent, but may make things worse on others, and can cause some oddities
during seeking. Defaults to \f(CW0\fR.
.IP "\fBreset_timestamps\fR \fI1|0\fR" 4
.IX Item "reset_timestamps 1|0"
Reset timestamps at the beginning of each segment, so that each segment
will start with near-zero timestamps. It is meant to ease the playback
of the generated segments. May not work with some combinations of
muxers/codecs. It is set to \f(CW0\fR by default.
.IP "\fBinitial_offset\fR \fIoffset\fR" 4
.IX Item "initial_offset offset"
Specify timestamp offset to apply to the output packet timestamps. The
argument must be a time duration specification, and defaults to 0.
.IP "\fBwrite_empty_segments\fR \fI1|0\fR" 4
.IX Item "write_empty_segments 1|0"
If enabled, write an empty segment if there are no packets during the period a
segment would usually span. Otherwise, the segment will be filled with the next
packet written. Defaults to \f(CW0\fR.
.PP
Make sure to require a closed GOP when encoding and to set the GOP
size to fit your segment time constraint.
.PP
\fIExamples\fR
.IX Subsection "Examples"
.IP \(bu 4
Remux the content of file \fIin.mkv\fR to a list of segments
\&\fIout\-000.nut\fR, \fIout\-001.nut\fR, etc., and write the list of
generated segments to \fIout.list\fR:
.Sp
.Vb 1
\&        ffmpeg \-i in.mkv \-codec hevc \-flags +cgop \-g 60 \-map 0 \-f segment \-segment_list out.list out%03d.nut
.Ve
.IP \(bu 4
Segment input and set output format options for the output segments:
.Sp
.Vb 1
\&        ffmpeg \-i in.mkv \-f segment \-segment_time 10 \-segment_format_options movflags=+faststart out%03d.mp4
.Ve
.IP \(bu 4
Segment the input file according to the split points specified by the
\&\fIsegment_times\fR option:
.Sp
.Vb 1
\&        ffmpeg \-i in.mkv \-codec copy \-map 0 \-f segment \-segment_list out.csv \-segment_times 1,2,3,5,8,13,21 out%03d.nut
.Ve
.IP \(bu 4
Use the \fBffmpeg\fR \fBforce_key_frames\fR
option to force key frames in the input at the specified location, together
with the segment option \fBsegment_time_delta\fR to account for
possible roundings operated when setting key frame times.
.Sp
.Vb 2
\&        ffmpeg \-i in.mkv \-force_key_frames 1,2,3,5,8,13,21 \-codec:v mpeg4 \-codec:a pcm_s16le \-map 0 \e
\&        \-f segment \-segment_list out.csv \-segment_times 1,2,3,5,8,13,21 \-segment_time_delta 0.05 out%03d.nut
.Ve
.Sp
In order to force key frames on the input file, transcoding is
required.
.IP \(bu 4
Segment the input file by splitting the input file according to the
frame numbers sequence specified with the \fBsegment_frames\fR option:
.Sp
.Vb 1
\&        ffmpeg \-i in.mkv \-codec copy \-map 0 \-f segment \-segment_list out.csv \-segment_frames 100,200,300,500,800 out%03d.nut
.Ve
.IP \(bu 4
Convert the \fIin.mkv\fR to TS segments using the \f(CW\*(C`libx264\*(C'\fR
and \f(CW\*(C`aac\*(C'\fR encoders:
.Sp
.Vb 1
\&        ffmpeg \-i in.mkv \-map 0 \-codec:v libx264 \-codec:a aac \-f ssegment \-segment_list out.list out%03d.ts
.Ve
.IP \(bu 4
Segment the input file, and create an M3U8 live playlist (can be used
as live HLS source):
.Sp
.Vb 2
\&        ffmpeg \-re \-i in.mkv \-codec copy \-map 0 \-f segment \-segment_list playlist.m3u8 \e
\&        \-segment_list_flags +live \-segment_time 10 out%03d.mkv
.Ve
.SS smoothstreaming
.IX Subsection "smoothstreaming"
Smooth Streaming muxer generates a set of files (Manifest, chunks) suitable for serving with conventional web server.
.IP \fBwindow_size\fR 4
.IX Item "window_size"
Specify the number of fragments kept in the manifest. Default 0 (keep all).
.IP \fBextra_window_size\fR 4
.IX Item "extra_window_size"
Specify the number of fragments kept outside of the manifest before removing from disk. Default 5.
.IP \fBlookahead_count\fR 4
.IX Item "lookahead_count"
Specify the number of lookahead fragments. Default 2.
.IP \fBmin_frag_duration\fR 4
.IX Item "min_frag_duration"
Specify the minimum fragment duration (in microseconds). Default 5000000.
.IP \fBremove_at_exit\fR 4
.IX Item "remove_at_exit"
Specify whether to remove all fragments when finished. Default 0 (do not remove).
.SS streamhash
.IX Subsection "streamhash"
Per stream hash testing format.
.PP
This muxer computes and prints a cryptographic hash of all the input frames,
on a per-stream basis. This can be used for equality checks without having
to do a complete binary comparison.
.PP
By default audio frames are converted to signed 16\-bit raw audio and
video frames to raw video before computing the hash, but the output
of explicit conversions to other codecs can also be used. Timestamps
are ignored. It uses the SHA\-256 cryptographic hash function by default,
but supports several other algorithms.
.PP
The output of the muxer consists of one line per stream of the form:
\&\fIstreamindex\fR,\fIstreamtype\fR,\fIalgo\fR=\fIhash\fR, where
\&\fIstreamindex\fR is the index of the mapped stream, \fIstreamtype\fR is a
single character indicating the type of stream, \fIalgo\fR is a short string
representing the hash function used, and \fIhash\fR is a hexadecimal number
representing the computed hash.
.IP "\fBhash\fR \fIalgorithm\fR" 4
.IX Item "hash algorithm"
Use the cryptographic hash function specified by the string \fIalgorithm\fR.
Supported values include \f(CW\*(C`MD5\*(C'\fR, \f(CW\*(C`murmur3\*(C'\fR, \f(CW\*(C`RIPEMD128\*(C'\fR,
\&\f(CW\*(C`RIPEMD160\*(C'\fR, \f(CW\*(C`RIPEMD256\*(C'\fR, \f(CW\*(C`RIPEMD320\*(C'\fR, \f(CW\*(C`SHA160\*(C'\fR,
\&\f(CW\*(C`SHA224\*(C'\fR, \f(CW\*(C`SHA256\*(C'\fR (default), \f(CW\*(C`SHA512/224\*(C'\fR, \f(CW\*(C`SHA512/256\*(C'\fR,
\&\f(CW\*(C`SHA384\*(C'\fR, \f(CW\*(C`SHA512\*(C'\fR, \f(CW\*(C`CRC32\*(C'\fR and \f(CW\*(C`adler32\*(C'\fR.
.PP
\fIExamples\fR
.IX Subsection "Examples"
.PP
To compute the SHA\-256 hash of the input converted to raw audio and
video, and store it in the file \fIout.sha256\fR:
.PP
.Vb 1
\&        ffmpeg \-i INPUT \-f streamhash out.sha256
.Ve
.PP
To print an MD5 hash to stdout use the command:
.PP
.Vb 1
\&        ffmpeg \-i INPUT \-f streamhash \-hash md5 \-
.Ve
.PP
See also the \fBhash\fR and \fBframehash\fR muxers.
.SS tee
.IX Subsection "tee"
The tee muxer can be used to write the same data to several outputs, such as files or streams.
It can be used, for example, to stream a video over a network and save it to disk at the same time.
.PP
It is different from specifying several outputs to the \fBffmpeg\fR
command-line tool. With the tee muxer, the audio and video data will be encoded only once.
With conventional multiple outputs, multiple encoding operations in parallel are initiated,
which can be a very expensive process. The tee muxer is not useful when using the libavformat API
directly because it is then possible to feed the same packets to several muxers directly.
.PP
Since the tee muxer does not represent any particular output format, ffmpeg cannot auto-select
output streams. So all streams intended for output must be specified using \f(CW\*(C`\-map\*(C'\fR. See
the examples below.
.PP
Some encoders may need different options depending on the output format;
the auto-detection of this can not work with the tee muxer, so they need to be explicitly specified.
The main example is the \fBglobal_header\fR flag.
.PP
The slave outputs are specified in the file name given to the muxer,
separated by '|'. If any of the slave name contains the '|' separator,
leading or trailing spaces or any special character, those must be
escaped (see \fBthe "Quoting and escaping"
section in the ffmpeg\-utils\|(1) manual\fR).
.PP
\fIOptions\fR
.IX Subsection "Options"
.IP "\fBuse_fifo\fR \fIbool\fR" 4
.IX Item "use_fifo bool"
If set to 1, slave outputs will be processed in separate threads using the \fBfifo\fR
muxer. This allows to compensate for different speed/latency/reliability of
outputs and setup transparent recovery. By default this feature is turned off.
.IP \fBfifo_options\fR 4
.IX Item "fifo_options"
Options to pass to fifo pseudo-muxer instances. See \fBfifo\fR.
.PP
Muxer options can be specified for each slave by prepending them as a list of
\&\fIkey\fR=\fIvalue\fR pairs separated by ':', between square brackets. If
the options values contain a special character or the ':' separator, they
must be escaped; note that this is a second level escaping.
.PP
The following special options are also recognized:
.IP \fBf\fR 4
.IX Item "f"
Specify the format name. Required if it cannot be guessed from the
output URL.
.IP \fBbsfs[/\fR\fIspec\fR\fB]\fR 4
.IX Item "bsfs[/spec]"
Specify a list of bitstream filters to apply to the specified
output.
.Sp
It is possible to specify to which streams a given bitstream filter
applies, by appending a stream specifier to the option separated by
\&\f(CW\*(C`/\*(C'\fR. \fIspec\fR must be a stream specifier (see \fBFormat
stream specifiers\fR).
.Sp
If the stream specifier is not specified, the bitstream filters will be
applied to all streams in the output. This will cause that output operation
to fail if the output contains streams to which the bitstream filter cannot
be applied e.g. \f(CW\*(C`h264_mp4toannexb\*(C'\fR being applied to an output containing an audio stream.
.Sp
Options for a bitstream filter must be specified in the form of \f(CW\*(C`opt=value\*(C'\fR.
.Sp
Several bitstream filters can be specified, separated by ",".
.IP "\fBuse_fifo\fR \fIbool\fR" 4
.IX Item "use_fifo bool"
This allows to override tee muxer use_fifo option for individual slave muxer.
.IP \fBfifo_options\fR 4
.IX Item "fifo_options"
This allows to override tee muxer fifo_options for individual slave muxer.
See \fBfifo\fR.
.IP \fBselect\fR 4
.IX Item "select"
Select the streams that should be mapped to the slave output,
specified by a stream specifier. If not specified, this defaults to
all the mapped streams. This will cause that output operation to fail
if the output format does not accept all mapped streams.
.Sp
You may use multiple stream specifiers separated by commas (\f(CW\*(C`,\*(C'\fR) e.g.: \f(CW\*(C`a:0,v\*(C'\fR
.IP \fBonfail\fR 4
.IX Item "onfail"
Specify behaviour on output failure. This can be set to either \f(CW\*(C`abort\*(C'\fR (which is
default) or \f(CW\*(C`ignore\*(C'\fR. \f(CW\*(C`abort\*(C'\fR will cause whole process to fail in case of failure
on this slave output. \f(CW\*(C`ignore\*(C'\fR will ignore failure on this output, so other outputs
will continue without being affected.
.PP
\fIExamples\fR
.IX Subsection "Examples"
.IP \(bu 4
Encode something and both archive it in a WebM file and stream it
as MPEG-TS over UDP:
.Sp
.Vb 2
\&        ffmpeg \-i ... \-c:v libx264 \-c:a mp2 \-f tee \-map 0:v \-map 0:a
\&          "archive\-20121107.mkv|[f=mpegts]udp://**********:1234/"
.Ve
.IP \(bu 4
As above, but continue streaming even if output to local file fails
(for example local drive fills up):
.Sp
.Vb 2
\&        ffmpeg \-i ... \-c:v libx264 \-c:a mp2 \-f tee \-map 0:v \-map 0:a
\&          "[onfail=ignore]archive\-20121107.mkv|[f=mpegts]udp://**********:1234/"
.Ve
.IP \(bu 4
Use \fBffmpeg\fR to encode the input, and send the output
to three different destinations. The \f(CW\*(C`dump_extra\*(C'\fR bitstream
filter is used to add extradata information to all the output video
keyframes packets, as requested by the MPEG-TS format. The select
option is applied to \fIout.aac\fR in order to make it contain only
audio packets.
.Sp
.Vb 2
\&        ffmpeg \-i ... \-map 0 \-flags +global_header \-c:v libx264 \-c:a aac
\&               \-f tee "[bsfs/v=dump_extra=freq=keyframe]out.ts|[movflags=+faststart]out.mp4|[select=a]out.aac"
.Ve
.IP \(bu 4
As above, but select only stream \f(CW\*(C`a:1\*(C'\fR for the audio output. Note
that a second level escaping must be performed, as ":" is a special
character used to separate options.
.Sp
.Vb 2
\&        ffmpeg \-i ... \-map 0 \-flags +global_header \-c:v libx264 \-c:a aac
\&               \-f tee "[bsfs/v=dump_extra=freq=keyframe]out.ts|[movflags=+faststart]out.mp4|[select=\e\*(Aqa:1\e\*(Aq]out.aac"
.Ve
.SS webm_chunk
.IX Subsection "webm_chunk"
WebM Live Chunk Muxer.
.PP
This muxer writes out WebM headers and chunks as separate files which can be
consumed by clients that support WebM Live streams via DASH.
.PP
\fIOptions\fR
.IX Subsection "Options"
.PP
This muxer supports the following options:
.IP \fBchunk_start_index\fR 4
.IX Item "chunk_start_index"
Index of the first chunk (defaults to 0).
.IP \fBheader\fR 4
.IX Item "header"
Filename of the header where the initialization data will be written.
.IP \fBaudio_chunk_duration\fR 4
.IX Item "audio_chunk_duration"
Duration of each audio chunk in milliseconds (defaults to 5000).
.PP
\fIExample\fR
.IX Subsection "Example"
.PP
.Vb 10
\&        ffmpeg \-f v4l2 \-i /dev/video0 \e
\&               \-f alsa \-i hw:0 \e
\&               \-map 0:0 \e
\&               \-c:v libvpx\-vp9 \e
\&               \-s 640x360 \-keyint_min 30 \-g 30 \e
\&               \-f webm_chunk \e
\&               \-header webm_live_video_360.hdr \e
\&               \-chunk_start_index 1 \e
\&               webm_live_video_360_%d.chk \e
\&               \-map 1:0 \e
\&               \-c:a libvorbis \e
\&               \-b:a 128k \e
\&               \-f webm_chunk \e
\&               \-header webm_live_audio_128.hdr \e
\&               \-chunk_start_index 1 \e
\&               \-audio_chunk_duration 1000 \e
\&               webm_live_audio_128_%d.chk
.Ve
.SS webm_dash_manifest
.IX Subsection "webm_dash_manifest"
WebM DASH Manifest muxer.
.PP
This muxer implements the WebM DASH Manifest specification to generate the DASH
manifest XML. It also supports manifest generation for DASH live streams.
.PP
For more information see:
.IP \(bu 4
WebM DASH Specification: <\fBhttps://sites.google.com/a/webmproject.org/wiki/adaptive\-streaming/webm\-dash\-specification\fR>
.IP \(bu 4
ISO DASH Specification: <\fBhttp://standards.iso.org/ittf/PubliclyAvailableStandards/c065274_ISO_IEC_23009\-1_2014.zip\fR>
.PP
\fIOptions\fR
.IX Subsection "Options"
.PP
This muxer supports the following options:
.IP \fBadaptation_sets\fR 4
.IX Item "adaptation_sets"
This option has the following syntax: "id=x,streams=a,b,c id=y,streams=d,e" where x and y are the
unique identifiers of the adaptation sets and a,b,c,d and e are the indices of the corresponding
audio and video streams. Any number of adaptation sets can be added using this option.
.IP \fBlive\fR 4
.IX Item "live"
Set this to 1 to create a live stream DASH Manifest. Default: 0.
.IP \fBchunk_start_index\fR 4
.IX Item "chunk_start_index"
Start index of the first chunk. This will go in the \fBstartNumber\fR attribute
of the \fBSegmentTemplate\fR element in the manifest. Default: 0.
.IP \fBchunk_duration_ms\fR 4
.IX Item "chunk_duration_ms"
Duration of each chunk in milliseconds. This will go in the \fBduration\fR
attribute of the \fBSegmentTemplate\fR element in the manifest. Default: 1000.
.IP \fButc_timing_url\fR 4
.IX Item "utc_timing_url"
URL of the page that will return the UTC timestamp in ISO format. This will go
in the \fBvalue\fR attribute of the \fBUTCTiming\fR element in the manifest.
Default: None.
.IP \fBtime_shift_buffer_depth\fR 4
.IX Item "time_shift_buffer_depth"
Smallest time (in seconds) shifting buffer for which any Representation is
guaranteed to be available. This will go in the \fBtimeShiftBufferDepth\fR
attribute of the \fBMPD\fR element. Default: 60.
.IP \fBminimum_update_period\fR 4
.IX Item "minimum_update_period"
Minimum update period (in seconds) of the manifest. This will go in the
\&\fBminimumUpdatePeriod\fR attribute of the \fBMPD\fR element. Default: 0.
.PP
\fIExample\fR
.IX Subsection "Example"
.PP
.Vb 9
\&        ffmpeg \-f webm_dash_manifest \-i video1.webm \e
\&               \-f webm_dash_manifest \-i video2.webm \e
\&               \-f webm_dash_manifest \-i audio1.webm \e
\&               \-f webm_dash_manifest \-i audio2.webm \e
\&               \-map 0 \-map 1 \-map 2 \-map 3 \e
\&               \-c copy \e
\&               \-f webm_dash_manifest \e
\&               \-adaptation_sets "id=0,streams=0,1 id=1,streams=2,3" \e
\&               manifest.xml
.Ve
.SS whip
.IX Subsection "whip"
WebRTC (Real-Time Communication) muxer that supports sub-second latency streaming according to
the WHIP (WebRTC-HTTP ingestion protocol) specification.
.PP
This is an experimental feature.
.PP
It uses HTTP as a signaling protocol to exchange SDP capabilities and ICE lite candidates. Then,
it uses STUN binding requests and responses to establish a session over UDP. Subsequently, it
initiates a DTLS handshake to exchange the SRTP encryption keys. Lastly, it splits video and
audio frames into RTP packets and encrypts them using SRTP.
.PP
Ensure that you use H.264 without B frames and Opus for the audio codec. For example, to convert
an input file with \fBffmpeg\fR to WebRTC:
.PP
.Vb 3
\&        ffmpeg \-re \-i input.mp4 \-acodec libopus \-ar 48000 \-ac 2 \e
\&          \-vcodec libx264 \-profile:v baseline \-tune zerolatency \-threads 1 \-bf 0 \e
\&          \-f whip "http://localhost:1985/rtc/v1/whip/?app=live&stream=livestream"
.Ve
.PP
For this example, we have employed low latency options, resulting in an end-to-end latency of
approximately 150ms.
.PP
\fIOptions\fR
.IX Subsection "Options"
.PP
This muxer supports the following options:
.IP "\fBhandshake_timeout\fR \fIinteger\fR" 4
.IX Item "handshake_timeout integer"
Set the timeout in milliseconds for ICE and DTLS handshake.
Default value is 5000.
.IP "\fBpkt_size\fR \fIinteger\fR" 4
.IX Item "pkt_size integer"
Set the maximum size, in bytes, of RTP packets that send out.
Default value is 1500.
.IP "\fBauthorization\fR \fIstring\fR" 4
.IX Item "authorization string"
The optional Bearer token for WHIP Authorization.
.IP "\fBcert_file\fR \fIstring\fR" 4
.IX Item "cert_file string"
The optional certificate file path for DTLS.
.IP "\fBkey_file\fR \fIstring\fR" 4
.IX Item "key_file string"
The optional private key file path for DTLS.
.SH METADATA
.IX Header "METADATA"
FFmpeg is able to dump metadata from media files into a simple UTF\-8\-encoded
INI-like text file and then load it back using the metadata muxer/demuxer.
.PP
The file format is as follows:
.IP 1. 4
A file consists of a header and a number of metadata tags divided into sections,
each on its own line.
.IP 2. 4
The header is a \fB;FFMETADATA\fR string, followed by a version number (now 1).
.IP 3. 4
Metadata tags are of the form \fBkey=value\fR
.IP 4. 4
Immediately after header follows global metadata
.IP 5. 4
After global metadata there may be sections with per\-stream/per\-chapter
metadata.
.IP 6. 4
A section starts with the section name in uppercase (i.e. STREAM or CHAPTER) in
brackets (\fB[\fR, \fB]\fR) and ends with next section or end of file.
.IP 7. 4
At the beginning of a chapter section there may be an optional timebase to be
used for start/end values. It must be in form
\&\fBTIMEBASE=\fR\fInum\fR\fB/\fR\fIden\fR, where \fInum\fR and \fIden\fR are
integers. If the timebase is missing then start/end times are assumed to
be in nanoseconds.
.Sp
Next a chapter section must contain chapter start and end times in form
\&\fBSTART=\fR\fInum\fR, \fBEND=\fR\fInum\fR, where \fInum\fR is a positive
integer.
.IP 8. 4
Empty lines and lines starting with \fB;\fR or \fB#\fR are ignored.
.IP 9. 4
Metadata keys or values containing special characters (\fB=\fR, \fB;\fR,
\&\fB#\fR, \fB\e\fR and a newline) must be escaped with a backslash \fB\e\fR.
.IP 10. 4
Note that whitespace in metadata (e.g. \fBfoo = bar\fR) is considered to be
a part of the tag (in the example above key is \fBfoo\fR , value is
 \fBbar\fR).
.PP
A ffmetadata file might look like this:
.PP
.Vb 4
\&        ;FFMETADATA1
\&        title=bike\e\eshed
\&        ;this is a comment
\&        artist=FFmpeg troll team
\&        
\&        [CHAPTER]
\&        TIMEBASE=1/1000
\&        START=0
\&        #chapter ends at 0:01:00
\&        END=60000
\&        title=chapter \e#1
\&        [STREAM]
\&        title=multi\e
\&        line
.Ve
.PP
By using the ffmetadata muxer and demuxer it is possible to extract
metadata from an input file to an ffmetadata file, and then transcode
the file into an output file with the edited ffmetadata file.
.PP
Extracting an ffmetadata file with \fIffmpeg\fR goes as follows:
.PP
.Vb 1
\&        ffmpeg \-i INPUT \-f ffmetadata FFMETADATAFILE
.Ve
.PP
Reinserting edited metadata information from the FFMETADATAFILE file can
be done as:
.PP
.Vb 1
\&        ffmpeg \-i INPUT \-i FFMETADATAFILE \-map_metadata 1 \-codec copy OUTPUT
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBffmpeg\fR\|(1), \fBffplay\fR\|(1), \fBffprobe\fR\|(1), \fBlibavformat\fR\|(3)
.SH AUTHORS
.IX Header "AUTHORS"
The FFmpeg developers.
.PP
For details about the authorship, see the Git history of the project
(https://git.ffmpeg.org/ffmpeg), e.g. by typing the command
\&\fBgit log\fR in the FFmpeg source directory, or browsing the
online repository at <\fBhttps://git.ffmpeg.org/ffmpeg\fR>.
.PP
Maintainers for the specific components are listed in the file
\&\fIMAINTAINERS\fR in the source code tree.
