.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "FFPLAY 1"
.TH FFPLAY 1 " " " " " "
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
ffplay \- FFplay media player
.SH SYNOPSIS
.IX Header "SYNOPSIS"
ffplay [\fIoptions\fR] [\fIinput_url\fR]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
FFplay is a very simple and portable media player using the FFmpeg
libraries and the SDL library. It is mostly used as a testbed for the
various FFmpeg APIs.
.SH OPTIONS
.IX Header "OPTIONS"
All the numerical options, if not specified otherwise, accept a string
representing a number as input, which may be followed by one of the SI
unit prefixes, for example: 'K', 'M', or 'G'.
.PP
If 'i' is appended to the SI unit prefix, the complete prefix will be
interpreted as a unit prefix for binary multiples, which are based on
powers of 1024 instead of powers of 1000. Appending 'B' to the SI unit
prefix multiplies the value by 8. This allows using, for example:
\&'KB', 'MiB', 'G' and 'B' as number suffixes.
.PP
Options which do not take arguments are boolean options, and set the
corresponding value to true. They can be set to false by prefixing
the option name with "no". For example using "\-nofoo"
will set the boolean option with name "foo" to false.
.PP
Options that take arguments support a special syntax where the argument given on
the command line is interpreted as a path to the file from which the actual
argument value is loaded. To use this feature, add a forward slash '/'
immediately before the option name (after the leading dash). E.g.
.PP
.Vb 1
\&        ffmpeg \-i INPUT \-/filter:v filter.script OUTPUT
.Ve
.PP
will load a filtergraph description from the file named \fIfilter.script\fR.
.SS "Stream specifiers"
.IX Subsection "Stream specifiers"
Some options are applied per-stream, e.g. bitrate or codec. Stream specifiers
are used to precisely specify which stream(s) a given option belongs to.
.PP
A stream specifier is a string generally appended to the option name and
separated from it by a colon. E.g. \f(CW\*(C`\-codec:a:1 ac3\*(C'\fR contains the
\&\f(CW\*(C`a:1\*(C'\fR stream specifier, which matches the second audio stream. Therefore, it
would select the ac3 codec for the second audio stream.
.PP
A stream specifier can match several streams, so that the option is applied to all
of them. E.g. the stream specifier in \f(CW\*(C`\-b:a 128k\*(C'\fR matches all audio
streams.
.PP
An empty stream specifier matches all streams. For example, \f(CW\*(C`\-codec copy\*(C'\fR
or \f(CW\*(C`\-codec: copy\*(C'\fR would copy all the streams without reencoding.
.PP
Possible forms of stream specifiers are:
.IP \fIstream_index\fR 4
.IX Item "stream_index"
Matches the stream with this index. E.g. \f(CW\*(C`\-threads:1 4\*(C'\fR would set the
thread count for the second stream to 4. If \fIstream_index\fR is used as an
additional stream specifier (see below), then it selects stream number
\&\fIstream_index\fR from the matching streams. Stream numbering is based on the
order of the streams as detected by libavformat except when a stream group
specifier or program ID is also specified. In this case it is based on the
ordering of the streams in the group or program.
.IP \fIstream_type\fR\fB[:\fR\fIadditional_stream_specifier\fR\fB]\fR 4
.IX Item "stream_type[:additional_stream_specifier]"
\&\fIstream_type\fR is one of following: 'v' or 'V' for video, 'a' for audio, 's'
for subtitle, 'd' for data, and 't' for attachments. 'v' matches all video
streams, 'V' only matches video streams which are not attached pictures, video
thumbnails or cover arts. If \fIadditional_stream_specifier\fR is used, then
it matches streams which both have this type and match the
\&\fIadditional_stream_specifier\fR. Otherwise, it matches all streams of the
specified type.
.IP \fBg:\fR\fIgroup_specifier\fR\fB[:\fR\fIadditional_stream_specifier\fR\fB]\fR 4
.IX Item "g:group_specifier[:additional_stream_specifier]"
Matches streams which are in the group with the specifier \fIgroup_specifier\fR.
if \fIadditional_stream_specifier\fR is used, then it matches streams which both
are part of the group and match the \fIadditional_stream_specifier\fR.
\&\fIgroup_specifier\fR may be one of the following:
.RS 4
.IP \fIgroup_index\fR 4
.IX Item "group_index"
Match the stream with this group index.
.IP "\fB#\fR\fIgroup_id\fR \fBor i:\fR\fIgroup_id\fR" 4
.IX Item "#group_id or i:group_id"
Match the stream with this group id.
.RE
.RS 4
.RE
.IP \fBp:\fR\fIprogram_id\fR\fB[:\fR\fIadditional_stream_specifier\fR\fB]\fR 4
.IX Item "p:program_id[:additional_stream_specifier]"
Matches streams which are in the program with the id \fIprogram_id\fR. If
\&\fIadditional_stream_specifier\fR is used, then it matches streams which both
are part of the program and match the \fIadditional_stream_specifier\fR.
.IP "\fB#\fR\fIstream_id\fR \fBor i:\fR\fIstream_id\fR" 4
.IX Item "#stream_id or i:stream_id"
Match the stream by stream id (e.g. PID in MPEG-TS container).
.IP \fBm:\fR\fIkey\fR\fB[:\fR\fIvalue\fR\fB]\fR 4
.IX Item "m:key[:value]"
Matches streams with the metadata tag \fIkey\fR having the specified value. If
\&\fIvalue\fR is not given, matches streams that contain the given tag with any
value. The colon character ':' in \fIkey\fR or \fIvalue\fR needs to be
backslash-escaped.
.IP \fBdisp:\fR\fIdispositions\fR\fB[:\fR\fIadditional_stream_specifier\fR\fB]\fR 4
.IX Item "disp:dispositions[:additional_stream_specifier]"
Matches streams with the given disposition(s). \fIdispositions\fR is a list of
one or more dispositions (as printed by the \fB\-dispositions\fR option)
joined with '+'.
.IP \fBu\fR 4
.IX Item "u"
Matches streams with usable configuration, the codec must be defined and the
essential information such as video dimension or audio sample rate must be present.
.Sp
Note that in \fBffmpeg\fR, matching by metadata will only work properly for
input files.
.SS "Generic options"
.IX Subsection "Generic options"
These options are shared amongst the ff* tools.
.IP \fB\-L\fR 4
.IX Item "-L"
Show license.
.IP "\fB\-h, \-?, \-help, \-\-help [\fR\fIarg\fR\fB]\fR" 4
.IX Item "-h, -?, -help, --help [arg]"
Show help. An optional parameter may be specified to print help about a specific
item. If no argument is specified, only basic (non advanced) tool
options are shown.
.Sp
Possible values of \fIarg\fR are:
.RS 4
.IP \fBlong\fR 4
.IX Item "long"
Print advanced tool options in addition to the basic tool options.
.IP \fBfull\fR 4
.IX Item "full"
Print complete list of options, including shared and private options
for encoders, decoders, demuxers, muxers, filters, etc.
.IP \fBdecoder=\fR\fIdecoder_name\fR 4
.IX Item "decoder=decoder_name"
Print detailed information about the decoder named \fIdecoder_name\fR. Use the
\&\fB\-decoders\fR option to get a list of all decoders.
.IP \fBencoder=\fR\fIencoder_name\fR 4
.IX Item "encoder=encoder_name"
Print detailed information about the encoder named \fIencoder_name\fR. Use the
\&\fB\-encoders\fR option to get a list of all encoders.
.IP \fBdemuxer=\fR\fIdemuxer_name\fR 4
.IX Item "demuxer=demuxer_name"
Print detailed information about the demuxer named \fIdemuxer_name\fR. Use the
\&\fB\-formats\fR option to get a list of all demuxers and muxers.
.IP \fBmuxer=\fR\fImuxer_name\fR 4
.IX Item "muxer=muxer_name"
Print detailed information about the muxer named \fImuxer_name\fR. Use the
\&\fB\-formats\fR option to get a list of all muxers and demuxers.
.IP \fBfilter=\fR\fIfilter_name\fR 4
.IX Item "filter=filter_name"
Print detailed information about the filter named \fIfilter_name\fR. Use the
\&\fB\-filters\fR option to get a list of all filters.
.IP \fBbsf=\fR\fIbitstream_filter_name\fR 4
.IX Item "bsf=bitstream_filter_name"
Print detailed information about the bitstream filter named \fIbitstream_filter_name\fR.
Use the \fB\-bsfs\fR option to get a list of all bitstream filters.
.IP \fBprotocol=\fR\fIprotocol_name\fR 4
.IX Item "protocol=protocol_name"
Print detailed information about the protocol named \fIprotocol_name\fR.
Use the \fB\-protocols\fR option to get a list of all protocols.
.RE
.RS 4
.RE
.IP \fB\-version\fR 4
.IX Item "-version"
Show version.
.IP \fB\-buildconf\fR 4
.IX Item "-buildconf"
Show the build configuration, one option per line.
.IP \fB\-formats\fR 4
.IX Item "-formats"
Show available formats (including devices).
.IP \fB\-demuxers\fR 4
.IX Item "-demuxers"
Show available demuxers.
.IP \fB\-muxers\fR 4
.IX Item "-muxers"
Show available muxers.
.IP \fB\-devices\fR 4
.IX Item "-devices"
Show available devices.
.IP \fB\-codecs\fR 4
.IX Item "-codecs"
Show all codecs known to libavcodec.
.Sp
Note that the term 'codec' is used throughout this documentation as a shortcut
for what is more correctly called a media bitstream format.
.IP \fB\-decoders\fR 4
.IX Item "-decoders"
Show available decoders.
.IP \fB\-encoders\fR 4
.IX Item "-encoders"
Show all available encoders.
.IP \fB\-bsfs\fR 4
.IX Item "-bsfs"
Show available bitstream filters.
.IP \fB\-protocols\fR 4
.IX Item "-protocols"
Show available protocols.
.IP \fB\-filters\fR 4
.IX Item "-filters"
Show available libavfilter filters.
.IP \fB\-pix_fmts\fR 4
.IX Item "-pix_fmts"
Show available pixel formats.
.IP \fB\-sample_fmts\fR 4
.IX Item "-sample_fmts"
Show available sample formats.
.IP \fB\-layouts\fR 4
.IX Item "-layouts"
Show channel names and standard channel layouts.
.IP \fB\-dispositions\fR 4
.IX Item "-dispositions"
Show stream dispositions.
.IP \fB\-colors\fR 4
.IX Item "-colors"
Show recognized color names.
.IP "\fB\-sources\fR \fIdevice\fR\fB[,\fR\fIopt1\fR\fB=\fR\fIval1\fR\fB[,\fR\fIopt2\fR\fB=\fR\fIval2\fR\fB]...]\fR" 4
.IX Item "-sources device[,opt1=val1[,opt2=val2]...]"
Show autodetected sources of the input device.
Some devices may provide system-dependent source names that cannot be autodetected.
The returned list cannot be assumed to be always complete.
.Sp
.Vb 1
\&        ffmpeg \-sources pulse,server=***********
.Ve
.IP "\fB\-sinks\fR \fIdevice\fR\fB[,\fR\fIopt1\fR\fB=\fR\fIval1\fR\fB[,\fR\fIopt2\fR\fB=\fR\fIval2\fR\fB]...]\fR" 4
.IX Item "-sinks device[,opt1=val1[,opt2=val2]...]"
Show autodetected sinks of the output device.
Some devices may provide system-dependent sink names that cannot be autodetected.
The returned list cannot be assumed to be always complete.
.Sp
.Vb 1
\&        ffmpeg \-sinks pulse,server=***********
.Ve
.IP "\fB\-loglevel [\fR\fIflags\fR\fB+]\fR\fIloglevel\fR \fB| \-v [\fR\fIflags\fR\fB+]\fR\fIloglevel\fR" 4
.IX Item "-loglevel [flags+]loglevel | -v [flags+]loglevel"
Set logging level and flags used by the library.
.Sp
The optional \fIflags\fR prefix can consist of the following values:
.RS 4
.IP \fBrepeat\fR 4
.IX Item "repeat"
Indicates that repeated log output should not be compressed to the first line
and the "Last message repeated n times" line will be omitted.
.IP \fBlevel\fR 4
.IX Item "level"
Indicates that log output should add a \f(CW\*(C`[level]\*(C'\fR prefix to each message
line. This can be used as an alternative to log coloring, e.g. when dumping the
log to file.
.IP \fBtime\fR 4
.IX Item "time"
Indicates that log lines should be prefixed with time information.
.IP \fBdatetime\fR 4
.IX Item "datetime"
Indicates that log lines should be prefixed with date and time information.
.RE
.RS 4
.Sp
Flags can also be used alone by adding a '+'/'\-' prefix to set/reset a single
flag without affecting other \fIflags\fR or changing \fIloglevel\fR. When
setting both \fIflags\fR and \fIloglevel\fR, a '+' separator is expected
between the last \fIflags\fR value and before \fIloglevel\fR.
.Sp
\&\fIloglevel\fR is a string or a number containing one of the following values:
.IP "\fBquiet, \-8\fR" 4
.IX Item "quiet, -8"
Show nothing at all; be silent.
.IP "\fBpanic, 0\fR" 4
.IX Item "panic, 0"
Only show fatal errors which could lead the process to crash, such as
an assertion failure. This is not currently used for anything.
.IP "\fBfatal, 8\fR" 4
.IX Item "fatal, 8"
Only show fatal errors. These are errors after which the process absolutely
cannot continue.
.IP "\fBerror, 16\fR" 4
.IX Item "error, 16"
Show all errors, including ones which can be recovered from.
.IP "\fBwarning, 24\fR" 4
.IX Item "warning, 24"
Show all warnings and errors. Any message related to possibly
incorrect or unexpected events will be shown.
.IP "\fBinfo, 32\fR" 4
.IX Item "info, 32"
Show informative messages during processing. This is in addition to
warnings and errors. This is the default value.
.IP "\fBverbose, 40\fR" 4
.IX Item "verbose, 40"
Same as \f(CW\*(C`info\*(C'\fR, except more verbose.
.IP "\fBdebug, 48\fR" 4
.IX Item "debug, 48"
Show everything, including debugging information.
.IP "\fBtrace, 56\fR" 4
.IX Item "trace, 56"
.RE
.RS 4
.Sp
For example to enable repeated log output, add the \f(CW\*(C`level\*(C'\fR prefix, and set
\&\fIloglevel\fR to \f(CW\*(C`verbose\*(C'\fR:
.Sp
.Vb 1
\&        ffmpeg \-loglevel repeat+level+verbose \-i input output
.Ve
.Sp
Another example that enables repeated log output without affecting current
state of \f(CW\*(C`level\*(C'\fR prefix flag or \fIloglevel\fR:
.Sp
.Vb 1
\&        ffmpeg [...] \-loglevel +repeat
.Ve
.Sp
By default the program logs to stderr. If coloring is supported by the
terminal, colors are used to mark errors and warnings. Log coloring
can be disabled setting the environment variable
\&\fBAV_LOG_FORCE_NOCOLOR\fR, or can be forced setting
the environment variable \fBAV_LOG_FORCE_COLOR\fR.
.RE
.IP \fB\-report\fR 4
.IX Item "-report"
Dump full command line and log output to a file named
\&\f(CW\*(C`\fR\f(CIprogram\fR\f(CW\-\fR\f(CIYYYYMMDD\fR\f(CW\-\fR\f(CIHHMMSS\fR\f(CW.log\*(C'\fR in the current
directory.
This file can be useful for bug reports.
It also implies \f(CW\*(C`\-loglevel debug\*(C'\fR.
.Sp
Setting the environment variable \fBFFREPORT\fR to any value has the
same effect. If the value is a ':'\-separated key=value sequence, these
options will affect the report; option values must be escaped if they
contain special characters or the options delimiter ':' (see the
``Quoting and escaping'' section in the ffmpeg-utils manual).
.Sp
The following options are recognized:
.RS 4
.IP \fBfile\fR 4
.IX Item "file"
set the file name to use for the report; \f(CW%p\fR is expanded to the name
of the program, \f(CW%t\fR is expanded to a timestamp, \f(CW\*(C`%%\*(C'\fR is expanded
to a plain \f(CW\*(C`%\*(C'\fR
.IP \fBlevel\fR 4
.IX Item "level"
set the log verbosity level using a numerical value (see \f(CW\*(C`\-loglevel\*(C'\fR).
.RE
.RS 4
.Sp
For example, to output a report to a file named \fIffreport.log\fR
using a log level of \f(CW32\fR (alias for log level \f(CW\*(C`info\*(C'\fR):
.Sp
.Vb 1
\&        FFREPORT=file=ffreport.log:level=32 ffmpeg \-i input output
.Ve
.Sp
Errors in parsing the environment variable are not fatal, and will not
appear in the report.
.RE
.IP \fB\-hide_banner\fR 4
.IX Item "-hide_banner"
Suppress printing banner.
.Sp
All FFmpeg tools will normally show a copyright notice, build options
and library versions. This option can be used to suppress printing
this information.
.IP "\fB\-cpuflags flags (\fR\fIglobal\fR\fB)\fR" 4
.IX Item "-cpuflags flags (global)"
Allows setting and clearing cpu flags. This option is intended
for testing. Do not use it unless you know what you're doing.
.Sp
.Vb 3
\&        ffmpeg \-cpuflags \-sse+mmx ...
\&        ffmpeg \-cpuflags mmx ...
\&        ffmpeg \-cpuflags 0 ...
.Ve
.Sp
Possible flags for this option are:
.RS 4
.IP \fBx86\fR 4
.IX Item "x86"
.RS 4
.PD 0
.IP \fBmmx\fR 4
.IX Item "mmx"
.IP \fBmmxext\fR 4
.IX Item "mmxext"
.IP \fBsse\fR 4
.IX Item "sse"
.IP \fBsse2\fR 4
.IX Item "sse2"
.IP \fBsse2slow\fR 4
.IX Item "sse2slow"
.IP \fBsse3\fR 4
.IX Item "sse3"
.IP \fBsse3slow\fR 4
.IX Item "sse3slow"
.IP \fBssse3\fR 4
.IX Item "ssse3"
.IP \fBatom\fR 4
.IX Item "atom"
.IP \fBsse4.1\fR 4
.IX Item "sse4.1"
.IP \fBsse4.2\fR 4
.IX Item "sse4.2"
.IP \fBavx\fR 4
.IX Item "avx"
.IP \fBavx2\fR 4
.IX Item "avx2"
.IP \fBxop\fR 4
.IX Item "xop"
.IP \fBfma3\fR 4
.IX Item "fma3"
.IP \fBfma4\fR 4
.IX Item "fma4"
.IP \fB3dnow\fR 4
.IX Item "3dnow"
.IP \fB3dnowext\fR 4
.IX Item "3dnowext"
.IP \fBbmi1\fR 4
.IX Item "bmi1"
.IP \fBbmi2\fR 4
.IX Item "bmi2"
.IP \fBcmov\fR 4
.IX Item "cmov"
.RE
.RS 4
.RE
.IP \fBARM\fR 4
.IX Item "ARM"
.RS 4
.IP \fBarmv5te\fR 4
.IX Item "armv5te"
.IP \fBarmv6\fR 4
.IX Item "armv6"
.IP \fBarmv6t2\fR 4
.IX Item "armv6t2"
.IP \fBvfp\fR 4
.IX Item "vfp"
.IP \fBvfpv3\fR 4
.IX Item "vfpv3"
.IP \fBneon\fR 4
.IX Item "neon"
.IP \fBsetend\fR 4
.IX Item "setend"
.RE
.RS 4
.RE
.IP \fBAArch64\fR 4
.IX Item "AArch64"
.RS 4
.IP \fBarmv8\fR 4
.IX Item "armv8"
.IP \fBvfp\fR 4
.IX Item "vfp"
.IP \fBneon\fR 4
.IX Item "neon"
.RE
.RS 4
.RE
.IP \fBPowerPC\fR 4
.IX Item "PowerPC"
.RS 4
.IP \fBaltivec\fR 4
.IX Item "altivec"
.RE
.RS 4
.RE
.IP "\fBSpecific Processors\fR" 4
.IX Item "Specific Processors"
.RS 4
.IP \fBpentium2\fR 4
.IX Item "pentium2"
.IP \fBpentium3\fR 4
.IX Item "pentium3"
.IP \fBpentium4\fR 4
.IX Item "pentium4"
.IP \fBk6\fR 4
.IX Item "k6"
.IP \fBk62\fR 4
.IX Item "k62"
.IP \fBathlon\fR 4
.IX Item "athlon"
.IP \fBathlonxp\fR 4
.IX Item "athlonxp"
.IP \fBk8\fR 4
.IX Item "k8"
.RE
.RS 4
.RE
.RE
.RS 4
.RE
.IP "\fB\-cpucount\fR \fIcount\fR \fB(\fR\fIglobal\fR\fB)\fR" 4
.IX Item "-cpucount count (global)"
.PD
Override detection of CPU count. This option is intended
for testing. Do not use it unless you know what you're doing.
.Sp
.Vb 1
\&        ffmpeg \-cpucount 2
.Ve
.IP "\fB\-max_alloc\fR \fIbytes\fR" 4
.IX Item "-max_alloc bytes"
Set the maximum size limit for allocating a block on the heap by ffmpeg's
family of malloc functions. Exercise \fBextreme caution\fR when using
this option. Don't use if you do not understand the full consequence of doing so.
Default is INT_MAX.
.SS AVOptions
.IX Subsection "AVOptions"
These options are provided directly by the libavformat, libavdevice and
libavcodec libraries. To see the list of available AVOptions, use the
\&\fB\-help\fR option. They are separated into two categories:
.IP \fBgeneric\fR 4
.IX Item "generic"
These options can be set for any container, codec or device. Generic options
are listed under AVFormatContext options for containers/devices and under
AVCodecContext options for codecs.
.IP \fBprivate\fR 4
.IX Item "private"
These options are specific to the given container, device or codec. Private
options are listed under their corresponding containers/devices/codecs.
.PP
For example to write an ID3v2.3 header instead of a default ID3v2.4 to
an MP3 file, use the \fBid3v2_version\fR private option of the MP3
muxer:
.PP
.Vb 1
\&        ffmpeg \-i input.flac \-id3v2_version 3 out.mp3
.Ve
.PP
All codec AVOptions are per-stream, and thus a stream specifier
should be attached to them:
.PP
.Vb 1
\&        ffmpeg \-i multichannel.mxf \-map 0:v:0 \-map 0:a:0 \-map 0:a:0 \-c:a:0 ac3 \-b:a:0 640k \-ac:a:1 2 \-c:a:1 aac \-b:2 128k out.mp4
.Ve
.PP
In the above example, a multichannel audio stream is mapped twice for output.
The first instance is encoded with codec ac3 and bitrate 640k.
The second instance is downmixed to 2 channels and encoded with codec aac. A bitrate of 128k is specified for it using
absolute index of the output stream.
.PP
Note: the \fB\-nooption\fR syntax cannot be used for boolean
AVOptions, use \fB\-option 0\fR/\fB\-option 1\fR.
.PP
Note: the old undocumented way of specifying per-stream AVOptions by
prepending v/a/s to the options name is now obsolete and will be
removed soon.
.SS "Main options"
.IX Subsection "Main options"
.IP "\fB\-x\fR \fIwidth\fR" 4
.IX Item "-x width"
Force displayed width.
.IP "\fB\-y\fR \fIheight\fR" 4
.IX Item "-y height"
Force displayed height.
.IP \fB\-fs\fR 4
.IX Item "-fs"
Start in fullscreen mode.
.IP \fB\-an\fR 4
.IX Item "-an"
Disable audio.
.IP \fB\-vn\fR 4
.IX Item "-vn"
Disable video.
.IP \fB\-sn\fR 4
.IX Item "-sn"
Disable subtitles.
.IP "\fB\-ss\fR \fIpos\fR" 4
.IX Item "-ss pos"
Seek to \fIpos\fR. Note that in most formats it is not possible to seek
exactly, so \fBffplay\fR will seek to the nearest seek point to
\&\fIpos\fR.
.Sp
\&\fIpos\fR must be a time duration specification,
see \fBthe Time duration section in the ffmpeg\-utils\|(1) manual\fR.
.IP "\fB\-t\fR \fIduration\fR" 4
.IX Item "-t duration"
Play \fIduration\fR seconds of audio/video.
.Sp
\&\fIduration\fR must be a time duration specification,
see \fBthe Time duration section in the ffmpeg\-utils\|(1) manual\fR.
.IP \fB\-bytes\fR 4
.IX Item "-bytes"
Seek by bytes.
.IP \fB\-seek_interval\fR 4
.IX Item "-seek_interval"
Set custom interval, in seconds, for seeking using left/right keys. Default is 10 seconds.
.IP \fB\-nodisp\fR 4
.IX Item "-nodisp"
Disable graphical display.
.IP \fB\-noborder\fR 4
.IX Item "-noborder"
Borderless window.
.IP \fB\-alwaysontop\fR 4
.IX Item "-alwaysontop"
Window always on top. Available on: X11 with SDL >= 2.0.5, Windows SDL >= 2.0.6.
.IP \fB\-volume\fR 4
.IX Item "-volume"
Set the startup volume. 0 means silence, 100 means no volume reduction or
amplification. Negative values are treated as 0, values above 100 are treated
as 100.
.IP "\fB\-f\fR \fIfmt\fR" 4
.IX Item "-f fmt"
Force format.
.IP "\fB\-window_title\fR \fItitle\fR" 4
.IX Item "-window_title title"
Set window title (default is the input filename).
.IP "\fB\-left\fR \fItitle\fR" 4
.IX Item "-left title"
Set the x position for the left of the window (default is a centered window).
.IP "\fB\-top\fR \fItitle\fR" 4
.IX Item "-top title"
Set the y position for the top of the window (default is a centered window).
.IP "\fB\-loop\fR \fInumber\fR" 4
.IX Item "-loop number"
Loops movie playback <number> times. 0 means forever.
.IP "\fB\-showmode\fR \fImode\fR" 4
.IX Item "-showmode mode"
Set the show mode to use.
Available values for \fImode\fR are:
.RS 4
.IP "\fB0, video\fR" 4
.IX Item "0, video"
show video
.IP "\fB1, waves\fR" 4
.IX Item "1, waves"
show audio waves
.IP "\fB2, rdft\fR" 4
.IX Item "2, rdft"
show audio frequency band using RDFT ((Inverse) Real Discrete Fourier Transform)
.RE
.RS 4
.Sp
Default value is "video", if video is not present or cannot be played
"rdft" is automatically selected.
.Sp
You can interactively cycle through the available show modes by
pressing the key \fBw\fR.
.RE
.IP "\fB\-vf\fR \fIfiltergraph\fR" 4
.IX Item "-vf filtergraph"
Create the filtergraph specified by \fIfiltergraph\fR and use it to
filter the video stream.
.Sp
\&\fIfiltergraph\fR is a description of the filtergraph to apply to
the stream, and must have a single video input and a single video
output. In the filtergraph, the input is associated to the label
\&\f(CW\*(C`in\*(C'\fR, and the output to the label \f(CW\*(C`out\*(C'\fR. See the
ffmpeg-filters manual for more information about the filtergraph
syntax.
.Sp
You can specify this parameter multiple times and cycle through the specified
filtergraphs along with the show modes by pressing the key \fBw\fR.
.IP "\fB\-af\fR \fIfiltergraph\fR" 4
.IX Item "-af filtergraph"
\&\fIfiltergraph\fR is a description of the filtergraph to apply to
the input audio.
Use the option "\-filters" to show all the available filters (including
sources and sinks).
.IP "\fB\-i\fR \fIinput_url\fR" 4
.IX Item "-i input_url"
Read \fIinput_url\fR.
.SS "Advanced options"
.IX Subsection "Advanced options"
.IP \fB\-stats\fR 4
.IX Item "-stats"
Print several playback statistics, in particular show the stream
duration, the codec parameters, the current position in the stream and
the audio/video synchronisation drift. It is shown by default, unless the
log level is lower than \f(CW\*(C`info\*(C'\fR. Its display can be forced by manually
specifying this option. To disable it, you need to specify \f(CW\*(C`\-nostats\*(C'\fR.
.IP \fB\-fast\fR 4
.IX Item "-fast"
Non-spec-compliant optimizations.
.IP \fB\-genpts\fR 4
.IX Item "-genpts"
Generate pts.
.IP "\fB\-sync\fR \fItype\fR" 4
.IX Item "-sync type"
Set the master clock to audio (\f(CW\*(C`type=audio\*(C'\fR), video
(\f(CW\*(C`type=video\*(C'\fR) or external (\f(CW\*(C`type=ext\*(C'\fR). Default is audio. The
master clock is used to control audio-video synchronization. Most media
players use audio as master clock, but in some cases (streaming or high
quality broadcast) it is necessary to change that. This option is mainly
used for debugging purposes.
.IP "\fB\-ast\fR \fIaudio_stream_specifier\fR" 4
.IX Item "-ast audio_stream_specifier"
Select the desired audio stream using the given stream specifier. The stream
specifiers are described in the \fBStream specifiers\fR chapter. If this option
is not specified, the "best" audio stream is selected in the program of the
already selected video stream.
.IP "\fB\-vst\fR \fIvideo_stream_specifier\fR" 4
.IX Item "-vst video_stream_specifier"
Select the desired video stream using the given stream specifier. The stream
specifiers are described in the \fBStream specifiers\fR chapter. If this option
is not specified, the "best" video stream is selected.
.IP "\fB\-sst\fR \fIsubtitle_stream_specifier\fR" 4
.IX Item "-sst subtitle_stream_specifier"
Select the desired subtitle stream using the given stream specifier. The stream
specifiers are described in the \fBStream specifiers\fR chapter. If this option
is not specified, the "best" subtitle stream is selected in the program of the
already selected video or audio stream.
.IP \fB\-autoexit\fR 4
.IX Item "-autoexit"
Exit when video is done playing.
.IP \fB\-exitonkeydown\fR 4
.IX Item "-exitonkeydown"
Exit if any key is pressed.
.IP \fB\-exitonmousedown\fR 4
.IX Item "-exitonmousedown"
Exit if any mouse button is pressed.
.IP "\fB\-codec:\fR\fImedia_specifier\fR\fB \fR\fIcodec_name\fR" 4
.IX Item "-codec:media_specifier codec_name"
Force a specific decoder implementation for the stream identified by
\&\fImedia_specifier\fR, which can assume the values \f(CW\*(C`a\*(C'\fR (audio),
\&\f(CW\*(C`v\*(C'\fR (video), and \f(CW\*(C`s\*(C'\fR subtitle.
.IP "\fB\-acodec\fR \fIcodec_name\fR" 4
.IX Item "-acodec codec_name"
Force a specific audio decoder.
.IP "\fB\-vcodec\fR \fIcodec_name\fR" 4
.IX Item "-vcodec codec_name"
Force a specific video decoder.
.IP "\fB\-scodec\fR \fIcodec_name\fR" 4
.IX Item "-scodec codec_name"
Force a specific subtitle decoder.
.IP \fB\-autorotate\fR 4
.IX Item "-autorotate"
Automatically rotate the video according to file metadata. Enabled by
default, use \fB\-noautorotate\fR to disable it.
.IP \fB\-framedrop\fR 4
.IX Item "-framedrop"
Drop video frames if video is out of sync. Enabled by default if the master
clock is not set to video. Use this option to enable frame dropping for all
master clock sources, use \fB\-noframedrop\fR to disable it.
.IP \fB\-infbuf\fR 4
.IX Item "-infbuf"
Do not limit the input buffer size, read as much data as possible from the
input as soon as possible. Enabled by default for realtime streams, where data
may be dropped if not read in time. Use this option to enable infinite buffers
for all inputs, use \fB\-noinfbuf\fR to disable it.
.IP "\fB\-filter_threads\fR \fInb_threads\fR" 4
.IX Item "-filter_threads nb_threads"
Defines how many threads are used to process a filter pipeline. Each pipeline
will produce a thread pool with this many threads available for parallel
processing. The default is 0 which means that the thread count will be
determined by the number of available CPUs.
.IP \fB\-enable_vulkan\fR 4
.IX Item "-enable_vulkan"
Use vulkan renderer rather than SDL builtin renderer. Depends on libplacebo.
.IP \fB\-vulkan_params\fR 4
.IX Item "-vulkan_params"
Vulkan configuration using a list of \fIkey\fR=\fIvalue\fR pairs separated by
":".
.IP \fB\-hwaccel\fR 4
.IX Item "-hwaccel"
Use HW accelerated decoding. Enable this option will enable vulkan renderer
automatically.
.SS "While playing"
.IX Subsection "While playing"
.IP "\fBq, ESC\fR" 4
.IX Item "q, ESC"
Quit.
.IP \fBf\fR 4
.IX Item "f"
Toggle full screen.
.IP "\fBp, SPC\fR" 4
.IX Item "p, SPC"
Pause.
.IP \fBm\fR 4
.IX Item "m"
Toggle mute.
.IP "\fB9, 0\fR" 4
.IX Item "9, 0"
.PD 0
.IP "\fB/, *\fR" 4
.IX Item "/, *"
.PD
Decrease and increase volume respectively.
.IP \fBa\fR 4
.IX Item "a"
Cycle audio channel in the current program.
.IP \fBv\fR 4
.IX Item "v"
Cycle video channel.
.IP \fBt\fR 4
.IX Item "t"
Cycle subtitle channel in the current program.
.IP \fBc\fR 4
.IX Item "c"
Cycle program.
.IP \fBw\fR 4
.IX Item "w"
Cycle video filters or show modes.
.IP \fBs\fR 4
.IX Item "s"
Step to the next frame.
.Sp
Pause if the stream is not already paused, step to the next video
frame, and pause.
.IP \fBleft/right\fR 4
.IX Item "left/right"
Seek backward/forward 10 seconds.
.IP \fBdown/up\fR 4
.IX Item "down/up"
Seek backward/forward 1 minute.
.IP "\fBpage down/page up\fR" 4
.IX Item "page down/page up"
Seek to the previous/next chapter.
or if there are no chapters
Seek backward/forward 10 minutes.
.IP "\fBright mouse click\fR" 4
.IX Item "right mouse click"
Seek to percentage in file corresponding to fraction of width.
.IP "\fBleft mouse double-click\fR" 4
.IX Item "left mouse double-click"
Toggle full screen.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBffplay\-all\fR\|(1),
\&\fBffmpeg\fR\|(1), \fBffprobe\fR\|(1),
\&\fBffmpeg\-utils\fR\|(1), \fBffmpeg\-scaler\fR\|(1), \fBffmpeg\-resampler\fR\|(1),
\&\fBffmpeg\-codecs\fR\|(1), \fBffmpeg\-bitstream\-filters\fR\|(1), \fBffmpeg\-formats\fR\|(1),
\&\fBffmpeg\-devices\fR\|(1), \fBffmpeg\-protocols\fR\|(1), \fBffmpeg\-filters\fR\|(1)
.SH AUTHORS
.IX Header "AUTHORS"
The FFmpeg developers.
.PP
For details about the authorship, see the Git history of the project
(https://git.ffmpeg.org/ffmpeg), e.g. by typing the command
\&\fBgit log\fR in the FFmpeg source directory, or browsing the
online repository at <\fBhttps://git.ffmpeg.org/ffmpeg\fR>.
.PP
Maintainers for the specific components are listed in the file
\&\fIMAINTAINERS\fR in the source code tree.
