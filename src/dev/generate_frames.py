import pandas as pd
import os


def check_dir(dir):
    if not os.path.exists(dir):
        os.system(f'mkdir -p {dir}')
    return dir


video_home = check_dir('/raid/ryan/datasets/gait/videos/video_formated_trim')
save_dir = check_dir('/raid/ryan/datasets/gait/frames')

dataset = pd.read_pickle(
    '/raid/ryan/datasets/gait/person_detection_and_tracking_results.pkl')

video_names = list(set(dataset.vids[~dataset.vids.isna()]))

for video_name in video_names:
    save_subdir = check_dir(os.path.join(
    save_dir, video_name))
    vpath = os.path.join(video_home, video_name+'.avi')
    os.system(
        f"./ffmpeg -i {vpath} {save_subdir}/thumb%04d.jpg -hide_banner")
