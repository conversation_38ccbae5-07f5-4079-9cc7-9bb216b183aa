import warnings
warnings.filterwarnings("ignore")

import numpy as np
np.int = int

import os
import sys
import sklearn

# Add current directory to Python path for module imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import datasets.gaitregression
from opts import parse_opts
from utils.generate_model import init_state, load_trained_ckpt
from utils.transforms import (
    Compose, ToTensor, Normalize)
from utils.train_utils import Trainer
from utils.testing_utils import Tester
from utils.target_columns import get_target_columns
import utils.visualization as viz
from utils.mean import get_mean, get_std
import torchvision.transforms as TF
import torch
import torch.distributed as dist
import torch.multiprocessing as mp


torch.backends.cudnn.benchmark = True   # 입력 크기 고정일 때


def setup_ddp(rank, world_size, backend='nccl', init_method='env://'):
    """Initialize the distributed environment."""
    os.environ['MASTER_ADDR'] = os.environ.get('MASTER_ADDR', 'localhost')
    os.environ['MASTER_PORT'] = os.environ.get('MASTER_PORT', '12355')
    
    # Initialize the process group
    dist.init_process_group(backend, rank=rank, world_size=world_size, init_method=init_method)
    
    # Set the GPU device
    torch.cuda.set_device(rank)


def cleanup_ddp():
    """Clean up the distributed environment."""
    if dist.is_initialized():
        dist.destroy_process_group()


def main_worker(rank, world_size, opt):
    """Main worker function for DDP training."""
    
    # Setup DDP
    setup_ddp(rank, world_size, opt.dist_backend, opt.dist_url)

    # Set local rank
    opt.local_rank = rank
    opt.use_ddp = True

    # Memory optimization settings
    torch.backends.cudnn.benchmark = True
    torch.backends.cudnn.deterministic = False

    # Clear GPU cache
    torch.cuda.empty_cache()
    
    try:
        # attention indicator
        opt.bop_str = 'BOP' if opt.bop_refinement else 'NonBOP'
        opt.group_str = f"G{opt.n_groups}" if opt.n_groups > 0 else ''
        opt.arch = "{}-{}".format(opt.backbone, opt.model_depth)
        opt.start_epoch = 0

        target_columns = get_target_columns(opt)

        # define regression model
        net, criterion1, criterion2, optimizer, scheduler = init_state(opt)

        if not opt.training_from_zero:
            net = load_trained_ckpt(opt, net)

        opt.mean = get_mean(opt.norm_value, dataset=opt.mean_dataset)
        opt.std = get_std(opt.norm_value, dataset=opt.mean_dataset)

        if opt.no_mean_norm and not opt.std_norm:
            norm_method = Normalize([0, 0, 0], [1, 1, 1])
        else:
            norm_method = Normalize(opt.mean, opt.std)

        # Use transforms compatible with the Trainer class (expects dict structure)
        # Handle sample_size as tuple or int
        if isinstance(opt.sample_size, tuple):
            sample_size = opt.sample_size[0] if len(opt.sample_size) > 0 else 112
        else:
            sample_size = opt.sample_size

        spatial_transform = {
            'train': Compose([
                TF.RandomRotation(degrees=(0, 0)),  # First transform must be rotation
                TF.RandomResizedCrop(size=sample_size, scale=(0.8, 1.0)),  # Second must be crop
                ToTensor(opt.norm_value),
                norm_method,
            ]),
            'test': Compose([
                TF.RandomRotation(degrees=(0, 0)),  # Consistent structure for test
                TF.CenterCrop(sample_size),
                ToTensor(opt.norm_value),
                norm_method,
            ])
        }

        temporal_transform = {
            'train': None,
            'test': None
        }

        target_transform = sklearn.preprocessing.StandardScaler()

        # Create log directory and set logpath for all processes
        model_indicator = f"{opt.model_arch}_{opt.arch}_{opt.bop_str}_{opt.group_str}"
        logpath = os.path.join(opt.log_dir, model_indicator)
        opt.logpath = logpath  # Set for all processes

        if rank == 0:
            # Only rank 0 creates the directory and plotter
            if not os.path.exists(logpath):
                os.makedirs(logpath)
            plotter = viz.VisdomPlotter(env_name=model_indicator)
        else:
            plotter = None

        if opt.dataset == "Gaitparams_PD":
            # prepare dataset  (train/test split)
            data = datasets.gaitregression.prepare_dataset(
                input_file=opt.input_file,
                target_file=opt.target_file,
                target_columns=target_columns,
                chunk_parts=opt.chunk_parts,
                target_transform=target_transform,
                legacy_mode=opt.legacy_mode,
                clinical_test_file=opt.clinical_test_file,
            )

            ds_class = datasets.gaitregression.GAITDataset

            train_ds = ds_class(
                X=data["train_X"], y=data["train_y"], opt=opt, phase='train',
            )

            test_ds = ds_class(
                X=data["test_X"], y=data["test_y"], opt=opt, phase='test',
            )

            # Use the original dataloader generator from gaitregression
            dataloader_generator = (
                datasets.gaitregression.generate_dataloader_for_crossvalidation
            )

        else:
            raise NotImplementedError("Does not support other datasets until now..")

        if opt.mode == "train":
            trainer = Trainer(
                model=net,
                criterion1=criterion1, criterion2=criterion2,
                optimizer=optimizer,
                scheduler=scheduler,
                opt=opt,
                spatial_transform=spatial_transform,
                temporal_transform=temporal_transform,
                target_transform=target_transform,
            )

            trainer.fit(
                ds=train_ds, dataloader_generator=dataloader_generator,
                ds_class=ds_class,
                plotter=plotter)

        elif opt.mode == "test":
            # Create score function for testing
            from sklearn.metrics import r2_score
            def score_func(y_true, y_pred, multioutput='uniform_average'):
                return r2_score(y_true, y_pred, multioutput=multioutput)

            tester = Tester(
                model=net,
                opt=opt,
                score_func=score_func,
                spatial_transform=spatial_transform,
                temporal_transform=temporal_transform,
                target_transform=target_transform,
            )

            tester.test(
                ds=test_ds, dataloader_generator=dataloader_generator,
                ds_class=ds_class,
                target_columns=target_columns)

    finally:
        # Clean up DDP
        cleanup_ddp()


def main():
    """Main function to launch DDP training."""
    opt = parse_opts()
    
    if opt.multi_gpu and opt.use_ddp:
        # DDP training
        world_size = len(os.environ.get("CUDA_VISIBLE_DEVICES", "0").split(","))
        opt.world_size = world_size
        
        # Use torch.multiprocessing to spawn processes
        mp.spawn(main_worker, args=(world_size, opt), nprocs=world_size, join=True)
    else:
        # Single GPU or DataParallel training (fallback to original main.py behavior)
        import multiprocessing
        multiprocessing.set_start_method('spawn', True)
        
        # Run single process version
        main_worker(0, 1, opt)


if __name__ == "__main__":
    main()
