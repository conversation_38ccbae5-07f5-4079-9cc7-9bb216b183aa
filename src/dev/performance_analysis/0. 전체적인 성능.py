#%%
import pandas as pd

bop_result = pd.read_pickle('../logs.bak/BOP_AGNet_r2plus1d_18-18/test/full_testing_results.pkl')
baseline_result = pd.read_pickle('../logs.bak/NonBOP_AGNet_r2plus1d_18-18/test/full_testing_results.pkl')

#%%

# %%
bop_y_true = bop_result['y_true']
bop_y_pred = bop_result['y_pred']

baseline_y_true = baseline_result['y_true']
baseline_y_pred = baseline_result['y_pred']
# %%
bop_mape = (bop_y_true-bop_y_pred).abs()/bop_y_true * 100
bop_mape.mean(0)
# %%
baseline_mape = (baseline_y_true-baseline_y_pred).abs()/baseline_y_true * 100
baseline_mape.mean(0)

# %%
res = pd.concat([bop_mape.mean(0), baseline_mape.mean(0)], axis=1)
res.columns=['baseline', 'smagnet']
res

#%% 

from sklearn.metrics import r2_score

r2_score(bop_y_true, bop_y_pred, multioutput='raw_values')

# %%
import matplotlib.pyplot as plt

plt.scatter(bop_y_true['Velocity'], bop_y_pred['Velocity'])
plt.show()
# %%
