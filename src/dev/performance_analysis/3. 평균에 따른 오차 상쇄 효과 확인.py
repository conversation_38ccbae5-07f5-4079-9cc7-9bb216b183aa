#%%
import pandas as pd

prediction_results = pd.read_pickle('../logs/NonBOP_AGNet_r2plus1d_18-18/test/full_testing_results.pkl')
#%%
test_vids = [ line.strip() for line in open('./test_vids.txt').readlines() ]
# %%
patient_ids = pd.Series(test_vids).str.split('_').str[0]
test_vids = pd.Series(test_vids)
# %%
from collections import defaultdict
y_true = defaultdict(list)
y_pred = defaultdict(list)

y_true_raw = defaultdict(list)
y_pred_raw = defaultdict(list)

n_trials = []
target_trials = 5

patient_list = []

for pid in patient_ids.unique():
    pid_idxs = test_vids[test_vids.str.startswith(pid)].index.tolist()
    pid_results = prediction_results.iloc[pid_idxs]

    if len(pid_idxs) >= 5:
        n_trials.append(len(pid_results))
        sample_indices = pid_results.index.to_series().sample(target_trials, random_state=0) # to reproduce results
        pid_y_true = pid_results['y_true'].loc[sample_indices].mean(0)
        pid_y_pred = pid_results['y_pred'].loc[sample_indices].mean(0)
        pid_y_true_raw = pid_results['y_true'].loc[sample_indices]
        pid_y_pred_raw = pid_results['y_pred'].loc[sample_indices]

        for key in pid_y_true.index[:-6]:
            y_true[key].append(pid_y_true[key])
            y_pred[key].append(pid_y_pred[key])
            y_true_raw[key] += pid_y_true_raw[key].tolist()
            y_pred_raw[key] += pid_y_pred_raw[key].tolist()

        patient_list.append(pid)

#%%
from matplotlib import pyplot as plt
pd.Series(n_trials).plot.hist(bins=30)
plt.xlabel('n_trials'); plt.title('distribution of n_trials')
plt.xticks(range(1, max(n_trials)+1))
plt.show()

# %%
y_true_avg = pd.DataFrame(y_true)
y_pred_avg = pd.DataFrame(y_pred)
# %%
y_true_avg.head()
# %%
mape = ((y_true_avg-y_pred_avg)/y_true_avg * 100).abs().mean()
avg_mape = mape.mean()
print(f'여러번의 보행 trial (n={target_trials})에 대한 평균값을 사용하였을때, 환자별 MAPE 성능')
print(mape)
print(f'MAPE (avg) : {avg_mape}')
# %%
y_true_raw = pd.DataFrame(y_true_raw)
y_pred_raw = pd.DataFrame(y_pred_raw)
# %%
y_true_raw.head()
# %%
mape = ((y_true_raw-y_pred_raw)/y_true_raw * 100).abs().mean()
avg_mape = mape.mean()
print('평균값 사용하지 않고, 각 보행 trial에 대한 MAPE 성능')
print(mape)
print(f'MAPE (avg) : {avg_mape}')
# %%
