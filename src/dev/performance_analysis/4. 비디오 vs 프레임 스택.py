import requests
import pickle
import json
import numpy as np
from moviepy.editor import VideoFileClip

# Client code for Flask API server by running "demo2/server.py"

# 서버 URL
url = 'http://localhost:5002/api'

def read_video_file(filename):
    clip = VideoFileClip(filename)
    frames = [frame for frame in clip.iter_frames()]
    return np.array(frames)


# Test case 1: raw_frames
def test_raw_frames():
    # 샘플 NumPy 배열 생성 (가상의 프레임 데이터)
    video_data = read_video_file('./1013388_test_1_trial_0.avi')
    data = pickle.dumps(video_data)

    # 요청 파라미터 설정
    params = {
        'data_type': 'raw_frames'
    }

    # 요청 보내기
    response = requests.post(url, data=data, headers={'Content-Type': 'application/octet-stream'}, params={'params': json.dumps(params)})

    # 응답 확인
    if response.status_code == 200:
        output = pickle.loads(response.content)
        print("## Test raw_frames output:", output)
    else:
        print("Test raw_frames failed with status code:", response.status_code)

# Test case 2: video
def test_video():
    # 샘플 비디오 파일 경로 설정 (실제 경로로 변경 필요)
    video_file_path = './1013388_test_1_trial_0.avi'
    
    # 요청 파라미터 설정
    params = {
        'data_type': 'video'
    }

    # 파일 업로드 요청 보내기
    files = {
        'video': open(video_file_path, 'rb'),
    }

    # 요청 보내기
    response = requests.post(url, files=files, params={'params': json.dumps(params)})

    # 응답 확인
    if response.status_code == 200:
        output = pickle.loads(response.content)
        print("## Test video output:", output)
    else:
        print("Test video failed with status code:", response.status_code)

# 테스트 실행
test_raw_frames()
print()
test_video()
