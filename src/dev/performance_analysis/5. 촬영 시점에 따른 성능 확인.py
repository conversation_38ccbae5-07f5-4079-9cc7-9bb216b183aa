#%%
# 보행 촬영 장소 변경 시점 확인
import pandas as pd

df_new = pd.read_excel('/data/hossay/GAITRite-dataset/v2/gaitrite_full_dataset.xlsx')
df_new.head()
# %%
import cv2
import matplotlib.pyplot as plt
df_new['Time'] = pd.to_datetime(df_new['Time'])
df_info = df_new[['VideoFile', 'Time']].sort_values('Time')

for year in range(2017, 2024):
    vfile = df_info.query(f"'{year}' < Time < '{year+1}'").iloc[-1]['VideoFile'].replace('/data/', '/data/hossay/')
    cap  = cv2.VideoCapture(vfile)
    _, frame = cap.read()
    frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
    plt.imshow(frame)
    plt.title(f'Year = {year}')
    plt.show()
# %%
for month in range(1, 13):
    try:
        vfile = df_info.query(f"'2021-{month}' < Time < '2021-{month+1}'").iloc[-1]['VideoFile'].replace('/data/', '/data/hossay/')
    except:
        continue
    cap  = cv2.VideoCapture(vfile)
    _, frame = cap.read()
    frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
    plt.imshow(frame)
    plt.title(f'Year = 2021, Month = {month}')
    plt.show()
# %%
for day in range(1, 32):
    try:
        vfile = df_info.query(f"'2021-10-{day}' < Time < '2021-10-{day+1}'").iloc[-1]['VideoFile'].replace('/data/', '/data/hossay/')
    except:
        continue
    cap  = cv2.VideoCapture(vfile)
    for i in range(50):
        _, frame = cap.read()
    frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
    plt.imshow(frame)
    plt.title(f'Year = 2021, Month = 10, Day = {day}')
    plt.show()
# %%
prediction_results = pd.read_pickle('../logs/NonBOP_AGNet_r2plus1d_18-18/test/full_testing_results.pkl')
prediction_results.shape
# %%
test_vids = [ line.strip() for line in open('./test_vids.txt').readlines() ]
test_vids
# %%
vfiles_before = df_info.query(f"'2021-10-15' > Time")['VideoFile'].str.replace('/data/GAITRite-dataset/v2/video_formated_trim/', '').str.replace('.avi', '').tolist()
vfiles_after = df_info.query(f"'2021-10-15' <= Time")['VideoFile'].str.replace('/data/GAITRite-dataset/v2/video_formated_trim/', '').str.replace('.avi', '').tolist()

test_vids_before = list(set(vfiles_before).intersection(set(test_vids)))
test_vids_after = list(set(vfiles_after).intersection(set(test_vids)))
print(f'Before : {len(test_vids_before)}, After : {len(test_vids_after)}')
# %%
test_idxs_before = [ test_vids.index(vid) for vid in test_vids_before ]
test_idxs_after = [ test_vids.index(vid) for vid in test_vids_after ]

# %%
prediction_results_before = prediction_results.loc[test_idxs_before]
prediction_results_after = prediction_results.loc[test_idxs_after]
# %%
def compute_mape(results):
    y_true = results['y_true']
    y_pred = results['y_pred']

    mape = ((y_true-y_pred).abs()/y_true * 100).mean(0)
    print(mape)
    print(f'Avg MAPE : {mape.mean()}')

#%%
print('[MAPE] Before : ')
compute_mape(prediction_results_before)
# %%
print('[MAPE] After : ')
compute_mape(prediction_results_after)
# %%
