#%%
import matplotlib.pyplot as plt
plt.ion()

#%%
import pandas as pd
import pandas_access
from pathlib import Path

mdb_dir = '/data/hossay/GAITRite-dataset/v2/mdb/'

contact_intervals_concat = []

for mdb_file in Path(mdb_dir).glob('*'):
    contact_intervals_concat.append(pandas_access.read_table(mdb_file, "Test", usecols=["Gait_Id", "Pt_id", "FirstContact",
                                                                        "LastContact", "Loc_heel_X"]))  # 보행 시작/끝 타이밍 정보

contact_intervals_concat = pd.concat(contact_intervals_concat, axis=0).drop_duplicates()
contact_intervals_concat.reset_index(inplace=True, drop=True)
contact_intervals_concat.tail()

#%%
df_full = pd.read_excel('/data/hossay/GAITRite-dataset/v2/gaitrite_full_dataset.xlsx')
df_full.head()

#%%
df_full.set_index('index', inplace=True)
df_full.head()


#%%
test_vids = [ line.strip() for line in open('./test_vids.txt').readlines() ]
test_indices = [ '_'.join([i.split('_')[0], i.split('_')[2], i.split('_')[4]]) for i in test_vids ]
test_indices[:4]

#%%
prediction_results = pd.read_pickle('../logs/NonBOP_AGNet_r2plus1d_18-18/test/full_testing_results.pkl')
prediction_results.head()

#%%
def decide_view(gait_id):
    x_traces = contact_intervals_concat.query(f"Gait_Id == {gait_id}")['Loc_heel_X'].astype(float).values
    view = 'front' if x_traces[-1] - x_traces[0] > 0 else 'back'
    return view

gait_info_for_testing = df_full.loc[test_indices, ['Gait_Id']]
gait_info_for_testing['View'] = gait_info_for_testing['Gait_Id'].map(decide_view)
gait_info_for_testing.head()
# %%
view = 'back'
gait_info_for_testing_filtered = gait_info_for_testing.query(f"View == '{view}'")
#%%
patient_ids = gait_info_for_testing_filtered.index.str.split('_').str[0]
test_vids = pd.Series(test_vids)
# %%
from collections import defaultdict
y_true = defaultdict(list)
y_pred = defaultdict(list)

for pid in patient_ids.unique():
    pid_idxs = test_vids[test_vids.str.startswith(pid)].index.tolist()
    pid_results = prediction_results.iloc[pid_idxs]

    pid_y_true = pid_results['y_true']
    pid_y_pred = pid_results['y_pred']

    for key in pid_y_true.columns:
        y_true[key] += pid_y_true[key].tolist()
        y_pred[key] += pid_y_pred[key].tolist()
#%%
y_true = pd.DataFrame(y_true)
y_pred = pd.DataFrame(y_pred)

mape = ((y_true-y_pred)/y_true * 100).abs().mean()
avg_mape = mape.mean()
print(f'View : {view}에 대한 MAPE 성능')
print(mape)
print(f'MAPE (avg) : {avg_mape}')
# %%
