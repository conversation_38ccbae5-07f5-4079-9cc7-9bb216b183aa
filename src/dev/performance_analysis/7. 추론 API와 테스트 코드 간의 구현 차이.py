#%%
import numpy as np
import requests
import pickle
import json
from PIL import Image
import pandas as pd


# Client code for Flask API server by running "demo2/server.py"

# 서버 URL
url = 'http://127.0.0.1:5002/api'
frame_root = '/data/hossay/GAITRite-dataset/v2/frame'
video_root = '/data/hossay/GAITRite-dataset/v2/video_formated_trim'
input_file = '/data/hossay/GAITRite-dataset/v2/person_detection_and_tracking_results.csv'


cur_X = pd.read_csv(input_file)



def read_video_frames(frame_root, vid, frame_indices):
    res = []
    subdir = os.path.join(frame_root, vid)
    for i in frame_indices:
        f = os.path.join(subdir, f"thumb{int(i):04d}.jpg")
        img = np.array(Image.open(f))
        res.append(img)

    return np.array(res)
#%%
# Test case 1: raw_frames
def test_raw_frames(vid):
    frame_indices = cur_X.loc[cur_X.vids == vid, 'idx'].values
    # 샘플 NumPy 배열 생성 (가상의 프레임 데이터)
    video_data = read_video_frames(frame_root, vid=vid, frame_indices=frame_indices)

    print('test_raw_frames : ', video_data.shape)
    print(video_data[0, 0, :, 0])
    data = pickle.dumps(video_data)

    # 요청 파라미터 설정
    params = {
        'data_type': 'raw_frames'
    }

    

    # 요청 보내기
    response = requests.post(url, data=data, headers={'Content-Type': 'application/octet-stream'}, params={'params': json.dumps(params)})

    # 응답 확인
    if response.status_code == 200:
        output = pickle.loads(response.content)

    return output


# Test case 2: video
def test_video(video_file_path):
    # 요청 파라미터 설정
    params = {
        'data_type': 'video'
    }

    # 파일 업로드 요청 보내기
    files = {
        'video': open(video_file_path, 'rb'),
    }

    # 요청 보내기
    response = requests.post(url, files=files, params={'params': json.dumps(params)})

    # 응답 확인
    if response.status_code == 200:
        output = pickle.loads(response.content)

    return output


#%%
out1 = test_raw_frames(vid = '1972710_test_3_trial_12')
out2 = test_video(video_file_path=os.path.join(video_root, '1972710_test_3_trial_12.avi'))

print('## raw frames ##')
print(pd.Series(out1))

print('## video file ##')
print(pd.Series(out2))

#%%
import os
test_video_files = [ os.path.join(video_root, line.strip() + '.avi') for line in open('./test_vids.txt').readlines() ]
test_video_files[:3]
#%%

from collections import defaultdict
import pandas as pd
from tqdm import tqdm

api_call_results = defaultdict(list)

for video_file in tqdm(test_video_files):
    output = test_video(video_file)
    for key in output.keys():
        api_call_results[key].append(output[key])

#%%
api_call_results = pd.DataFrame(api_call_results)
api_call_results.head(10)
#%%

prediction_results = pd.read_pickle('../logs/NonBOP_AGNet_r2plus1d_18-18/test/full_testing_results.pkl')
prediction_results['y_pred'].head(10)

#%%
def report_mape(y_true, y_pred):
    return ((y_true-y_pred)/y_true * 100).abs().mean()

print('## Original test code performance')
org_mape = report_mape(prediction_results['y_true'].iloc[:, :-6], prediction_results['y_pred'].iloc[:, :-6])
print(org_mape)
print('Avg : ', org_mape.mean())

print('## API call performance')
org_mape = report_mape(prediction_results['y_true'].iloc[:, :-6], api_call_results.iloc[:, :-6])
print(org_mape)
print('Avg : ', org_mape.mean())

# %%
import pandas as pd

input_file = '/data/hossay/GAITRite-dataset/v2/person_detection_and_tracking_results.csv'
cur_X = pd.read_csv(input_file)
#%%
cur_X.head()
# %%
cur_X['idx'].head().diff()
# %%
cur_X.head(150)['idx'].diff()
# %%
def check_continuous(x):
    return (x['idx'].diff()==1.0).iloc[1:].all()

is_continue = cur_X.groupby('vids').apply(check_continuous)
is_continue
# %%
(is_continue == True).all()
# %%
