# pre-training gait variables with average type
CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7 python main.py --input_file /raid/ryan/datasets/gait/person_detection_and_tracking_results.pkl --target_file /raid/ryan/datasets/gait/gaitrite_full_dataset.xlsx --backbone r2plus1d_18 --model_depth 18 --pretrained_path "" --data_root /raid/ryan/datasets/gait/frames/ --multi_gpu --device_ids 0,1,2,3,4,5,6,7 --batch_size 32 --learning_rate 1e-4 --n_threads 8 --mode train --model_arch R2Plus1D-pretrain --n_iter 201 --CV 5 --training_from_zero --clinical_test_file /raid/ryan/datasets/gait/clinical_test/test_data_20230515_cleansing.xlsx
