# test model
CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7 python main.py --input_file /raid/ryan/datasets/gait/person_detection_and_tracking_results.pkl --target_file /raid/ryan/datasets/gait/gaitrite_full_dataset.xlsx --backbone r2plus1d_18 --model_depth 18 --pretrained_path "" --data_root /raid/ryan/datasets/gait/frames/ --multi_gpu --device_ids 0,1,2,3,4,5,6,7 --test_epoch 60 --pretrain_epoch 100 --batch_size 32 --n_threads 8 --mode test --model_arch R2Plus1D --training_from_zero
