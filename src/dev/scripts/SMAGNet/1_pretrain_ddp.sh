#!/bin/bash

# DDP (DistributedDataParallel) pre-training script for gait variables with optimized video loading
# This script uses the new optimized video loading pipeline and DDP for better performance

# Configuration
export CUDA_VISIBLE_DEVICES=0,1,2,3
export MASTER_ADDR=localhost
export MASTER_PORT=12355

# Data paths - using optimized video loading
DATA_ROOT="/raid/ryan/datasets/gait/videos/video_formated_trim"  # Original video files
INPUT_FILE="/raid/ryan/datasets/gait/person_detection_and_tracking_results.pkl"
TARGET_FILE="/raid/ryan/datasets/gait/gaitrite_full_dataset.xlsx"
CLINICAL_TEST_FILE="/raid/ryan/datasets/gait/clinical_test/test_data_20230515_cleansing.xlsx"

# Model configuration
BACKBONE="r2plus1d_18"
MODEL_DEPTH=18
MODEL_ARCH="AGNet-pretrain"
BATCH_SIZE=64  # Total batch size across all GPUs
LEARNING_RATE=1e-4
N_THREADS=32
N_ITER=201
CV=5

# Training arguments
COMMON_ARGS="
    --input_file $INPUT_FILE
    --target_file $TARGET_FILE
    --backbone $BACKBONE
    --model_depth $MODEL_DEPTH
    --data_root $DATA_ROOT
    --multi_gpu
    --use_ddp
    --with_segmentation
    --batch_size $BATCH_SIZE
    --learning_rate $LEARNING_RATE
    --n_threads $N_THREADS
    --mode train
    --model_arch $MODEL_ARCH
    --n_iter $N_ITER
    --CV $CV
    --bop_refinement
    --training_from_zero
    --clinical_test_file $CLINICAL_TEST_FILE
"

echo "🚀 Starting Enhanced DDP training with optimized video loading..."
echo "Data root: $DATA_ROOT"
echo "Using $(echo $CUDA_VISIBLE_DEVICES | tr ',' '\n' | wc -l) GPUs"
echo "Total batch size: $BATCH_SIZE"
echo "Batch size per GPU: $((BATCH_SIZE / $(echo $CUDA_VISIBLE_DEVICES | tr ',' '\n' | wc -l)))"

# Launch DDP training using the new main_ddp.py script
python main_ddp.py $COMMON_ARGS

echo "DDP training completed!"
