#!/bin/bash

# Fine-tuning gait variables with Coefficient of Variation (CV) type
# Enhanced with DDP support while maintaining backward compatibility

# Configuration
export CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7

# Data paths
INPUT_FILE="/raid/ryan/datasets/gait/person_detection_and_tracking_results.pkl"
TARGET_FILE="/raid/ryan/datasets/gait/gaitrite_full_dataset.xlsx"
DATA_ROOT="/raid/ryan/datasets/gait/frames"
CLINICAL_TEST_FILE="/raid/ryan/datasets/gait/clinical_test/test_data_20230515_cleansing.xlsx"

# Model configuration
BACKBONE="r2plus1d_18"
MODEL_DEPTH=18
MODEL_ARCH="AGNet"
BATCH_SIZE=256
LEARNING_RATE=1e-4
N_THREADS=32  # Increased for better performance
N_ITER=51
CV=5
PRETRAIN_EPOCH=100

# Choose between enhanced DDP or legacy mode
USE_DDP=true  # Set to false for legacy DataParallel mode

if [ "$USE_DDP" = true ]; then
    echo "🏋️ Starting Enhanced DDP Fine-tuning..."
    echo "📊 Using DDP mode with enhanced logging"

    # Enhanced DDP fine-tuning
    python main_ddp.py \
        --input_file $INPUT_FILE \
        --target_file $TARGET_FILE \
        --backbone $BACKBONE \
        --model_depth $MODEL_DEPTH \
        --pretrained_path "" \
        --data_root $DATA_ROOT \
        --multi_gpu \
        --use_ddp \
        --with_segmentation \
        --pretrain_epoch $PRETRAIN_EPOCH \
        --batch_size $BATCH_SIZE \
        --learning_rate $LEARNING_RATE \
        --n_threads $N_THREADS \
        --mode train \
        --model_arch $MODEL_ARCH \
        --n_iter $N_ITER \
        --CV $CV \
        --bop_refinement \
        --training_from_zero \
        --clinical_test_file $CLINICAL_TEST_FILE
else
    echo "🏋️ Starting Legacy DataParallel Fine-tuning..."
    echo "📊 Using legacy mode for backward compatibility"

    # Legacy fine-tuning (original command with improvements)
    python main.py \
        --input_file $INPUT_FILE \
        --target_file $TARGET_FILE \
        --backbone $BACKBONE \
        --model_depth $MODEL_DEPTH \
        --pretrained_path "" \
        --data_root $DATA_ROOT \
        --multi_gpu \
        --with_segmentation \
        --device_ids 0,1,2,3,4,5,6,7 \
        --pretrain_epoch $PRETRAIN_EPOCH \
        --batch_size $BATCH_SIZE \
        --learning_rate $LEARNING_RATE \
        --n_threads $N_THREADS \
        --mode train \
        --model_arch $MODEL_ARCH \
        --n_iter $N_ITER \
        --CV $CV \
        --bop_refinement \
        --training_from_zero \
        --clinical_test_file $CLINICAL_TEST_FILE
fi

echo "✅ Fine-tuning completed!"
