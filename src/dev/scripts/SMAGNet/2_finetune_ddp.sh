#!/bin/bash

# Enhanced DDP Fine-tuning Script for SMAGNet
# Fine-tuning gait variables with Coefficient of Variation (CV) type using DDP

# Configuration
export CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7
export MASTER_ADDR=localhost
export MASTER_PORT=12357

# Data paths - supports both optimized video loading and frame-based fallback
DATA_ROOT_VIDEO="/raid/ryan/datasets/gait/videos/video_formated_trim"  # Optimized video loading
DATA_ROOT_FRAMES="/raid/ryan/datasets/gait/frames"                     # Frame-based fallback
INPUT_FILE="/raid/ryan/datasets/gait/person_detection_and_tracking_results.pkl"
TARGET_FILE="/raid/ryan/datasets/gait/gaitrite_full_dataset.xlsx"
CLINICAL_TEST_FILE="/raid/ryan/datasets/gait/clinical_test/test_data_20230515_cleansing.xlsx"

# Model configuration
BACKBONE="r2plus1d_18"
MODEL_DEPTH=18
MODEL_ARCH="AGNet"
BATCH_SIZE=256
LEARNING_RATE=1e-4
N_THREADS=32
N_ITER=51
CV=5
PRETRAIN_EPOCH=100

# Choose data loading mode (video or frames)
USE_VIDEO_LOADING=true  # Set to false for frame-based loading

if [ "$USE_VIDEO_LOADING" = true ]; then
    DATA_ROOT=$DATA_ROOT_VIDEO
    LOADING_MODE="optimized video loading"
else
    DATA_ROOT=$DATA_ROOT_FRAMES
    LOADING_MODE="frame-based loading"
fi

echo "🏋️ Starting Enhanced DDP Fine-tuning..."
echo "📊 Configuration:"
echo "   - Model: $MODEL_ARCH ($BACKBONE-$MODEL_DEPTH)"
echo "   - Data root: $DATA_ROOT"
echo "   - Loading mode: $LOADING_MODE"
echo "   - Using $(echo $CUDA_VISIBLE_DEVICES | tr ',' '\n' | wc -l) GPUs"
echo "   - Total batch size: $BATCH_SIZE"
echo "   - Batch size per GPU: $((BATCH_SIZE / $(echo $CUDA_VISIBLE_DEVICES | tr ',' '\n' | wc -l)))"
echo "   - Learning rate: $LEARNING_RATE"
echo "   - Iterations: $N_ITER"
echo "   - Cross-validation folds: $CV"

# Enhanced DDP fine-tuning arguments
COMMON_ARGS="
    --input_file $INPUT_FILE 
    --target_file $TARGET_FILE 
    --backbone $BACKBONE 
    --model_depth $MODEL_DEPTH 
    --pretrained_path \"\" 
    --data_root $DATA_ROOT 
    --multi_gpu 
    --use_ddp 
    --with_segmentation 
    --pretrain_epoch $PRETRAIN_EPOCH 
    --batch_size $BATCH_SIZE 
    --learning_rate $LEARNING_RATE 
    --n_threads $N_THREADS 
    --mode train 
    --model_arch $MODEL_ARCH 
    --n_iter $N_ITER 
    --CV $CV 
    --bop_refinement 
    --training_from_zero 
    --clinical_test_file $CLINICAL_TEST_FILE
"

echo "🚀 Launching enhanced DDP fine-tuning..."

# Launch DDP fine-tuning using the enhanced main_ddp.py script
python main_ddp.py $COMMON_ARGS

echo "✅ Enhanced DDP fine-tuning completed!"
