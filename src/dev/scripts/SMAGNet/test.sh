#!/bin/bash

# Enhanced DDP Testing Script for SMAGNet
# This script uses the new enhanced DDP testing utilities with optimized video loading

# Configuration - Reduced GPU count to prevent OOM
export CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7
export MASTER_ADDR=localhost
export MASTER_PORT=12356

# Data paths - supports both optimized video loading and frame-based fallback
DATA_ROOT_VIDEO="/raid/ryan/datasets/gait/videos/video_formated_trim"  # Optimized video loading
DATA_ROOT_FRAMES="/raid/ryan/datasets/gait/frames"                     # Frame-based fallback
INPUT_FILE="/raid/ryan/datasets/gait/person_detection_and_tracking_results.pkl"
TARGET_FILE="/raid/ryan/datasets/gait/gaitrite_full_dataset.xlsx"
CLINICAL_TEST_FILE="/raid/ryan/datasets/gait/clinical_test/test_data_20230515_cleansing.xlsx"

# Model configuration - Optimized for memory efficiency
BACKBONE="r2plus1d_18"
MODEL_DEPTH=18
MODEL_ARCH="AGNet-pretrain"
BATCH_SIZE=32  # Reduced from 128 to prevent OOM
N_THREADS=8   # Reduced from 32 to prevent too many processes
TEST_EPOCH=0
PRETRAIN_EPOCH=0

# Choose data loading mode (video or frames)
USE_VIDEO_LOADING=true  # Set to false for frame-based loading

if [ "$USE_VIDEO_LOADING" = true ]; then
    DATA_ROOT=$DATA_ROOT_VIDEO
    LOADING_MODE="optimized video loading"
else
    DATA_ROOT=$DATA_ROOT_FRAMES
    LOADING_MODE="frame-based loading"
fi

echo "🧪 Starting Enhanced DDP Testing..."
echo "📊 Configuration:"
echo "   - Model: $MODEL_ARCH ($BACKBONE-$MODEL_DEPTH)"
echo "   - Data root: $DATA_ROOT"
echo "   - Loading mode: $LOADING_MODE"
echo "   - Using $(echo $CUDA_VISIBLE_DEVICES | tr ',' '\n' | wc -l) GPUs"
echo "   - Batch size: $BATCH_SIZE"
echo "   - Test epoch: $TEST_EPOCH"

# Enhanced DDP testing arguments
COMMON_ARGS="
    --input_file $INPUT_FILE
    --target_file $TARGET_FILE
    --backbone $BACKBONE
    --model_depth $MODEL_DEPTH
    --data_root $DATA_ROOT
    --multi_gpu
    --use_ddp
    --with_segmentation
    --test_epoch $TEST_EPOCH
    --pretrain_epoch $PRETRAIN_EPOCH
    --batch_size $BATCH_SIZE
    --n_threads $N_THREADS
    --mode test
    --model_arch $MODEL_ARCH
    --bop_refinement
    --clinical_test_file $CLINICAL_TEST_FILE
"

echo "🚀 Launching enhanced DDP testing..."

# Set Python path and launch DDP testing using the enhanced main_ddp.py script
export PYTHONPATH="${PYTHONPATH}:$(pwd)"
python main_ddp.py $COMMON_ARGS

echo "✅ Enhanced DDP testing completed!"