#!/bin/bash

# Legacy Testing Script for SMAGNet (Backward Compatibility)
# This script maintains the original testing approach while offering DDP enhancements

# Configuration
export CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7

# Data paths
INPUT_FILE="/raid/ryan/datasets/gait/person_detection_and_tracking_results.pkl"
TARGET_FILE="/raid/ryan/datasets/gait/gaitrite_full_dataset.xlsx"
DATA_ROOT="/raid/ryan/datasets/gait/frames"

# Model configuration
BACKBONE="r2plus1d_18"
MODEL_DEPTH=18
MODEL_ARCH="AGNet-pretrain"
BATCH_SIZE=128
N_THREADS=32  # Increased for better performance
TEST_EPOCH=80
PRETRAIN_EPOCH=80

# Choose between enhanced DDP or legacy mode
USE_DDP=false  # Set to true for enhanced DDP testing

if [ "$USE_DDP" = true ]; then
    echo "🧪 Starting Enhanced DDP Testing..."
    echo "📊 Using DDP mode with enhanced logging and progress tracking"
    
    # Enhanced DDP testing
    python main_ddp.py \
        --input_file $INPUT_FILE \
        --target_file $TARGET_FILE \
        --backbone $BACKBONE \
        --model_depth $MODEL_DEPTH \
        --pretrained_path "" \
        --data_root $DATA_ROOT \
        --multi_gpu \
        --use_ddp \
        --with_segmentation \
        --test_epoch $TEST_EPOCH \
        --pretrain_epoch $PRETRAIN_EPOCH \
        --batch_size $BATCH_SIZE \
        --n_threads $N_THREADS \
        --mode test \
        --model_arch $MODEL_ARCH \
        --bop_refinement
else
    echo "🧪 Starting Legacy DataParallel Testing..."
    echo "📊 Using legacy mode for backward compatibility"
    
    # Legacy testing (original command with improvements)
    python main.py \
        --input_file $INPUT_FILE \
        --target_file $TARGET_FILE \
        --backbone $BACKBONE \
        --model_depth $MODEL_DEPTH \
        --pretrained_path "" \
        --data_root $DATA_ROOT \
        --multi_gpu \
        --with_segmentation \
        --device_ids 0,1,2,3,4,5,6,7 \
        --test_epoch $TEST_EPOCH \
        --pretrain_epoch $PRETRAIN_EPOCH \
        --batch_size $BATCH_SIZE \
        --n_threads $N_THREADS \
        --mode test \
        --model_arch $MODEL_ARCH \
        --bop_refinement
fi

echo "✅ Testing completed!"
