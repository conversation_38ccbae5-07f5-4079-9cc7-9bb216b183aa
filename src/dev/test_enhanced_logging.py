#!/usr/bin/env python3
"""
Test script to verify enhanced DDP training progress logging functionality.
"""

import os
import sys
import time
import torch
import numpy as np

def test_progress_logger_class():
    """Test the DDPProgressLogger class functionality."""
    print("🔄 Testing DDPProgressLogger Class...")
    
    try:
        from utils.train_utils import DDPProgressLogger
        
        # Test initialization
        logger = DDPProgressLogger(
            total_epochs=10,
            total_iterations_per_epoch=100,
            use_ddp=False
        )
        
        print("✅ DDPProgressLogger initialized successfully")
        print(f"   - Total epochs: {logger.total_epochs}")
        print(f"   - Iterations per epoch: {logger.total_iterations_per_epoch}")
        print(f"   - DDP mode: {logger.use_ddp}")
        print(f"   - Is main process: {logger.is_main_process}")
        
        return True
        
    except Exception as e:
        print(f"❌ DDPProgressLogger test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_progress_logging_methods():
    """Test progress logging methods."""
    print("\n🔄 Testing Progress Logging Methods...")
    
    try:
        from utils.train_utils import DDPProgressLogger
        
        logger = DDPProgressLogger(
            total_epochs=5,
            total_iterations_per_epoch=50,
            use_ddp=False
        )
        
        # Test epoch start
        print("\n--- Testing Epoch Start ---")
        logger.start_epoch(0)
        
        # Test training progress
        print("\n--- Testing Training Progress ---")
        losses_dict = {
            'Total Loss': 0.5234,
            'Reg Loss': 0.4123,
            'Seg Loss': 0.1111
        }
        
        logger.log_training_progress(
            epoch=0,
            iteration=10,
            total_iterations=50,
            losses_dict=losses_dict,
            learning_rate=1e-4,
            batch_size=32
        )
        
        # Test validation start
        print("\n--- Testing Validation Start ---")
        logger.log_validation_start(0, 0)
        
        # Test validation results
        print("\n--- Testing Validation Results ---")
        metrics_dict = {
            'Reg Loss': 0.3456,
            'Seg Loss': 0.0987,
            'Score': 0.8765
        }
        
        logger.log_validation_results(
            epoch=0,
            split=0,
            metrics_dict=metrics_dict,
            is_ddp_reduced=True
        )
        
        # Test epoch summary
        print("\n--- Testing Epoch Summary ---")
        train_metrics = {'Loss': 0.4567, 'Score': 0.7890}
        val_metrics = {'Loss': 0.3456, 'Score': 0.8765}
        
        logger.log_epoch_summary(0, train_metrics, val_metrics)
        
        print("\n✅ All progress logging methods working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Progress logging methods test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_ddp_rank_detection():
    """Test DDP rank detection functionality."""
    print("\n🔄 Testing DDP Rank Detection...")
    
    try:
        from utils.train_utils import DDPProgressLogger
        
        # Test without DDP
        logger_no_ddp = DDPProgressLogger(
            total_epochs=5,
            total_iterations_per_epoch=50,
            use_ddp=False
        )
        
        print(f"✅ Non-DDP mode:")
        print(f"   - Rank: {logger_no_ddp.rank}")
        print(f"   - Is main process: {logger_no_ddp.is_main_process}")
        
        # Test with DDP (simulated)
        logger_ddp = DDPProgressLogger(
            total_epochs=5,
            total_iterations_per_epoch=50,
            use_ddp=True
        )
        
        print(f"✅ DDP mode (simulated):")
        print(f"   - Rank: {logger_ddp.rank}")
        print(f"   - Is main process: {logger_ddp.is_main_process}")
        
        return True
        
    except Exception as e:
        print(f"❌ DDP rank detection test failed: {e}")
        return False


def test_training_function_integration():
    """Test integration with training functions."""
    print("\n🔄 Testing Training Function Integration...")
    
    try:
        from utils.train_utils import train_epoch, validate
        import inspect
        
        # Check if train_epoch has progress_logger parameter
        train_sig = inspect.signature(train_epoch)
        if 'progress_logger' in train_sig.parameters:
            print("✅ train_epoch has progress_logger parameter")
        else:
            print("❌ train_epoch missing progress_logger parameter")
            return False
        
        # Check if validate has progress_logger parameter
        val_sig = inspect.signature(validate)
        if 'progress_logger' in val_sig.parameters:
            print("✅ validate has progress_logger parameter")
        else:
            print("❌ validate missing progress_logger parameter")
            return False
        
        # Check if functions use DDPProgressLogger
        train_source = inspect.getsource(train_epoch)
        val_source = inspect.getsource(validate)
        
        if 'DDPProgressLogger' in train_source:
            print("✅ train_epoch uses DDPProgressLogger")
        else:
            print("❌ train_epoch doesn't use DDPProgressLogger")
            return False
        
        if 'progress_logger.log_validation_start' in val_source:
            print("✅ validate uses progress logging methods")
        else:
            print("❌ validate doesn't use progress logging methods")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Training function integration test failed: {e}")
        return False


def test_time_estimation():
    """Test time estimation functionality."""
    print("\n🔄 Testing Time Estimation...")
    
    try:
        from utils.train_utils import DDPProgressLogger
        import time
        
        logger = DDPProgressLogger(
            total_epochs=3,
            total_iterations_per_epoch=10,
            use_ddp=False
        )
        
        # Simulate training progress
        logger.start_epoch(0)
        
        # Simulate some iterations with timing
        for i in range(5):
            time.sleep(0.01)  # Simulate work
            losses_dict = {'Loss': 0.5 - i*0.05}
            logger.log_training_progress(
                epoch=0,
                iteration=i,
                total_iterations=10,
                losses_dict=losses_dict,
                learning_rate=1e-4,
                batch_size=32
            )
        
        print("✅ Time estimation functionality working")
        return True
        
    except Exception as e:
        print(f"❌ Time estimation test failed: {e}")
        return False


def main():
    """Run all enhanced logging tests."""
    print("🚀 Enhanced DDP Logging Tests")
    print("=" * 70)
    
    results = []
    
    # Test progress logger class
    results.append(("DDPProgressLogger Class", test_progress_logger_class()))
    
    # Test progress logging methods
    results.append(("Progress Logging Methods", test_progress_logging_methods()))
    
    # Test DDP rank detection
    results.append(("DDP Rank Detection", test_ddp_rank_detection()))
    
    # Test training function integration
    results.append(("Training Function Integration", test_training_function_integration()))
    
    # Test time estimation
    results.append(("Time Estimation", test_time_estimation()))
    
    # Print summary
    print("\n" + "=" * 70)
    print("Enhanced Logging Test Results")
    print("=" * 70)
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ PASSED" if passed else "❌ FAILED"
        print(f"{test_name:35} : {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 70)
    if all_passed:
        print("🎉 ENHANCED LOGGING READY!")
        print("\n📊 Key Features:")
        print("   ✅ Clean DDP progress logging (rank 0 only)")
        print("   ✅ Time estimation and ETA calculations")
        print("   ✅ Progress bars and completion percentages")
        print("   ✅ Training speed metrics (it/s, samples/s)")
        print("   ✅ Enhanced validation result formatting")
        print("   ✅ Epoch summaries with timing information")
        
        print("\n🚀 Ready to Test:")
        print("   cd src/dev && timeout 180s bash scripts/SMAGNet/1_pretrain_ddp_frames.sh")
        print("   Look for enhanced progress logs with ETA and progress bars!")
        
    else:
        print("⚠️  Some enhanced logging features need attention.")
        print("   Check the failed tests above for details.")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
