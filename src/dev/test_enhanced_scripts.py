#!/usr/bin/env python3
"""
Test script to validate all enhanced DDP scripts in the SMAGNet directory.
"""

import os
import sys
import subprocess
import time

def test_script_structure(script_path, script_name):
    """Test if a script has proper structure and DDP support."""
    print(f"🔄 Testing {script_name}...")
    
    if not os.path.exists(script_path):
        print(f"❌ {script_name}: File not found")
        return False
    
    try:
        with open(script_path, 'r') as f:
            content = f.read()
        
        # Check for essential components
        checks = {
            'Shebang': content.startswith('#!/bin/bash'),
            'CUDA_VISIBLE_DEVICES': 'CUDA_VISIBLE_DEVICES' in content,
            'Configuration section': '# Configuration' in content,
            'Data paths': 'DATA_ROOT' in content or 'data_root' in content,
            'Model configuration': '# Model configuration' in content,
            'Enhanced features': 'Enhanced' in content or 'DDP' in content,
        }
        
        # DDP-specific checks for enhanced scripts
        if 'ddp' in script_name.lower() or 'test.sh' in script_name:
            checks.update({
                'DDP support': '--use_ddp' in content,
                'main_ddp.py': 'main_ddp.py' in content,
                'Progress logging': 'Enhanced' in content,
                'Master address': 'MASTER_ADDR' in content,
                'Master port': 'MASTER_PORT' in content,
            })
        
        # Check if script is executable
        checks['Executable'] = os.access(script_path, os.X_OK)
        
        # Print results
        all_passed = True
        for check, passed in checks.items():
            status = "✅" if passed else "❌"
            print(f"   {status} {check}")
            if not passed:
                all_passed = False
        
        if all_passed:
            print(f"✅ {script_name}: All checks passed")
        else:
            print(f"⚠️  {script_name}: Some checks failed")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ {script_name}: Error reading file - {e}")
        return False


def test_script_syntax(script_path, script_name):
    """Test if a script has valid bash syntax."""
    print(f"🔄 Testing {script_name} syntax...")
    
    try:
        # Use bash -n to check syntax without executing
        result = subprocess.run(['bash', '-n', script_path], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print(f"✅ {script_name}: Syntax valid")
            return True
        else:
            print(f"❌ {script_name}: Syntax error")
            print(f"   Error: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"❌ {script_name}: Syntax check timeout")
        return False
    except Exception as e:
        print(f"❌ {script_name}: Syntax check failed - {e}")
        return False


def test_script_dry_run(script_path, script_name):
    """Test script dry run (check if it starts without errors)."""
    print(f"🔄 Testing {script_name} dry run...")
    
    # Skip dry run for certain scripts to avoid long execution
    if any(x in script_name.lower() for x in ['pretrain', 'finetune']):
        print(f"⚠️  {script_name}: Skipping dry run (training script)")
        return True
    
    try:
        # For test scripts, we can do a quick dry run
        if 'test' in script_name.lower():
            # Modify environment to prevent actual execution
            env = os.environ.copy()
            env['CUDA_VISIBLE_DEVICES'] = '0'  # Use single GPU for testing
            
            # Run with timeout to prevent hanging
            result = subprocess.run(['timeout', '30s', 'bash', script_path], 
                                  capture_output=True, text=True, 
                                  env=env, timeout=35)
            
            # Check if script started properly (exit code 124 = timeout, which is expected)
            if result.returncode in [0, 124]:  # 0 = success, 124 = timeout
                print(f"✅ {script_name}: Dry run successful")
                return True
            else:
                print(f"❌ {script_name}: Dry run failed")
                print(f"   Exit code: {result.returncode}")
                if result.stderr:
                    print(f"   Error: {result.stderr[:200]}...")
                return False
        else:
            print(f"⚠️  {script_name}: Skipping dry run (not a test script)")
            return True
            
    except Exception as e:
        print(f"❌ {script_name}: Dry run error - {e}")
        return False


def main():
    """Test all enhanced scripts in the SMAGNet directory."""
    print("🚀 Enhanced DDP Scripts Validation")
    print("=" * 70)
    
    # Define scripts to test
    scripts_dir = "scripts/SMAGNet"
    scripts = [
        ("1_pretrain.sh", "Enhanced Pre-training (Backward Compatible)"),
        ("1_pretrain_ddp.sh", "DDP Pre-training (Video Loading)"),
        ("1_pretrain_ddp_frames.sh", "DDP Pre-training (Frame Loading)"),
        ("2_finetune.sh", "Enhanced Fine-tuning (Backward Compatible)"),
        ("2_finetune_ddp.sh", "DDP Fine-tuning"),
        ("test.sh", "Enhanced DDP Testing"),
        ("test_legacy.sh", "Legacy Testing (Backward Compatible)"),
    ]
    
    results = []
    
    for script_file, script_desc in scripts:
        script_path = os.path.join(scripts_dir, script_file)
        
        print(f"\n📋 Testing: {script_desc}")
        print(f"📁 Path: {script_path}")
        
        # Test script structure
        structure_ok = test_script_structure(script_path, script_file)
        
        # Test script syntax
        syntax_ok = test_script_syntax(script_path, script_file)
        
        # Test script dry run (limited)
        dry_run_ok = test_script_dry_run(script_path, script_file)
        
        # Overall result
        overall_ok = structure_ok and syntax_ok and dry_run_ok
        results.append((script_desc, overall_ok))
        
        if overall_ok:
            print(f"🎉 {script_desc}: ALL TESTS PASSED")
        else:
            print(f"⚠️  {script_desc}: Some tests failed")
    
    # Print summary
    print("\n" + "=" * 70)
    print("Enhanced Scripts Test Summary")
    print("=" * 70)
    
    all_passed = True
    for script_desc, passed in results:
        status = "✅ PASSED" if passed else "❌ FAILED"
        print(f"{script_desc:45} : {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 70)
    if all_passed:
        print("🎉 ALL ENHANCED SCRIPTS READY!")
        print("\n📊 Available Scripts:")
        print("   🚀 Training Scripts:")
        print("      - 1_pretrain.sh (Enhanced with DDP option)")
        print("      - 1_pretrain_ddp.sh (Pure DDP with video loading)")
        print("      - 1_pretrain_ddp_frames.sh (Pure DDP with frame loading)")
        print("      - 2_finetune.sh (Enhanced with DDP option)")
        print("      - 2_finetune_ddp.sh (Pure DDP fine-tuning)")
        
        print("\n   🧪 Testing Scripts:")
        print("      - test.sh (Enhanced DDP testing)")
        print("      - test_legacy.sh (Backward compatible testing)")
        
        print("\n🔧 Key Features:")
        print("   ✅ DDP support with enhanced logging")
        print("   ✅ Optimized video loading compatibility")
        print("   ✅ Backward compatibility with legacy workflows")
        print("   ✅ Progress tracking and ETA calculations")
        print("   ✅ Configurable data loading modes")
        
        print("\n🚀 Usage Examples:")
        print("   # Enhanced DDP training:")
        print("   cd src/dev && bash scripts/SMAGNet/1_pretrain_ddp.sh")
        print("   # Enhanced DDP testing:")
        print("   cd src/dev && bash scripts/SMAGNet/test.sh")
        print("   # Legacy mode (backward compatibility):")
        print("   cd src/dev && bash scripts/SMAGNet/1_pretrain.sh  # Set USE_DDP=false")
        
    else:
        print("⚠️  Some enhanced scripts need attention.")
        print("   Check the failed tests above for details.")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
