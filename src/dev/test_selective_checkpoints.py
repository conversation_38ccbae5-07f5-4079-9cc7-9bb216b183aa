#!/usr/bin/env python3
"""
Test script to verify selective checkpoint saving functionality.
"""

import os
import sys
import tempfile
import torch
import torch.nn as nn
import numpy as np
import json
import shutil

def test_checkpoint_manager():
    """Test the ModelCheckpointManager functionality."""
    print("🔄 Testing ModelCheckpointManager...")
    
    try:
        from utils.train_utils import ModelCheckpointManager
        
        # Create temporary directory for testing
        with tempfile.TemporaryDirectory() as temp_dir:
            print(f"   Using temp directory: {temp_dir}")
            
            # Initialize checkpoint manager
            manager = ModelCheckpointManager(temp_dir, use_ddp=False)
            
            # Create a simple test model
            model = nn.Linear(10, 5)
            optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
            
            print("✅ ModelCheckpointManager initialized successfully")
            print(f"   - Checkpoint directory: {temp_dir}")
            print(f"   - Initial best score: {manager.best_score}")
            print(f"   - Initial best epoch: {manager.best_epoch}")
            
            # Test saving checkpoints with different scores
            test_scores = [0.5, 0.3, 0.7, 0.6, 0.8, 0.4]  # Mix of improving and non-improving
            expected_best_epochs = []
            
            for epoch, score in enumerate(test_scores):
                print(f"\n--- Testing epoch {epoch} with score {score} ---")
                
                is_best = manager.save_checkpoint(
                    model=model,
                    optimizer=optimizer,
                    epoch=epoch,
                    validation_score=score,
                    split=0
                )
                
                # Check if files exist
                last_path = os.path.join(temp_dir, 'last.pt')
                best_path = os.path.join(temp_dir, 'best.pt')
                metadata_path = os.path.join(temp_dir, 'checkpoint_metadata.json')
                
                assert os.path.exists(last_path), "last.pt should always exist"
                print(f"   ✅ last.pt exists")
                
                if is_best:
                    assert os.path.exists(best_path), "best.pt should exist when score improves"
                    print(f"   ✅ best.pt saved (new best score: {score})")
                    expected_best_epochs.append(epoch)
                else:
                    print(f"   ℹ️  No improvement (current best: {manager.best_score})")
                
                assert os.path.exists(metadata_path), "metadata file should exist"
                print(f"   ✅ metadata file exists")
                
                # Verify metadata content
                with open(metadata_path, 'r') as f:
                    metadata = json.load(f)
                    assert 'best_score' in metadata
                    assert 'best_epoch' in metadata
                    assert 'last_updated' in metadata
                    print(f"   ✅ metadata valid: best_score={metadata['best_score']}, best_epoch={metadata['best_epoch']}")
            
            # Verify final state
            print(f"\n--- Final State ---")
            print(f"   Final best score: {manager.best_score}")
            print(f"   Final best epoch: {manager.best_epoch}")
            print(f"   Expected best score: {max(test_scores)}")
            print(f"   Expected best epoch: {test_scores.index(max(test_scores))}")
            
            assert manager.best_score == max(test_scores), "Best score should match maximum"
            assert manager.best_epoch == test_scores.index(max(test_scores)), "Best epoch should match"
            
            # Test loading saved checkpoints
            last_checkpoint = torch.load(os.path.join(temp_dir, 'last.pt'))
            best_checkpoint = torch.load(os.path.join(temp_dir, 'best.pt'))
            
            assert 'state_dict' in last_checkpoint
            assert 'optimizer' in last_checkpoint
            assert 'validation_score' in last_checkpoint
            assert 'epoch' in last_checkpoint
            print(f"   ✅ last.pt checkpoint structure valid")
            
            assert 'state_dict' in best_checkpoint
            assert 'optimizer' in best_checkpoint
            assert 'validation_score' in best_checkpoint
            assert 'epoch' in best_checkpoint
            print(f"   ✅ best.pt checkpoint structure valid")
            
            # Verify best checkpoint has the highest score
            assert best_checkpoint['validation_score'] == max(test_scores)
            print(f"   ✅ best.pt contains highest validation score")
            
            # Verify last checkpoint has the most recent score
            assert last_checkpoint['validation_score'] == test_scores[-1]
            print(f"   ✅ last.pt contains most recent validation score")
            
        print("✅ All ModelCheckpointManager tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ ModelCheckpointManager test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_ddp_compatibility():
    """Test DDP compatibility of checkpoint manager."""
    print("\n🔄 Testing DDP Compatibility...")
    
    try:
        from utils.train_utils import ModelCheckpointManager
        
        # Test with DDP disabled
        with tempfile.TemporaryDirectory() as temp_dir:
            manager_no_ddp = ModelCheckpointManager(temp_dir, use_ddp=False)
            assert manager_no_ddp._should_save() == True
            print("✅ Non-DDP mode: should_save = True")
            
            # Test with DDP enabled (simulated)
            manager_ddp = ModelCheckpointManager(temp_dir, use_ddp=True)
            # Without actual DDP initialization, should default to True
            assert manager_ddp._should_save() == True
            print("✅ DDP mode (simulated): should_save = True")
        
        return True
        
    except Exception as e:
        print(f"❌ DDP compatibility test failed: {e}")
        return False


def test_integration_with_trainer():
    """Test integration with Trainer class."""
    print("\n🔄 Testing Integration with Trainer...")
    
    try:
        from utils.train_utils import Trainer, ModelCheckpointManager
        
        # Create mock options
        class MockOpt:
            def __init__(self):
                self.ckpt_dir = tempfile.mkdtemp()
                self.bop_str = "BOP"
                self.model_arch = "AGNet-pretrain"
                self.merge_type = ""
                self.arch = "r2plus1d_18-18"
                self.group_str = ""
                self.use_ddp = False
                self.legacy_mode = False
                self.chunk_parts = 1
        
        opt = MockOpt()
        
        # Create simple model and trainer components
        model = nn.Linear(10, 4)
        criterion1 = nn.MSELoss()
        criterion2 = nn.MSELoss()
        optimizer = torch.optim.Adam(model.parameters())
        scheduler = None
        
        # Initialize trainer
        trainer = Trainer(
            model=model,
            criterion1=criterion1,
            criterion2=criterion2,
            optimizer=optimizer,
            scheduler=scheduler,
            opt=opt
        )
        
        # Check if checkpoint manager was initialized
        assert hasattr(trainer, 'checkpoint_manager')
        assert isinstance(trainer.checkpoint_manager, ModelCheckpointManager)
        print("✅ Trainer has checkpoint_manager attribute")
        print(f"   - Checkpoint directory: {trainer.checkpoint_manager.ckpt_dir}")
        
        # Clean up
        shutil.rmtree(opt.ckpt_dir)
        
        return True
        
    except Exception as e:
        print(f"❌ Trainer integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all selective checkpoint tests."""
    print("🚀 Selective Checkpoint Saving Tests")
    print("=" * 70)
    
    results = []
    
    # Test checkpoint manager
    results.append(("ModelCheckpointManager", test_checkpoint_manager()))
    
    # Test DDP compatibility
    results.append(("DDP Compatibility", test_ddp_compatibility()))
    
    # Test trainer integration
    results.append(("Trainer Integration", test_integration_with_trainer()))
    
    # Print summary
    print("\n" + "=" * 70)
    print("Selective Checkpoint Saving Test Results")
    print("=" * 70)
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ PASSED" if passed else "❌ FAILED"
        print(f"{test_name:35} : {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 70)
    if all_passed:
        print("🎉 SELECTIVE CHECKPOINT SAVING READY!")
        print("\n📊 Key Features:")
        print("   ✅ Best model saving (best.pt) when validation improves")
        print("   ✅ Last model saving (last.pt) at every epoch")
        print("   ✅ Eliminated frequent checkpoint saving")
        print("   ✅ R² score-based validation metric selection")
        print("   ✅ DDP compatibility (rank 0 only saves)")
        print("   ✅ Enhanced logging with checkpoint status")
        
        print("\n💾 Checkpoint Files:")
        print("   - best.pt: Saved only when validation score improves")
        print("   - last.pt: Always saved (most recent model)")
        print("   - checkpoint_metadata.json: Tracks best score and epoch")
        
        print("\n🚀 Benefits:")
        print("   - Reduced storage usage (no save_0.pth, save_1.pth, etc.)")
        print("   - Faster I/O operations")
        print("   - Automatic best model tracking")
        print("   - DDP-safe file operations")
        
    else:
        print("⚠️  Some selective checkpoint features need attention.")
        print("   Check the failed tests above for details.")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
