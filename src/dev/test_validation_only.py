#!/usr/bin/env python3
"""
Test validation performance optimization directly.
This script tests only the validation phase to verify the performance improvements.
"""

import os
import sys
import time
import torch
import torch.multiprocessing as mp
import numpy as np
from pathlib import Path

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def setup_test_environment():
    """Set up the test environment with minimal configuration."""
    
    # Mock options class for testing
    class TestOpt:
        def __init__(self):
            # Data paths
            self.data_root = "/raid/ryan/datasets/gait/videos/video_formated_trim"
            self.input_file = "/raid/ryan/datasets/gait/person_detection_and_tracking_results.pkl"
            self.target_file = "/raid/ryan/datasets/gait/gaitrite_full_dataset.xlsx"
            
            # Model configuration
            self.backbone = "r2plus1d_18"
            self.model_depth = 18
            self.model_arch = "AGNet-pretrain"
            self.arch = "r2plus1d_18-18"
            
            # Training configuration
            self.batch_size = 32  # Smaller batch size for testing
            self.learning_rate = 1e-4
            self.n_threads = 8
            self.CV = 1  # Single fold for testing
            self.n_iter = 1
            
            # DDP configuration - disable for testing
            self.use_ddp = False
            self.multi_gpu = False
            
            # Other settings
            self.with_segmentation = True
            self.bop_refinement = False
            self.training_from_zero = True
            self.mode = "train"
            
            # Image settings
            self.img_size = 224
            self.sample_size = (224, 224)
            self.sample_duration = 16
            self.norm_value = 255
            
            # Checkpoint settings
            self.ckpt_dir = "./ckpt_repos"
            self.bop_str = "BOP"
            self.merge_type = ""
            self.group_str = ""
            
            # Legacy settings
            self.legacy_mode = False
            self.chunk_parts = 1

            # Additional required attributes for model initialization
            self.pretrained_path = ""
            self.raw_h = 1080
            self.raw_w = 1920
            self.checkpoint = 10
            self.start_epoch = 0
            self.max_gradnorm = 1.0
            self.log_dir = "./logs"
            self.nesterov = False
            self.momentum = 0.9
            self.weight_decay = 1e-3
            self.dampening = 0
            self.optimizer = "sgd"
            self.lr_scheduler = "plateau"
            self.lr_patience = 10
            self.lr_factor = 0.1
            self.no_cuda = False
            self.gpu_id = 0
            self.distributed = False
            self.dist_url = "tcp://127.0.0.1:23456"
            self.dist_backend = "nccl"
            self.multiprocessing_distributed = False
            self.world_size = 1
            self.rank = 0
            self.load_pretrained = False
            self.clinical_test_file = ""
    
    return TestOpt()

def test_validation_performance():
    """Test validation performance with optimizations."""
    print("🧪 Testing Validation Performance Optimization")
    print("=" * 60)
    
    try:
        # Import required modules
        from utils.generate_model import init_state
        from datasets.gaitregression import generate_dataloader_for_crossvalidation
        from utils.train_utils import validate, DDPProgressLogger
        from sklearn.metrics import r2_score
        from utils.target_columns import get_target_columns
        from utils.transforms import Compose, ToTensor
        import torchvision.transforms as TF
        import sklearn.preprocessing
        
        # Set up test environment
        opt = setup_test_environment()
        
        print("✅ Test environment set up")
        print(f"   Data root: {opt.data_root}")
        print(f"   Batch size: {opt.batch_size}")
        print(f"   Model: {opt.model_arch}")
        
        # Initialize model and components
        print("\n🔧 Initializing model and components...")
        
        # Check if data exists
        if not Path(opt.data_root).exists():
            print(f"❌ Data root not found: {opt.data_root}")
            return False
        
        if not Path(opt.input_file).exists():
            print(f"❌ Input file not found: {opt.input_file}")
            return False
        
        print("✅ Data files found")
        
        # Initialize model components
        model, criterion1, criterion2, optimizer, scheduler = init_state(opt)
        print("✅ Model initialized")
        
        # Create transforms manually
        spatial_transform = {
            'train': Compose([
                TF.RandomRotation(degrees=(0, 0)),
                TF.RandomResizedCrop(size=224, scale=(0.8, 1.0)),
                ToTensor(opt.norm_value),
            ]),
            'test': Compose([
                TF.RandomRotation(degrees=(0, 0)),
                TF.CenterCrop(224),
                ToTensor(opt.norm_value),
            ])
        }

        temporal_transform = {
            'train': None,
            'test': None
        }

        target_transform = sklearn.preprocessing.StandardScaler()
        print("✅ Transforms initialized")
        
        # Create a small validation dataset
        print("\n📊 Creating validation dataset...")
        
        # Import dataset class
        from datasets.gaitregression import GAITDataset, prepare_dataset

        # Prepare dataset first
        data = prepare_dataset(
            input_file=opt.input_file,
            target_file=opt.target_file,
            target_columns=get_target_columns(opt),
            chunk_parts=opt.chunk_parts,
            target_transform=target_transform,
            legacy_mode=opt.legacy_mode
        )

        # Create dataset
        ds = GAITDataset(
            X=data['test_X'],
            y=data['test_y'],
            opt=opt,
            phase='valid',
            spatial_transform=spatial_transform['test'],
            temporal_transform=temporal_transform['test']
        )
        
        print(f"✅ Dataset loaded with {len(ds)} samples")
        
        # Create validation dataloader with small subset for testing
        valid_vids = data['test_vids'][:min(50, len(data['test_vids']))]  # Use only first 50 videos for testing

        valid_loader = generate_dataloader_for_crossvalidation(
            opt, ds, valid_vids, GAITDataset,
            phase='valid',
            spatial_transform=spatial_transform['test'],
            temporal_transform=temporal_transform['test'],
            shuffle=False
        )
        
        print(f"✅ Validation dataloader created with {len(valid_loader)} batches")
        
        # Set up for validation test
        model.eval()
        target_columns = get_target_columns(opt)
        
        # Initialize progress logger
        progress_logger = DDPProgressLogger(
            total_epochs=1,
            total_iterations_per_epoch=len(valid_loader),
            use_ddp=False  # Disable DDP for this test
        )
        
        print("\n🔍 Starting validation performance test...")
        print(f"   Batches to process: {len(valid_loader)}")
        print(f"   Expected batch size: {opt.batch_size}")
        
        # Check initial GPU memory
        if torch.cuda.is_available():
            print(f"   Initial GPU memory: {torch.cuda.memory_allocated() / 1024**2:.1f} MB")
        
        # Run validation with timing
        start_time = time.time()
        
        try:
            valid_loss, valid_score = validate(
                step=[0],
                epoch=0,
                split=0,
                data_loader=valid_loader,
                model=model,
                criterion1=criterion1,
                criterion2=criterion2,
                opt=opt,
                plotter=None,
                score_func=r2_score,
                target_transform=target_transform,
                target_columns=target_columns,
                progress_logger=progress_logger
            )
            
            elapsed_time = time.time() - start_time
            
            print(f"\n✅ Validation completed successfully!")
            print(f"   Time taken: {elapsed_time:.2f} seconds")
            print(f"   Average loss: {valid_loss:.4f}")
            print(f"   Average score: {valid_score:.4f}")
            print(f"   Processing speed: {len(valid_loader) / elapsed_time:.2f} batches/sec")
            
            # Check final GPU memory
            if torch.cuda.is_available():
                final_memory = torch.cuda.memory_allocated() / 1024**2
                print(f"   Final GPU memory: {final_memory:.1f} MB")
            
            # Performance benchmarks
            batches_per_sec = len(valid_loader) / elapsed_time
            samples_per_sec = len(valid_loader) * opt.batch_size / elapsed_time
            
            print(f"\n📊 Performance Metrics:")
            print(f"   Batches/sec: {batches_per_sec:.2f}")
            print(f"   Samples/sec: {samples_per_sec:.2f}")
            
            # Check if performance is acceptable
            if batches_per_sec > 0.5:  # At least 0.5 batches per second
                print("✅ Validation performance is acceptable!")
                return True
            else:
                print("⚠️  Validation performance is slower than expected")
                return False
                
        except torch.cuda.OutOfMemoryError as e:
            print(f"\n❌ CUDA OOM Error during validation: {e}")
            print("   The validation optimization did not prevent OOM")
            return False
        except Exception as e:
            print(f"\n❌ Error during validation: {e}")
            import traceback
            traceback.print_exc()
            return False
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("   Make sure all required modules are available")
        return False
    except Exception as e:
        print(f"❌ Setup error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run the validation performance test."""
    print("🚀 Validation Performance Optimization Test")
    print("=" * 60)
    print("This test verifies that validation performance bottlenecks are resolved:")
    print("1. 🔧 Memory optimizations (empty_cache, non_blocking)")
    print("2. 📦 Reduced batch sizes for validation")
    print("3. ⚡ Efficient data processing")
    print("4. 🚀 No CUDA OOM errors")
    
    # Run the test
    success = test_validation_performance()
    
    print("\n" + "=" * 60)
    print("🎯 Validation Performance Test Results")
    print("=" * 60)
    
    if success:
        print("🎉 VALIDATION PERFORMANCE OPTIMIZATION SUCCESS!")
        print("\n📊 Key Achievements:")
        print("   ✅ Validation runs without CUDA OOM errors")
        print("   ✅ Acceptable processing speed achieved")
        print("   ✅ Memory optimizations working effectively")
        print("   ✅ Batch size reductions preventing memory issues")
        
        print("\n🚀 The validation performance bottleneck has been resolved!")
        print("   Ready for full end-to-end pipeline testing.")
        
        return 0
    else:
        print("❌ VALIDATION PERFORMANCE OPTIMIZATION FAILED!")
        print("\n🔧 The validation bottleneck still exists.")
        print("   Further optimizations may be needed:")
        print("   - More aggressive batch size reduction")
        print("   - Additional memory clearing points")
        print("   - Model architecture optimizations")
        
        return 1

if __name__ == "__main__":
    exit(main())
