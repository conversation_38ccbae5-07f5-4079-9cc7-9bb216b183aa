import os
import time
from models import resnet
import torch
from torch import nn
from utils.parallel import DataParallelModel, DataParallelCriterion
import models.regression_model as regression_model
from torch import optim
from torch.optim import lr_scheduler
from torchvision import models
import torch.nn.utils as torch_utils
import torch.nn.functional as F

class AlternatingAdam(optim.Adam):
    def __init__(self, *args, clip_value=None, **kwargs):
        self.update_group = kwargs.pop("update_group", 1)  # 기본 그룹은 1로 설정
        self.clip_value = clip_value                           # 클리핑할 값 설정
        super().__init__(*args, **kwargs)

    def step(self, closure=None, update_group=None):
        update_group = update_group or self.update_group
        if update_group not in [1, 2]:
            raise ValueError("update_group must be 1 or 2")

        loss = None
        if closure is not None:
            with torch.enable_grad():
                loss = closure()

        for i, group in enumerate(self.param_groups[(update_group-1)::2]):
            params = group["params"]
            if self.clip_value is not None:
                torch.nn.utils.clip_grad_norm_(params, self.clip_value)

            for p in params:
                if p.grad is None:
                    continue
                grad = p.grad.data
                if grad.is_sparse:
                    raise RuntimeError("AlternatingAdam does not support sparse gradients")
                
                
                state = self.state[p]

                if len(state) == 0:
                    state["step"] = 0
                    state["exp_avg"] = torch.zeros_like(p.data)
                    state["exp_avg_sq"] = torch.zeros_like(p.data)
                
                exp_avg, exp_avg_sq = state["exp_avg"], state["exp_avg_sq"]
                beta1, beta2 = group["betas"]

                state["step"] += 1

                if group["weight_decay"] != 0:
                    grad.add_(group["weight_decay"], p.data)

                exp_avg.mul_(beta1).add_(1 - beta1, grad)
                exp_avg_sq.mul_(beta2).addcmul_(1 - beta2, grad, grad)
                denom = exp_avg_sq.sqrt().add_(group["eps"])

                bias_correction1 = 1 - beta1 ** state["step"]
                bias_correction2 = 1 - beta2 ** state["step"]
                step_size = group["lr"] * (bias_correction2 ** 0.5) / bias_correction1

                p.data.addcdiv_(-step_size, exp_avg, denom)

        self.update_group = 3 - update_group

        return loss

class MultiScaled_BCELoss(nn.Module):
    def __init__(self, n_scales):
        super().__init__()
        self.n_scales = n_scales
        self.loss_func = nn.BCEWithLogitsLoss()

    def forward(self, input, target):
        l = []
        for i in range(self.n_scales):
            target = F.interpolate(target,
                                   size=input[i].size()[2:])
            l.append(self.loss_func(input[i], target))

        return torch.stack(l).mean()


def generate_backbone(opt, pretrained=True):

    net = None

    if opt.backbone == '3D-resnet':
        if opt.model_depth == 18:
            net = resnet.resnet18(
                sample_size=opt.sample_size,
                sample_duration=opt.sample_duration, shortcut_type='A')

        elif opt.model_depth == 34:
            net = resnet.resnet34(
                sample_size=opt.sample_size,
                sample_duration=opt.sample_duration, shortcut_type='A')

        elif opt.model_depth == 50:
            net = resnet.resnet50(
                sample_size=opt.sample_size,
                sample_duration=opt.sample_duration, shortcut_type='B')

        elif opt.model_depth == 101:
            net = resnet.resnet101(
                sample_size=opt.sample_size,
                sample_duration=opt.sample_duration, shortcut_type='B')

        else:
            ValueError("Invalid model depth")

    # other models...
    if opt.backbone == '2D-resnet':
        if opt.mode == "preprocess__feature":
            if opt.model_depth == 50:
                net = models.resnet50(pretrained=pretrained)

            elif opt.model_depth == 101:
                net = models.resnet101(pretrained=pretrained)

            elif opt.model_depth == 152:
                net = models.resnet152(pretrained=pretrained)

            for param in net.parameters():
                param.requires_grad = False
        else:
            return net

    if opt.backbone == 'r2plus1d_18':
        net = models.video.r2plus1d_18(pretrained=pretrained)

    # if pre-trained modelfile exists...
    if opt.pretrained_path:
        print(f"Load pretrained model from {opt.pretrained_path}...")
        net = nn.DataParallel(net)

        # laod pre-trained model
        pretrain = torch.load(opt.pretrained_path,
                              map_location=torch.device('cpu'))
        net.load_state_dict(pretrain['state_dict'])

    # net will be uploaded to GPU later..

    return net


def generate_regression_model(backbone, opt):

    net = None

    criterion = nn.MSELoss()

    if opt.backbone == "3D-resnet":

        if opt.model_arch == 'SPP':
            net = regression_model.SpatialPyramid(
                backbone=backbone, dilation_config=(1, 6, 12, 18, 24),
                num_units=opt.num_units, n_factors=opt.n_factors,
                kernel_size=3, drop_rate=opt.drop_rate)

        elif opt.model_arch == "HPP":
            if opt.merge_type == 'addition':
                net = regression_model.HPP_Addition_Net(
                    num_units=opt.num_units, n_factors=opt.n_factors,
                    backbone=backbone, drop_rate=opt.drop_rate,
                    n_groups=opt.n_groups)
            elif opt.merge_type == '1x1_C':
                net = regression_model.HPP_1x1_Net(
                    num_units=opt.num_units, n_factors=opt.n_factors,
                    backbone=backbone, drop_rate=opt.drop_rate,
                    attention=opt.attention,
                    n_groups=opt.n_groups)

        elif opt.model_arch == 'naive':
            net = regression_model.Naive_Flatten_Net(num_units=opt.num_units,
                                                     n_factors=opt.n_factors,
                                                     backbone=backbone,
                                                     drop_rate=opt.drop_rate)

    elif opt.backbone == "2D-resnet":
        if opt.model_arch == 'DeepFFT':
            net = regression_model.DeepFFT(num_feats=2048,
                                           n_factors=opt.n_factors,
                                           num_freq=100,
                                           drop_rate=opt.drop_rate)
    elif opt.backbone == 'r2plus1d_18':
        if opt.model_arch == 'AGNet':
            pretrained_agnet = regression_model.AGNet_Mean(
                backbone, hidden_size=512, out_size=16, drop_rate=0.0, freeze=True)
            pretrained_agnet = load_pretrained_ckpt(opt,
                                                    pretrained_agnet)

            net = regression_model.AGNet(
                pretrained_agnet,
                backbone, hidden_size=1024, out_size=4)
        elif opt.model_arch == 'AGNet-pretrain':
            net = regression_model.AGNet_Mean(
                backbone, hidden_size=512, out_size=16, drop_rate=0.2)
        elif opt.model_arch == 'R2Plus1D':
            pretrained_r2plus1d = regression_model.R2Plus1D_Mean(
                backbone, out_size=16, drop_rate=0.0, freeze=True)
            pretrained_r2plus1d = load_pretrained_ckpt(opt,
                                                    pretrained_r2plus1d)
            net = regression_model.R2Plus1D(
                pretrained_r2plus1d,
                backbone, out_size=4)
        elif opt.model_arch == 'R2Plus1D-pretrain':
            net = regression_model.R2Plus1D_Mean(
                backbone=backbone, out_size=16, drop_rate=0.2
            )

        criterion1 = nn.SmoothL1Loss(reduction='sum')
        criterion2 = MultiScaled_BCELoss(n_scales=4)

    # Enable GPU model & data parallelism
    if opt.multi_gpu:
        # Check if we're using DDP (DistributedDataParallel)
        use_ddp = hasattr(opt, 'use_ddp') and opt.use_ddp

        if use_ddp:
            # DistributedDataParallel setup

            # Get local rank from environment or opt
            local_rank = int(os.environ.get('LOCAL_RANK', getattr(opt, 'local_rank', 0)))
            device = torch.device(f"cuda:{local_rank}")

            # Move model to the appropriate device
            net = net.to(device)

            # Wrap model with DDP
            net = torch.nn.parallel.DistributedDataParallel(
                net,
                device_ids=[local_rank],
                output_device=local_rank,
                find_unused_parameters=False  # Optimized for better performance
            )
        else:
            # Traditional DataParallel setup (backward compatibility)
            opt.device_ids = [ id for id in range(len(os.environ.get("CUDA_VISIBLE_DEVICES").split(","))) ]
            device = torch.device("cuda:0")
            net = net.to(device)                    # 먼저 기본 GPU로 올리고

            net = nn.DataParallel(net, device_ids=opt.device_ids, output_device=0)
            # net = DataParallelModel(net, device_ids=opt.device_ids).cuda()

            # criterion1 = DataParallelCriterion(
            #     criterion1, device_ids=opt.device_ids).cuda()

            # criterion2 = DataParallelCriterion(
            #     criterion2, device_ids=opt.device_ids).cuda()

    return net, criterion1, criterion2


def init_state(opt):
    # define backbone
    backbone = generate_backbone(opt)

    # define regression model
    net, criterion1, criterion2 = generate_regression_model(backbone, opt)

    if opt.nesterov:
        dampening = 0
    else:
        dampening = opt.dampening

    # optimizer = optim.SGD(
    #     net.parameters(),
    #     lr=opt.learning_rate,
    #     momentum=opt.momentum,
    #     dampening=dampening,
    #     weight_decay=opt.weight_decay,
    #     nesterov=opt.nesterov)

    # import swats

    # optimizer = swats.SWATS(
    #     net.parameters(), lr=opt.learning_rate, weight_decay=opt.weight_decay
    # )

    params = [p for p in net.parameters() if p.requires_grad]
    optimizer = optim.Adam(
        params, lr=opt.learning_rate, weight_decay=opt.weight_decay
    )
    if opt.bop_refinement:
        is_hyperparameter = lambda name : "scaling_layer" in name # if 'scaling_layer' in param's name, then it is hyperparamter(i.e., scaling wegihts during BOP-based attention mask refinement process)
        named_params = list(net.named_parameters())
        theta1 = [param for name, param in named_params if not is_hyperparameter(name)]
        theta2 = [param for name, param in named_params if is_hyperparameter(name)]
        optimizer = AlternatingAdam([
            {"params": theta1, "lr": opt.learning_rate, "weight_decay": opt.weight_decay},
            {"params": theta2, "lr": opt.learning_rate, "weight_decay": opt.weight_decay},
        ], 
        clip_value=opt.max_gradnorm # to avoid gradient exploding, we apply gradient clipping
        )

    # optimizer = optim.RMSprop(
    #     net.parameters(), lr=opt.learning_rate, weight_decay=opt.weight_decay,
    #     momentum=opt.momentum,
    # )

    scheduler = lr_scheduler.ReduceLROnPlateau(
        optimizer, 'min', verbose=True, patience=opt.lr_patience)

    return net, criterion1, criterion2, optimizer, scheduler


def load_trained_ckpt(opt, net):

    ckpt_dir = os.path.join(opt.ckpt_dir,
                            '_'.join(filter(lambda x: x != '',
                                            [opt.bop_str,
                                             opt.model_arch,
                                             opt.merge_type,
                                             opt.arch,
                                             opt.group_str])))
    if not opt.training_from_zero and os.path.exists(ckpt_dir):
        opt.start_epoch = max([ int(os.path.splitext(p)[0].split('_')[1]) for p in os.listdir(ckpt_dir) ]) # latest one
        opt.test_epoch  = opt.start_epoch

    model_path = os.path.join(ckpt_dir, 'save_' + str(opt.test_epoch) + '.pth')

    if os.path.exists(model_path):
        print(f"Load trained model from {model_path}...")

        # laod pre-trained model
        pretrain = torch.load(model_path)
        net.load_state_dict(pretrain['state_dict'])

    return net


def load_pretrained_ckpt(opt, net):

    net = nn.DataParallel(net)

    ckpt_dir = os.path.join(opt.ckpt_dir,
                            '_'.join(filter(lambda x: x != '',
                                            [opt.bop_str,
                                             opt.model_arch+'-pretrain',
                                             opt.merge_type,
                                             opt.arch,
                                             opt.group_str])))

    model_path = os.path.join(ckpt_dir, 'save_' + str(opt.pretrain_epoch) + '.pth')
    print(f"Load pretrained model from {model_path}...")

    # laod pre-trained model
    pretrain = torch.load(model_path,
                          map_location=torch.device('cpu'))
    net.load_state_dict(pretrain['state_dict'])

    return net
