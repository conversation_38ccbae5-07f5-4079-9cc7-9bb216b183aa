def get_target_columns(opt):
    target_columns = ['Velocity', 'Cadence', 'Cycle Time(sec)/L', 'Cycle Time(sec)/R',
                      'Stride Length(cm)/L', 'Stride Length(cm)/R', 'HH Base Support(cm)/L',
                      'HH Base Support(cm)/R', 'Swing Time(sec)/L', 'Swing Time(sec)/R',
                      'Stance Time(sec)/L', 'Stance Time(sec)/R', 'Double Supp. Time(sec)/L',
                      'Double Supp. Time(sec)/R',
                      'Swing % of Cycle/L', 'Swing % of Cycle/R', 'Stance % of Cycle/L',
                      'Stance % of Cycle/R', 'Double Supp % Cycle/L', 'Double Supp % Cycle/R',
                      'Toe In / Out/L', 'Toe In / Out/R',
                      'Stride Length Std Dev/L', 'Stride Length Std Dev/R',
                      'Stride Time Std Dev/L', 'Stride Time Std Dev/R', 'CV Stride Length/L',
                      'CV Stride Length/R', 'CV Stride Time/L', 'CV Stride Time/R']

    opt.target_columns_to_train = target_columns_to_train = \
        ['Velocity', 'Cadence', 'Cycle Time(sec)/L', 'Cycle Time(sec)/R',
         'Stride Length(cm)/L', 'Stride Length(cm)/R', 'HH Base Support(cm)/L',
         'HH Base Support(cm)/R', 'Swing Time(sec)/L', 'Swing Time(sec)/R',
         'Stance Time(sec)/L', 'Stance Time(sec)/R', 'Double Supp. Time(sec)/L',
         'Double Supp. Time(sec)/R', 'Toe In / Out/L', 'Toe In / Out/R',
         'Stride Length Std Dev/L', 'Stride Length Std Dev/R',
         'Stride Time Std Dev/L', 'Stride Time Std Dev/R']
    
    if opt.legacy_mode:
        opt.target_columns_to_eval = target_columns_to_eval = \
                    ['Velocity', 'Cadence', 'Cycle Time(sec)/L', 'Cycle Time(sec)/R',
         'Stride Length(cm)/L', 'Stride Length(cm)/R', 'HH Base Support(cm)/L',
         'HH Base Support(cm)/R', 'Swing % of Cycle/L', 'Swing % of Cycle/R',
         'Stance % of Cycle/L', 'Stance % of Cycle/R', 'Double Supp % Cycle/L',
         'Double Supp % Cycle/R', 'Toe In / Out/L', 'Toe In / Out/R',
         'CV Stride Length/L', 'CV Stride Length/R',
         'CV Stride Time/L', 'CV Stride Time/R']
    else:
        opt.target_columns_to_eval = target_columns_to_eval = \
            ['Velocity', 'Cadence',
            'Cycle_Time_L', 'Cycle_Time_R',
            'Stride_Len_L', 'Stride_Len_R',
            'Supp_Base_L', 'Supp_Base_R',
            'Swing_Perc_L', 'Swing_Perc_R',
            'Stance_Perc_L', 'Stance_Perc_R',

            'D_Supp_Perc_L', 'D_Supp_Perc_R',
            'ToeInOut_L', 'ToeInOut_R',

            'StrideLen_CV_L', 'StrideLen_CV_R',
            'StrideTm_CV_L', 'StrideTm_CV_R'
            ]

    if opt.model_arch.endswith('-pretrain'):
        if opt.legacy_mode:
            return [
            'Velocity', 'Cadence', 'Cycle Time(sec)/L', 'Cycle Time(sec)/R',
            'Stride Length(cm)/L', 'Stride Length(cm)/R', 'HH Base Support(cm)/L',
            'HH Base Support(cm)/R', 'Swing % of Cycle/L', 'Swing % of Cycle/R',
            'Stance % of Cycle/L', 'Stance % of Cycle/R', 'Double Supp % Cycle/L',
            'Double Supp % Cycle/R', 'Toe In / Out/L', 'Toe In / Out/R'
        ]

        else:
            return ['Velocity', 'Cadence',
            'Cycle_Time_L', 'Cycle_Time_R',
            'Stride_Len_L', 'Stride_Len_R',
            'Supp_Base_L', 'Supp_Base_R',
            'Swing_Perc_L', 'Swing_Perc_R',
            'Stance_Perc_L', 'Stance_Perc_R',
            'D_Supp_Perc_L', 'D_Supp_Perc_R',
            'ToeInOut_L', 'ToeInOut_R']

    return target_columns_to_eval
