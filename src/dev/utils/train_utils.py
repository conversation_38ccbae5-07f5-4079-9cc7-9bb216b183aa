from pathlib import Path
from sklearn.metrics import r2_score, mean_absolute_error
from utils.generate_model import init_state
from sklearn.model_selection import KFold
import torch
import time
import os
import numpy as np
import csv
import json
import datetime
from collections import deque
from utils.target_columns import get_target_columns
import matplotlib.pyplot as plt
from torchvision.utils import make_grid
import cv2
import torchvision.transforms.functional as F
from itertools import islice
import collections


def chunk(it, size):
    it = iter(it)
    return iter(lambda: tuple(islice(it, size)), ())


def normalize_img(img):
    img = (img - np.min(img))/np.max(img)
    return np.uint8(255 * img)


def normalize_video(video):
    video = (video-video.min())/(video.max()-video.min())*255
    video = torch.from_numpy(video.cpu().numpy().astype(np.uint8))
    return video


class AverageMeter(object):
    """Computes and stores the average and current value"""

    def __init__(self):
        self.reset()

    def reset(self):
        self.val = 0
        self.avg = 0
        self.sum = 0
        self.count = 0

    def update(self, val, n=1):
        self.val = val
        self.sum += val * n
        self.count += n
        self.avg = self.sum / self.count


class Logger(object):

    def __init__(self, path, header):
        if not os.path.exists(path):
            os.system(f'mkdir -p {os.path.dirname(path)}')
        self.log_file = open(path, 'w')
        self.logger = csv.writer(self.log_file, delimiter='\t')

        self.logger.writerow(header)
        self.header = header

    def __del(self):
        self.log_file.close()

    def log(self, values):
        write_values = []
        for col in self.header:
            assert col in values
            write_values.append(values[col])

        self.logger.writerow(write_values)
        self.log_file.flush()


def get_mtl_loss(named_params, layers_to_share=['layer3', 'layer4'], alpha=0.1):

    params_of_task = collections.defaultdict(list)

    for name, p in named_params:
        task_name, layer_name = name.split('.')[1:3]
        if p.requires_grad and \
                layer_name in layers_to_share:
            params_of_task[task_name].append(p.view(-1))

    for k, v in params_of_task.items():
        params_of_task[k] = torch.cat(v)

    centroid_of_params = torch.stack(
        list(params_of_task.values())).mean(dim=0)

    # # for multi-task (soft weight sharing)
    distanceNorms_from_centroids = torch.stack([
        (v-centroid_of_params).norm(2) for v in params_of_task.values()]).mean(dim=0)

    mtl_penalty = centroid_of_params.norm(2) + \
        alpha * distanceNorms_from_centroids

    return mtl_penalty


class DDPProgressLogger:
    """Enhanced progress logger for DDP training with time estimates and progress tracking."""

    def __init__(self, total_epochs, total_iterations_per_epoch, use_ddp=False):
        self.total_epochs = total_epochs
        self.total_iterations_per_epoch = total_iterations_per_epoch
        self.use_ddp = use_ddp
        self.start_time = time.time()
        self.epoch_start_time = None
        self.iteration_times = deque(maxlen=100)  # Keep last 100 iteration times for ETA
        self.current_epoch = 0
        self.current_iteration = 0

        # DDP rank detection
        self.rank = 0
        self.is_main_process = True
        if use_ddp:
            try:
                import torch.distributed as dist
                if dist.is_initialized():
                    self.rank = dist.get_rank()
                    self.is_main_process = (self.rank == 0)
            except:
                pass

    def start_epoch(self, epoch):
        """Start timing for a new epoch."""
        self.current_epoch = epoch
        self.current_iteration = 0
        self.epoch_start_time = time.time()

        if self.is_main_process:
            elapsed_time = time.time() - self.start_time
            elapsed_str = str(datetime.timedelta(seconds=int(elapsed_time)))

            print(f"\n{'='*80}")
            print(f"🚀 Starting Epoch {epoch + 1}/{self.total_epochs}")
            print(f"⏱️  Elapsed Time: {elapsed_str}")
            if epoch > 0:
                avg_epoch_time = elapsed_time / epoch
                remaining_epochs = self.total_epochs - epoch
                eta_seconds = avg_epoch_time * remaining_epochs
                eta_str = str(datetime.timedelta(seconds=int(eta_seconds)))
                print(f"⏳ Estimated Time Remaining: {eta_str}")
            print(f"{'='*80}")

    def log_training_progress(self, epoch, iteration, total_iterations, losses_dict,
                            learning_rate=None, batch_size=1):
        """Log training progress with ETA and speed metrics."""
        if not self.is_main_process:
            return

        self.current_iteration = iteration
        current_time = time.time()

        # Calculate iteration timing
        if hasattr(self, '_last_log_time'):
            iter_time = current_time - self._last_log_time
            self.iteration_times.append(iter_time)
        self._last_log_time = current_time

        # Calculate progress percentages
        epoch_progress = (iteration + 1) / total_iterations * 100
        total_progress = ((epoch * total_iterations) + iteration + 1) / (self.total_epochs * total_iterations) * 100

        # Calculate speed metrics
        if self.iteration_times:
            avg_iter_time = sum(self.iteration_times) / len(self.iteration_times)
            iterations_per_sec = 1.0 / avg_iter_time if avg_iter_time > 0 else 0
            samples_per_sec = iterations_per_sec * batch_size
        else:
            iterations_per_sec = 0
            samples_per_sec = 0

        # Calculate ETA
        if self.iteration_times and len(self.iteration_times) > 5:
            remaining_iterations = (self.total_epochs - epoch) * total_iterations - (iteration + 1)
            eta_seconds = remaining_iterations * avg_iter_time
            eta_str = str(datetime.timedelta(seconds=int(eta_seconds)))
        else:
            eta_str = "Calculating..."

        # Format losses
        loss_str = " | ".join([f"{k}: {v:.4f}" for k, v in losses_dict.items()])

        # Create progress bar
        bar_length = 30
        filled_length = int(bar_length * (iteration + 1) // total_iterations)
        bar = '█' * filled_length + '░' * (bar_length - filled_length)

        # Print comprehensive progress log
        print(f"\n📊 Epoch {epoch + 1}/{self.total_epochs} | "
              f"Iter {iteration + 1:4d}/{total_iterations} | "
              f"Progress: {epoch_progress:5.1f}%")
        print(f"🔄 [{bar}] {total_progress:5.1f}% Total")
        print(f"📈 {loss_str}")
        print(f"⚡ Speed: {iterations_per_sec:.2f} it/s | {samples_per_sec:.1f} samples/s")
        if learning_rate:
            print(f"📚 LR: {learning_rate:.2e}")
        print(f"⏳ ETA: {eta_str}")

    def log_validation_start(self, epoch, split):
        """Log validation start."""
        if self.is_main_process:
            print(f"\n🔍 Validation - Epoch {epoch + 1} | Split {split}")

    def _format_metric_value(self, value):
        """
        Safely format metric values for display.

        Args:
            value: The metric value to format (can be numeric or string)

        Returns:
            str: Formatted string representation of the value
        """
        if isinstance(value, str):
            return value
        elif isinstance(value, (int, float)) and not isinstance(value, bool):
            try:
                return f"{float(value):.4f}"
            except (ValueError, TypeError):
                return str(value)
        else:
            return str(value)

    def log_validation_results(self, epoch, split, metrics_dict, is_ddp_reduced=False):
        """Log validation results."""
        if not self.is_main_process:
            return

        # Format metrics with safe formatting
        metrics_str = " | ".join([f"{k}: {self._format_metric_value(v)}" for k, v in metrics_dict.items()])
        ddp_indicator = " (DDP Reduced)" if is_ddp_reduced else ""

        print(f"✅ Validation Results{ddp_indicator}: {metrics_str}")

    def log_epoch_summary(self, epoch, train_metrics, val_metrics=None):
        """Log epoch summary."""
        if not self.is_main_process:
            return

        epoch_time = time.time() - self.epoch_start_time if self.epoch_start_time else 0
        total_time = time.time() - self.start_time

        print(f"\n📋 Epoch {epoch + 1} Summary:")
        print(f"⏱️  Epoch Time: {datetime.timedelta(seconds=int(epoch_time))}")
        print(f"⏱️  Total Time: {datetime.timedelta(seconds=int(total_time))}")

        # Training metrics with safe formatting
        train_str = " | ".join([f"{k}: {self._format_metric_value(v)}" for k, v in train_metrics.items()])
        print(f"🏋️  Training: {train_str}")

        # Validation metrics with safe formatting
        if val_metrics:
            val_str = " | ".join([f"{k}: {self._format_metric_value(v)}" for k, v in val_metrics.items()])
            print(f"🔍 Validation: {val_str}")

        print(f"{'='*80}")


class ModelCheckpointManager:
    """
    Manages selective model checkpoint saving with best/last model tracking.
    Only saves best.pt when validation improves and always saves last.pt.
    """

    def __init__(self, ckpt_dir, use_ddp=False):
        self.ckpt_dir = ckpt_dir
        self.use_ddp = use_ddp
        self.best_score = float('-inf')  # Track best validation score (higher is better for R²)
        self.best_epoch = -1
        self.metadata_file = os.path.join(ckpt_dir, 'checkpoint_metadata.json')

        # Create checkpoint directory
        if not os.path.exists(ckpt_dir):
            os.makedirs(ckpt_dir, exist_ok=True)

        # Load existing metadata if available
        self._load_metadata()

    def _load_metadata(self):
        """Load checkpoint metadata from file."""
        if os.path.exists(self.metadata_file):
            try:
                with open(self.metadata_file, 'r') as f:
                    metadata = json.load(f)
                    self.best_score = metadata.get('best_score', float('-inf'))
                    self.best_epoch = metadata.get('best_epoch', -1)
            except (json.JSONDecodeError, KeyError):
                # If metadata is corrupted, start fresh
                pass

    def _save_metadata(self):
        """Save checkpoint metadata to file."""
        metadata = {
            'best_score': float(self.best_score),
            'best_epoch': self.best_epoch,
            'last_updated': datetime.datetime.now().isoformat()
        }
        with open(self.metadata_file, 'w') as f:
            json.dump(metadata, f, indent=2)

    def _should_save(self):
        """Check if this process should handle saving (rank 0 only for DDP)."""
        if not self.use_ddp:
            return True

        try:
            import torch.distributed as dist
            if dist.is_initialized():
                return dist.get_rank() == 0
        except:
            pass
        return True

    def save_checkpoint(self, model, optimizer, epoch, validation_score, split=None):
        """
        Save checkpoint with selective strategy.

        Args:
            model: PyTorch model
            optimizer: Optimizer state
            epoch: Current epoch
            validation_score: Current validation metric (higher is better)
            split: Current CV split (optional)
        """
        if not self._should_save():
            return

        # Prepare checkpoint state
        states = {
            'epoch': epoch + 1,
            'state_dict': model.state_dict(),
            'optimizer': optimizer.state_dict(),
            'validation_score': float(validation_score),
            'split': split
        }

        # Always save last checkpoint
        last_path = os.path.join(self.ckpt_dir, 'last.pt')
        torch.save(states, last_path)

        # Save best checkpoint if score improved
        is_best = validation_score > self.best_score
        if is_best:
            self.best_score = validation_score
            self.best_epoch = epoch

            best_path = os.path.join(self.ckpt_dir, 'best.pt')
            torch.save(states, best_path)

            print(f"💾 Saved BEST model (epoch {epoch}, split {split}): "
                  f"validation score = {validation_score:.4f}")

        # Always log last model save
        print(f"💾 Saved LAST model (epoch {epoch}, split {split}): "
              f"validation score = {validation_score:.4f}")

        # Update metadata
        self._save_metadata()

        return is_best


def train_epoch(step, epoch, split, data_loader, model, criterion1, criterion2, optimizer, opt,
                plotter, score_func, target_transform, target_columns, alpha=1.0, progress_logger=None):

    # Initialize progress logger if not provided
    if progress_logger is None:
        use_ddp = hasattr(opt, 'use_ddp') and opt.use_ddp
        progress_logger = DDPProgressLogger(
            total_epochs=getattr(opt, 'n_iter', 100),
            total_iterations_per_epoch=len(data_loader),
            use_ddp=use_ddp
        )

    # Start epoch logging
    progress_logger.start_epoch(epoch)

    model.train()

    running_loss = 0.0
    running_reg_loss = 0.0
    running_seg_loss = 0.0

    # update plotter at every epoch
    update_cycle = 5

    y_pred = []
    y_true = []

    for i, batch in enumerate(data_loader):
        inputs = batch[0].cuda()
        res = model(inputs)

        seg_loss = None
        if opt.with_segmentation:
            (_, masks, targets, vids, valid_lengths) = batch
            masks = masks.cuda()
            targets = targets.cuda()
            if opt.model_arch == 'AGNet-pretrain':
                reg_outputs, _, seg_outputs = res
                reg_loss = criterion1(reg_outputs, targets)
            else:
                reg_outputs, seg_outputs = res
                reg_loss = criterion1([reg_outputs[i][:, -4:]
                                    for i in range(len(reg_outputs))], targets[:, -4:])

            seg_loss = alpha * criterion2(seg_outputs, masks)

            loss = reg_loss + seg_loss
        else:
            (_, targets, vids, valid_lengths) = batch
            targets = targets.cuda()
            if opt.model_arch.endswith('-pretrain'):
                reg_outputs, _ = res
                reg_loss = criterion1(reg_outputs, targets)
            else:
                reg_outputs, *_ = res
                reg_loss = criterion1([reg_outputs[i][:, -4:]
                                    for i in range(len(reg_outputs))], targets[:, -4:])

            loss = reg_loss

        y_true.append(target_transform.inverse_transform(targets.cpu()))
        y_pred.append(target_transform.inverse_transform(reg_outputs.detach().cpu().numpy()))

        running_loss += loss.item()
        running_reg_loss += reg_loss.item()
        if seg_loss is not None:
            running_seg_loss += seg_loss.item()

        loss.backward()
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=opt.max_gradnorm)
        optimizer.step()

        step[0] += 1

        if (i + 1) % update_cycle == 0:
            _loss = running_loss / update_cycle
            _reg_loss = running_reg_loss / update_cycle
            _seg_loss = running_seg_loss / update_cycle

            # Enhanced progress logging with DDP support
            losses_dict = {
                'Total Loss': _loss,
                'Reg Loss': _reg_loss,
                'Seg Loss': _seg_loss
            }

            # Get current learning rate
            current_lr = optimizer.param_groups[0]['lr']

            # Log progress with enhanced information
            progress_logger.log_training_progress(
                epoch=epoch,
                iteration=i,
                total_iterations=len(data_loader),
                losses_dict=losses_dict,
                learning_rate=current_lr,
                batch_size=opt.batch_size
            )

            # update visdom window (only for rank 0 in DDP)
            if plotter is not None:
                plotter.plot('loss', 'train', 'avg_loss__trace',
                             step[0], _loss)
                plotter.plot('reg_loss', 'train', 'avg_reg_loss__trace',
                             step[0], _reg_loss)
                plotter.plot('seg_loss', 'train', 'avg_seg_loss__trace',
                             step[0], _seg_loss)
                plotter.plot('lr', 'train', 'lr', step[0],
                             optimizer.param_groups[0]['lr'])

            # re-init running_loss
            running_loss = 0.0
            running_reg_loss = 0.0
            running_seg_loss = 0.0

    # log scores
    score = score_func(
        np.vstack(y_true),
        np.vstack(y_pred),
        multioutput='raw_values',
    )

    # Distributed reduction for DDP training metrics
    if hasattr(opt, 'use_ddp') and opt.use_ddp:
        import torch.distributed as dist

        if dist.is_initialized():
            # Convert score to tensor for reduction
            score_tensor = torch.tensor(score, dtype=torch.float32, device='cuda')

            # All-reduce to sum across all processes
            dist.all_reduce(score_tensor, op=dist.ReduceOp.SUM)

            # Average across all processes
            world_size = dist.get_world_size()
            score = (score_tensor / world_size).cpu().numpy()

    # Only plot on rank 0 for DDP
    should_plot = plotter is not None
    if hasattr(opt, 'use_ddp') and opt.use_ddp:
        import torch.distributed as dist
        if dist.is_initialized():
            should_plot = should_plot and (dist.get_rank() == 0)

    if should_plot:
        plotter.plot('score', 'train', 'avg_score__trace',
                     step[0], score.mean())

        for n in range(len(target_columns)):
            plotter.plot(target_columns[n] + '_score', 'train',
                         target_columns[n] + '_' +
                         'score' + '__' + 'trace',
                         step[0], score[n])

    # Old frequent checkpoint saving removed - now using selective saving strategy


def validate(step, epoch, split, data_loader,
             model, criterion1, criterion2, opt, plotter, score_func,
             target_transform, target_columns, alpha=1.0, progress_logger=None):

    # Initialize progress logger if not provided
    if progress_logger is None:
        use_ddp = hasattr(opt, 'use_ddp') and opt.use_ddp
        progress_logger = DDPProgressLogger(
            total_epochs=getattr(opt, 'n_iter', 100),
            total_iterations_per_epoch=len(data_loader),
            use_ddp=use_ddp
        )

    # Log validation start
    progress_logger.log_validation_start(epoch, split)

    model.eval()

    # Optimized validation: focus on speed and memory efficiency
    losses = AverageMeter()
    reg_losses = AverageMeter()
    seg_losses = AverageMeter()

    # Pre-allocate lists for better memory management
    y_pred_list = []
    y_true_list = []

    # Progress tracking for validation
    start_time = time.time()

    with torch.no_grad():  # Ensure no gradients for faster validation
        for i, batch in enumerate(data_loader):
            # Memory optimization: clear cache before each batch
            if i % 10 == 0:
                torch.cuda.empty_cache()

            # Optimized data loading - move to GPU efficiently
            inputs = batch[0].cuda(non_blocking=True)

            # Forward pass with memory optimization
            res = model(inputs)

            # Optimized loss computation and data collection
            seg_loss = None
            if opt.with_segmentation:
                (_, masks, targets, vids, valid_lengths) = batch
                masks = masks.cuda(non_blocking=True)
                targets = targets.cuda(non_blocking=True)

                if opt.model_arch == 'AGNet-pretrain':
                    reg_outputs, _, seg_outputs = res
                    reg_loss = criterion1(reg_outputs, targets)
                else:
                    reg_outputs, seg_outputs = res
                    reg_loss = criterion1([reg_outputs[j][:, -4:]
                                        for j in range(len(reg_outputs))], targets[:, -4:])

                seg_loss = alpha * criterion2(seg_outputs, masks)
                loss = reg_loss + seg_loss
                seg_losses.update(seg_loss.item(), inputs.size(0))
            else:
                (_, targets, vids, valid_lengths) = batch
                targets = targets.cuda(non_blocking=True)

                if opt.model_arch.endswith('-pretrain'):
                    reg_outputs, _ = res
                    reg_loss = criterion1(reg_outputs, targets)
                else:
                    reg_outputs, *_ = res
                    reg_loss = criterion1([reg_outputs[j][:, -4:]
                                        for j in range(len(reg_outputs))], targets[:, -4:])

                loss = reg_loss

            # Efficient data collection - minimize CPU-GPU transfers
            y_true_batch = target_transform.inverse_transform(targets.cpu().numpy())
            y_pred_batch = target_transform.inverse_transform(reg_outputs.detach().cpu().numpy())

            y_true_list.append(y_true_batch)
            y_pred_list.append(y_pred_batch)

            # Update loss meters
            losses.update(loss.item(), inputs.size(0))
            reg_losses.update(reg_loss.item(), inputs.size(0))

            # Progress logging every 10 batches for validation
            if progress_logger.is_main_process and (i % 10 == 0 or i == len(data_loader) - 1):
                elapsed = time.time() - start_time
                progress_pct = (i + 1) / len(data_loader) * 100

                if i > 0:
                    eta_seconds = elapsed / (i + 1) * (len(data_loader) - (i + 1))
                    eta_str = str(datetime.timedelta(seconds=int(eta_seconds)))
                else:
                    eta_str = "Calculating..."

                print(f"🔍 Validation Progress: {progress_pct:5.1f}% | "
                      f"Batch {i + 1:4d}/{len(data_loader)} | ETA: {eta_str}")

    # Efficient array concatenation
    y_true = np.vstack(y_true_list)
    y_pred = np.vstack(y_pred_list)

    # Clear GPU memory after validation
    torch.cuda.empty_cache()

    avg_loss = losses.avg
    avg_reg_loss = reg_losses.avg
    avg_seg_loss = seg_losses.avg

    score = score_func(
        np.vstack(y_true),
        np.vstack(y_pred),
        multioutput='raw_values',
    )

    # Distributed reduction for DDP validation metrics
    if hasattr(opt, 'use_ddp') and opt.use_ddp:
        import torch.distributed as dist

        if dist.is_initialized():
            # Get current rank and world size
            rank = dist.get_rank()
            world_size = dist.get_world_size()

            # Convert metrics to tensors for reduction
            loss_tensor = torch.tensor(avg_loss, dtype=torch.float32, device='cuda')
            reg_loss_tensor = torch.tensor(avg_reg_loss, dtype=torch.float32, device='cuda')
            seg_loss_tensor = torch.tensor(avg_seg_loss, dtype=torch.float32, device='cuda')
            score_tensor = torch.tensor(score, dtype=torch.float32, device='cuda')

            # All-reduce to sum across all processes
            dist.all_reduce(loss_tensor, op=dist.ReduceOp.SUM)
            dist.all_reduce(reg_loss_tensor, op=dist.ReduceOp.SUM)
            dist.all_reduce(seg_loss_tensor, op=dist.ReduceOp.SUM)
            dist.all_reduce(score_tensor, op=dist.ReduceOp.SUM)

            # Average across all processes
            avg_loss = (loss_tensor / world_size).item()
            avg_reg_loss = (reg_loss_tensor / world_size).item()
            avg_seg_loss = (seg_loss_tensor / world_size).item()
            score = (score_tensor / world_size).cpu().numpy()

            # Enhanced validation logging with progress logger
            metrics_dict = {
                'Reg Loss': avg_reg_loss,
                'Seg Loss': avg_seg_loss,
                'Score': score.mean()
            }
            progress_logger.log_validation_results(
                epoch=epoch,
                split=split,
                metrics_dict=metrics_dict,
                is_ddp_reduced=True
            )
        else:
            # Fallback to single process printing
            metrics_dict = {
                'Reg Loss': avg_reg_loss,
                'Seg Loss': avg_seg_loss,
                'Score': score.mean()
            }
            progress_logger.log_validation_results(
                epoch=epoch,
                split=split,
                metrics_dict=metrics_dict,
                is_ddp_reduced=False
            )
    else:
        # Non-DDP mode - enhanced logging
        metrics_dict = {
            'Reg Loss': avg_reg_loss,
            'Seg Loss': avg_seg_loss,
            'Score': score.mean()
        }
        progress_logger.log_validation_results(
            epoch=epoch,
            split=split,
            metrics_dict=metrics_dict,
            is_ddp_reduced=False
        )

    # update visdom window (only for rank 0 in DDP)
    should_plot = plotter is not None
    if hasattr(opt, 'use_ddp') and opt.use_ddp:
        import torch.distributed as dist
        if dist.is_initialized():
            should_plot = should_plot and (dist.get_rank() == 0)

    if should_plot:
        plotter.plot('loss', 'val', 'avg_loss__trace',
                     step[0], avg_loss)
        plotter.plot('reg_loss', 'val', 'avg_reg_loss__trace',
                     step[0], avg_reg_loss)
        plotter.plot('seg_loss', 'val', 'avg_seg_loss__trace',
                     step[0], avg_seg_loss)
        plotter.plot('score', 'val', 'avg_score__trace',
                     step[0], score.mean())

        for n in range(len(target_columns)):
            plotter.plot(target_columns[n] + '_score', 'val', target_columns[n] +
                         '_' + 'score' + '__' + 'trace', step[0], score[n])

    return avg_loss, score.mean()


class Trainer(object):
    def __init__(self,
                 model,
                 criterion1, criterion2,
                 optimizer,
                 scheduler,
                 opt,
                 spatial_transform=None,
                 temporal_transform=None,
                 target_transform=None,
                 score_func=r2_score):

        self.model = model
        self.criterion1 = criterion1
        self.criterion2 = criterion2
        self.optimizer = optimizer
        self.scheduler = scheduler
        self.opt = opt
        self.spatial_transform = spatial_transform
        self.temporal_transform = temporal_transform
        self.target_transform = target_transform

        self.score_func = score_func

        self.target_columns = get_target_columns(opt)

        # Initialize selective checkpoint manager
        ckpt_dir = os.path.join(opt.ckpt_dir,
                               '_'.join(filter(lambda x: x != '',
                                               [opt.bop_str,
                                                opt.model_arch,
                                                opt.merge_type,
                                                opt.arch,
                                                opt.group_str])))
        use_ddp = hasattr(opt, 'use_ddp') and opt.use_ddp
        self.checkpoint_manager = ModelCheckpointManager(ckpt_dir, use_ddp=use_ddp)

    def fit(self, ds, dataloader_generator, ds_class, plotter):
        entire_vids = np.array(ds.vids)

        # filter only existing videos (support both video files and frame directories)
        def video_exists(vid):
            # Check for video file first
            video_path = Path(self.opt.data_root) / (vid + '.avi')
            if video_path.exists():
                return True
            # Check for frame directory
            frame_dir = Path(self.opt.data_root) / vid
            return frame_dir.exists()

        entire_vids = np.array([x for x in ds.vids if video_exists(x)])

        # K-fold CV
        kf = KFold(n_splits=self.opt.CV)
        from collections import defaultdict

        CV_results = defaultdict(list)

        # logdir for CV(K-fold Cross Validation)
        CV_logdir = os.path.join(os.path.dirname(
            self.opt.logpath), 'CV')

        if not os.path.exists(CV_logdir):
            os.system(f"mkdir -p {CV_logdir}")

        CV_logpath = os.path.join(CV_logdir, 'results.json')

        # Initialize enhanced progress logger for DDP training
        use_ddp = hasattr(self.opt, 'use_ddp') and self.opt.use_ddp
        progress_logger = DDPProgressLogger(
            total_epochs=self.opt.n_iter,
            total_iterations_per_epoch=100,  # Will be updated per split
            use_ddp=use_ddp
        )

        if progress_logger.is_main_process:
            print('🚀 Starting Enhanced DDP Training...')
            print(f"📊 Configuration: {self.opt.CV} splits × {self.opt.n_iter} epochs")
            print(f"🔧 DDP Mode: {'Enabled' if use_ddp else 'Disabled'}")

        cv_loss = 0.
        cv_score = 0.

        step = [0]

        for split, (train, valid) in enumerate(kf.split(entire_vids)):

            train_vids, valid_vids = entire_vids[train], entire_vids[valid]

            train_loader = dataloader_generator(self.opt, ds, train_vids, ds_class,
                                                phase='train',
                                                spatial_transform=self.spatial_transform['train'],
                                                temporal_transform=self.temporal_transform['train'],
                                                shuffle=True)
            valid_loader = dataloader_generator(self.opt, ds, valid_vids, ds_class,
                                                phase='valid',
                                                spatial_transform=self.spatial_transform['test'],
                                                temporal_transform=self.temporal_transform['test'],
                                                shuffle=False)

            epoch_status = defaultdict(list)
            start_epoch =  self.opt.start_epoch
            for epoch in range(start_epoch, self.opt.n_iter):
                # Default
                dataloader = train_loader
                alpha      = 1.0
                if self.opt.bop_refinement:
                    if epoch % 2 == 0:
                        # Train mask-guided model (for training dataset)
                        # Specifically, update theta_1=[W_base, W_mask, W_fc]
                        dataloader = train_loader
                        alpha      = 1.0
                    else:
                        # Finetune scaling wegihts (for validation dataset)
                        # Specifically, update theta_2=[W_scale]
                        dataloader = valid_loader
                        alpha = 0.0 # segmentation loss strength weight

                # Update progress logger with current split info
                progress_logger.total_iterations_per_epoch = len(dataloader)

                train_epoch(step, epoch, split, dataloader, self.model,
                            self.criterion1, self.criterion2, self.optimizer,
                            self.opt, plotter,
                            self.score_func, self.target_transform,
                            self.target_columns, alpha=alpha, progress_logger=progress_logger)

                with torch.no_grad():
                    # at every train epoch, validate model!
                    valid_loss, valid_score = validate(step, epoch, split,
                                                       valid_loader,
                                                       self.model,
                                                       self.criterion1, self.criterion2,
                                                       self.opt,
                                                       plotter,
                                                       self.score_func,
                                                       self.target_transform,
                                                       self.target_columns, progress_logger=progress_logger)
                # self.scheduler.step(valid_loss)

                # Use average R² score as the primary validation metric (higher is better)
                avg_validation_score = valid_score.mean() if hasattr(valid_score, 'mean') else valid_score

                # Selective checkpoint saving: save best.pt and last.pt
                is_best = self.checkpoint_manager.save_checkpoint(
                    model=self.model,
                    optimizer=self.optimizer,
                    epoch=epoch,
                    validation_score=avg_validation_score,
                    split=split
                )

                # Log epoch summary with checkpoint info
                train_metrics = {'Loss': 'N/A', 'Score': 'N/A'}  # Could be enhanced if needed
                val_metrics = {'Loss': valid_loss, 'Score': avg_validation_score}
                progress_logger.log_epoch_summary(epoch, train_metrics, val_metrics)

                if is_best and progress_logger.is_main_process:
                    print(f"🏆 New best model! Validation score: {avg_validation_score:.4f}")

                epoch_status['loss'].append(valid_loss)
                epoch_status['score'].append(valid_score)

            last_loss = epoch_status['loss'][-1]
            last_score = epoch_status['score'][-1]

            CV_results[f'split-{split}'].append(
                dict(loss=last_loss,
                     score=last_score
                     ))

            # todo. add only last epoch into cv_loss
            cv_loss += last_loss
            cv_score += last_score

            if not self.opt.warm_start:
                # if warm-starting is False, re-init the state
                print('Re-initializing states...')
                self.model, self.criterion1, self.criterion2, self.optimizer, self.scheduler = init_state(
                    self.opt)

            # at every end of split save cv_results ( as it takes too much time...)
            with open(CV_logpath, 'w') as fp:
                json.dump(CV_results, fp)

            break

        CV_results['avg_loss'] = cv_loss / self.opt.CV
        CV_results['avg_score'] = cv_score / self.opt.CV

        # save CV_results as file.
        with open(CV_logpath, 'w') as fp:
            json.dump(CV_results, fp)
