import math
import select
import subprocess
import tempfile
from tqdm import tqdm
import os
import collections
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
import seaborn as sns
import visdom
import base64
import statsmodels.api as sm
from sklearn.metrics import r2_score, mean_squared_error

plt.ioff()


def scatterplots(target_columns, y_true, y_pred, save_dir=''):
    pred_and_gt = {k: [] for k in target_columns}

    for i, col in enumerate(target_columns):
        pred_and_gt[col].append([y_pred[:, i], y_true[:, i]])

    data = collections.defaultdict(list)

    pp = []
    gg = []
    for i, col in enumerate(pred_and_gt.keys()):
        transposed_data = list(zip(*pred_and_gt[col]))
        preds = np.concatenate(transposed_data[0])
        gts = np.concatenate(transposed_data[1])

        pp.append(preds)
        gg.append(gts)

        for p, g in zip(preds, gts):
            data["name"].append(col)
            data["pred"].append(p)
            data["gt"].append(g)

    df = pd.DataFrame(data)

    fig, axes = plt.subplots(nrows=4, ncols=5, figsize=(20, 20))

    axes = axes.flatten()

    # for i, col in enumerate(target_columns):
    #     part_of_df = df.loc[df.name == col]
    #     ax = axes[i]
    #     part_of_df.plot.scatter(
    #         x="gt", y="pred", c='green', ax=ax, label='data')
    #     ax.set_title(f'name={col}')
    
    for i, (preds, gts) in enumerate(zip(pp, gg)):
        ax = axes[i]
        preds = np.array(preds)
        gts = np.array(gts)
        lower_ixs = np.where(min(gts) <= preds)[0]
        upper_ixs = np.where(preds <= max(gts))[0]

        valid_ixs = np.array(list(set(lower_ixs).intersection(set(upper_ixs))))

        preds = preds[valid_ixs]
        gts = gts[valid_ixs]
        ax.scatter(preds, gts, c='g', label='data')
        ax.plot([min(gts), max(gts)], [min(gts), max(gts)],
                'r--', label='GT=PRED')
        ax.set_title(f'name={target_columns[i]}')
        ax.legend()

    fig.tight_layout()
    fig.subplots_adjust(top=0.88)

    img_path = os.path.join(save_dir, f'scatter_plots.png')
    os.system(f'mkdir -p {save_dir}')

    plt.savefig(img_path)
    plt.close(fig)


def dist_plots(target_columns, group_cols, y_true, y_pred, save_dir, grid_size, figsize, group=None):
    nrow, ncol = grid_size

    fig, axes = plt.subplots(nrows=nrow, ncols=ncol, figsize=figsize)
    axes = axes.flatten()

    for ax, col in zip(axes, group_cols):
        ix = target_columns.index(col)
        sns.distplot(y_pred[:, ix], hist=False, label='y_pred', ax=ax)
        sns.distplot(y_true[:, ix], hist=False, label='y_true', ax=ax)
        ax.set_title(f'Distribution of {col}')
        ax.legend()

    fig.tight_layout()

    #     img_path = os.path.join(save_dir, f'trace_plots_{group}.png')
    img_path = os.path.join(save_dir, f'dist_plots_{group}.png')

    os.system(f'mkdir -p {save_dir}')

    plt.savefig(img_path)


def margin_plots(target_columns, group_cols, y_true, y_pred, save_dir, grid_size, figsize, group=None):
    nrow, ncol = grid_size

    fig, axes = plt.subplots(nrows=nrow, ncols=ncol, figsize=figsize)
    axes = axes.flatten()

    for ax, col in zip(axes, group_cols):

        ix = target_columns.index(col)

        sampled_y_true = y_true[:, ix]
        sampled_y_pred = y_pred[:, ix]

        sorted_ixs = np.argsort(sampled_y_true)

        sampled_y_true = sampled_y_true[sorted_ixs]
        sampled_y_pred = sampled_y_pred[sorted_ixs]

        error = sampled_y_true-sampled_y_pred

        ax.set_title(col+' trace')
        ax.plot(sampled_y_true, '^', color='orange', label='y_true')
        ax.plot(sampled_y_pred, label='y_pred')
        ax.fill_between(np.arange(len(sampled_y_true)), sampled_y_true-error, sampled_y_true,
                        alpha=1, edgecolor='#3F7F4C', facecolor='#7EFF99',
                        linewidth=0, label='margin')
        ax.legend()

    fig.tight_layout()

    #     img_path = os.path.join(save_dir, f'trace_plots_{group}.png')
    img_path = os.path.join(save_dir, f'margin_plots_{group}.png')

    os.system(f'mkdir -p {save_dir}')

    plt.savefig(img_path)


def encode(tensor):
    L = tensor.size(0)
    H = tensor.size(1)
    W = tensor.size(2)

    t = tempfile.NamedTemporaryFile(suffix='.mp4')

    command = ['ffmpeg',
               '-loglevel', 'error',
               '-f', 'rawvideo',
               '-vcodec', 'rawvideo',
               '-s', '{}x{}'.format(W, H),  # size of one frame
               '-pix_fmt', 'rgb24',
               '-r', '5',  # frames per second
               '-i', '-',  # The imput comes from a pipe
               '-pix_fmt', 'yuv420p',
               '-an',  # Tells FFMPEG not to expect any audio
               '-vcodec', 'h264',
               '-f', 'mp4',
               '-y',  # overwrite
               t.name
               ]

    proc = subprocess.Popen(
        command, stdin=subprocess.PIPE, stdout=subprocess.PIPE)

    output = bytes()

    frame = 0

    print("Encoding...")

    with tqdm(total=L) as bar:
        while frame < L:
            state = proc.poll()
            if state is not None:
                print('Could not call ffmpeg (see above)')
                raise IOError

            read_ready, write_ready, _ = select.select(
                [proc.stdout], [proc.stdin], [])

            if proc.stdout in read_ready:
                buf = proc.stdout.read1(1024 * 1024)
                output += buf

            if proc.stdin in write_ready:
                proc.stdin.write(tensor[frame].numpy().tobytes())
                frame += 1
                bar.update()

        remaining_output, _ = proc.communicate()
        output += remaining_output

    data = open(t.name, 'rb').read()
    t.close()

    return data


class VisdomPlotter(object):
    """Plots to Visdom"""

    def __init__(self, env_name='main'):
        self.viz = visdom.Visdom('localhost', port=5002)
        self.env = env_name
        self.plots = {}

    def plot(self, var_name, split_name, title_name, x, y):
        if var_name not in self.plots:
            self.plots[var_name] = self.viz.line(X=np.array([x, x]), Y=np.array([y, y]), env=self.env, opts=dict(
                legend=[split_name],
                title=title_name,
                xlabel='iterations',
                ylabel=var_name
            ))
        else:
            self.viz.line(X=np.array([x]), Y=np.array(
                [y]), env=self.env, win=self.plots[var_name], name=split_name, update='append')

    def draw_video(self, win_name, title_name, video):
        output = encode(video)

        videodata = """
            <video controls>
                <source type="video/mp4" src="data:video/mp4;base64,{}">
                Your browser does not support the video tag.
            </video>
        """.format(base64.b64encode(output).decode('utf-8'))

        self.viz.text(text=videodata, win=win_name, env=self.env, opts=dict(
            title=title_name
        ))

def merge_gt_and_pred(y_true_list, y_pred_list, type_list=["Pre", "Post"]):
    assert len(y_true_list) == len(y_pred_list) == len(type_list)

    for i in range(len(type_list)):
        y_true_list[i]["Type"] = type_list[i]
        y_pred_list[i]["Type"] = type_list[i]
    
    y_true = pd.concat(y_true_list, axis=0)
    y_pred = pd.concat(y_pred_list, axis=0)

    y_true.dropna(inplace=True)
    y_pred.dropna(inplace=True)

    target_columns = y_true.columns[:-1]
    gt_and_pred_all = []
    types = y_true["Type"].to_frame()
    for p in target_columns:
        gt_and_pred_of_p = pd.concat((y_true[p], y_pred[p], types), axis=1)
        gt_and_pred_of_p.columns = ["GAITRite", "Vision", "Type"]
        gt_and_pred_of_p["Name"] = p
        gt_and_pred_all.append(gt_and_pred_of_p)
    gt_and_pred_all = pd.concat(gt_and_pred_all, axis=0)

    return gt_and_pred_all


def scatterplots_v2(target_columns, y_true_list, y_pred_list, save_path='tmp.png'):

    sns.set_style("darkgrid")

    # 팔레트 설정
    my_palette = sns.color_palette("Set2")

    # 전체 색상 팔레트 설정
    sns.set_palette(my_palette)

    gt_and_pred_all = merge_gt_and_pred(y_true_list, y_pred_list, type_list=["Pre", "Post"])

    y_true__pre = gt_and_pred_all.query("Type == 'Pre'")["GAITRite"].values.reshape(-1, len(target_columns)); y_true__post = gt_and_pred_all.query("Type == 'Post'")["GAITRite"].values.reshape(-1, len(target_columns))
    y_pred__pre = gt_and_pred_all.query("Type == 'Pre'")["Vision"].values.reshape(-1, len(target_columns)); y_pred__post = gt_and_pred_all.query("Type == 'Post'")["Vision"].values.reshape(-1, len(target_columns))
    
    # socres
    r_squared__pre = r2_score(y_true__pre, y_pred__pre, multioutput='raw_values').round(3)
    rmse__pre      = np.sqrt(mean_squared_error(y_true__pre, y_pred__pre, multioutput='raw_values')).round(3)

    r_squared__post = r2_score(y_true__post, y_pred__post, multioutput='raw_values').round(3)
    rmse__post      = np.sqrt(mean_squared_error(y_true__post, y_pred__post, multioutput='raw_values')).round(3)

    plt.figure(figsize=(18, 18))
    plt.grid('on')
    for i,col in enumerate(target_columns):
        ax = plt.subplot(math.ceil(len(target_columns) / 3),3,i+1)
        ax.set_title(col)
        sns.regplot(x="GAITRite", y="Vision", marker='o',
                    data=gt_and_pred_all.query(f"Name == '{col}' & Type == 'Pre'"),
                    ax=ax, label="Pre", scatter_kws={"edgecolor":"black", "linewidths":1})
        sns.regplot(x="GAITRite", y="Vision", marker='^',
                    data=gt_and_pred_all.query(f"Name == '{col}' & Type == 'Post'"),
                    ax=ax, label="Post", scatter_kws={"edgecolor":"black", "linewidths":1})

        handles, _ = ax.get_legend_handles_labels()
        labels = [r'Pre [$r^2 = {:0.3f}, RMSE = {:0.3f}$]'.format(r_squared__pre[i], rmse__pre[i]),
                  r'Post [$r^2 = {:0.3f}, RMSE = {:0.3f}$]'.format(r_squared__post[i], rmse__post[i])]
        
        # add legend for markers
        marker_legend = ax.legend(handles, labels, loc='upper left',
                borderpad=1, labelspacing=1.2, fontsize='small')
        marker_legend.get_frame().set_facecolor('yellow')
        marker_legend.get_frame().set_alpha(0.1)
        
        # add marker legend to main plot
        ax.add_artist(marker_legend)

    # subplot 간 간격 조절
    plt.tight_layout()
    plt.savefig(save_path)
    
def BlandAltmanplots(target_columns, y_true_list, y_pred_list, save_path='tmp.png'):
    sns.set_style("darkgrid")

    # 팔레트 설정
    my_palette = sns.color_palette("Set2")

    # 전체 색상 팔레트 설정
    sns.set_palette(my_palette)

    gt_and_pred_all = merge_gt_and_pred(y_true_list, y_pred_list, type_list=["Pre", "Post"])

    y_true__pre = gt_and_pred_all.query("Type == 'Pre'")["GAITRite"].values.reshape(-1, len(target_columns)); y_true__post = gt_and_pred_all.query("Type == 'Post'")["GAITRite"].values.reshape(-1, len(target_columns))
    y_pred__pre = gt_and_pred_all.query("Type == 'Pre'")["Vision"].values.reshape(-1, len(target_columns)); y_pred__post = gt_and_pred_all.query("Type == 'Post'")["Vision"].values.reshape(-1, len(target_columns))

    plt.figure(figsize=(18, 18))
    plt.grid('on')
    for i,col in enumerate(target_columns):
        ax = plt.subplot(math.ceil(len(target_columns) / 3),3,i+1)
        ax.set_title(col)
        sm.graphics.mean_diff_plot(y_pred__pre[:, i], y_true__pre[:, i], ax = ax, scatter_kwds={"marker": "o", "facecolor": "C1", "label": "Pre"}, mean_line_kwds={'color': 'C1'}, limit_lines_kwds={'color': 'C1'})
        sm.graphics.mean_diff_plot(y_pred__post[:, i], y_true__post[:, i], ax = ax, scatter_kwds={"marker": "^", "facecolor": "C2", "label": "Post"}, mean_line_kwds={'color': 'C2'}, limit_lines_kwds={'color': 'C2'})

        # 범례 추가
        ax.legend()

    # subplot 간 간격 조절
    plt.tight_layout()
    plt.savefig(save_path)