{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%matplotlib notebook\n", "import json\n", "import numpy as np\n", "import pandas as pd\n", "import torch\n", "from torch.utils.data import Dataset, DataLoader\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "import itertools\n", "from IPython.core.debugger import set_trace\n", "# library\n", "import matplotlib.pyplot as plt\n", "from matplotlib_venn import venn2\n", "import collections\n", "import random\n", "import re\n", "from natsort import natsorted\n", "import seaborn as sns"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Check cuda.is_available ?"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["cuda_available : True, device : cuda:0\n"]}], "source": ["cuda_available = torch.cuda.is_available()\n", "device = torch.device(\"cuda:0\") if torch.cuda.is_available() else torch.device(\"cpu\")\n", "print(\"cuda_available : {}, device : {}\".format(cuda_available, device))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Make easy & target traj data based on sample length"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["f = open('./UDysRS_UPDRS_Export/LA_split_all_export.txt')\n", "json_d = json.load(f)\n", "\n", "for key,meta_dict in json_d.items():\n", "\n", "    all_body_parts = meta_dict['position'].keys()\n", "\n", "    tmp_dict = collections.defaultdict(list)\n", "\n", "    part_active = list(filter(lambda x: x.endswith('_act'), all_body_parts))\n", "    part_rst = list(filter(lambda x: x.endswith('_rst'), all_body_parts))\n", "\n", "    # add active data first\n", "    for part in part_active:\n", "        pure_part = part.split('_')[0]\n", "        tmp_dict[pure_part] += meta_dict['position'][part]\n", "\n", "    # append rst data\n", "    for part in part_rst:\n", "        pure_part = part.split('_')[0]\n", "        tmp_dict[pure_part] += meta_dict['position'][part]\n", "    \n", "    json_d[key]['position'] = tmp_dict\n", "\n", "data = collections.defaultdict(list)\n", "\n", "for key,meta_dict in json_d.items():\n", "    n_joints = len(meta_dict['position'].keys())\n", "\n", "    # time-major\n", "    time_series_data = np.asanyarray(list(meta_dict['position'].values())).transpose(1,0,2)\n", "    time_series_data = time_series_data.reshape(-1, n_joints*2)  # vectorisze; e.g. seq of position vectors\n", "\n", "    if len(time_series_data) < 300:\n", "        data['easy'].append((key,meta_dict))\n", "    if len(time_series_data) < 400:\n", "        data['hard'].append((key,meta_dict))\n", "    data['target'].append((key,meta_dict))\n", "\n", "json.dump(dict(data['easy']), open('./UDysRS_UPDRS_Export/LA_split_easy_export.txt','w'))\n", "json.dump(dict(data['hard']), open('./UDysRS_UPDRS_Export/LA_split_hard_export.txt','w'))\n", "json.dump(dict(data['target']), open('./UDysRS_UPDRS_Export/LA_split_target_export.txt','w'))"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["class ParkinsonDataset(Dataset):\n", "    def __init__(self, \n", "                 task_type,\n", "                 train_or_test,\n", "                 curriculum_level='target',\n", "                 split_ratio=(0.8, 0.2),\n", "                 UDysRS_rating_file = './UDysRS_UPDRS_Export/UDysRS.txt',\n", "                 UPDRS_rating_file = './UDysRS_UPDRS_Export/UPDRS.txt',\n", "                 CAPSIT_rating_file = './UDysRS_UPDRS_Export/CAPSIT.txt',\n", "                 sn_file = './UDysRS_UPDRS_Export/sn_numbers.txt',\n", "                 maxlen=600,\n", "                 seed=5\n", "                ):\n", "        \n", "        if task_type not in ['typeA', 'typeB']:\n", "            raise ValueError('Task type is not valid [ typeA | typeB ]')\n", "        \n", "        self.task_type = task_type\n", "        self.maxlen = maxlen\n", "\n", "        # support 2 type of tasks ( A : \"Communication/Drinking Tasks\",\n", "        #                           B : \"Leg Agility Task\" )\n", "        trajectory_files = ['./UDysRS_UPDRS_Export/Communication_all_export.txt', \n", "                            './UDysRS_UPDRS_Export/Drinking_all_export.txt']  if task_type=='typeA' else \\\n", "                              [f'./UDysRS_UPDRS_Export/LA_split_{curriculum_level}_export.txt']\n", "\n", "        def load_data(file_path):\n", "            with open(file_path, 'r') as infile:\n", "                data = json.load(infile)\n", "            return data\n", "\n", "        # input data\n", "        traj_data = {}\n", "        for traj_file in trajectory_files:\n", "            data = load_data(traj_file)\n", "            traj_data.update(data)\n", "        # target data\n", "        rating_data = {k:load_data(v) for k,v in zip(['UDysRS',\n", "                                                      'UPDRS',\n", "                                                      'CAPSIT'],\n", "                                                      [UDysRS_rating_file,\n", "                                                       UPDRS_rating_file,\n", "                                                       CAPSIT_rating_file])}\n", "\n", "        # map : trial number -> subject name\n", "        self.sn_map = load_data(sn_file)\n", "\n", "        ## preprocess input data (trajectory)\n", "        input_data = collections.defaultdict(list)\n", "                \n", "        for key,meta_dict in traj_data.items():\n", "            n_joints = len(meta_dict['position'].keys())\n", "            \n", "            # time-major\n", "            time_series_data = np.asanyarray(list(meta_dict['position'].values())).transpose(1,0,2)\n", "            time_series_data = time_series_data.reshape(-1, n_joints*2)  # vectorisze; e.g. seq of position vectors\n", "            \n", "            input_data[\"sample_id\"].append(key)\n", "            input_data[\"trajectory\"].append(time_series_data.tolist())        \n", "\n", "\n", "            \n", "        ## preprocess target data\n", "\n", "        # prepare empty data\n", "        target_data = collections.defaultdict(list)\n", "        all_trials = list(self.sn_map.keys())\n", "        \n", "        # for \"UDysRS\"\n", "        target_data[\"trial_nbr\"] = all_trials\n", "        for column_name in ['Communication', 'Drinking', 'Higher']:\n", "            for part in [ \"Neck\", \n", "                          \"Right arm/shoulder\", \"Left arm/shoulder\",\n", "                          \"Trunk\",\n", "                          \"Right leg/hip\", \"Left leg/hip\" ]:\n", "                target_data[\"UDysRS\" + '_' + column_name + '_' + part] = [ np.nan ] * len(target_data[\"trial_nbr\"])\n", "\n", "        # for \"UPDRS\"\n", "        target_data[\"UPDRS_Total\"] = [ np.nan ] * len(target_data[\"trial_nbr\"])            \n", "        \n", "        \n", "        RaitingItem2Name = { \n", "                            k: v for k,v in zip(['3.1', '3.10', '3.4', '3.5', '3.6', '3.8', '3.9'],\n", "                                                ['SPEECH', 'GAIT', 'FINGER TAPPING', 'HAND MOVEMENTS', \n", "                                                 'PRONATION-SUPINATION MOVEMENTS OFHANDS', 'LEG AGILITY', 'ARISING FROM CHAIR']\n", "                                               )\n", "                          }\n", "        \n", "        # for CAPSIT\n", "        for column_name in rating_data['CAPSIT'].keys():\n", "            for part in [ \"Neck\", \n", "                          \"Trunk\", \n", "                          \"Upper limb right\",\"Upper limb left\",\n", "                          \"Lower limb right\", \"Lower limb left\" ]:\n", "                target_data[\"CAPSIT\" + '_' + RaitingItem2Name[column_name] + '_' + part] = [ np.nan ] * len(target_data[\"trial_nbr\"])\n", "\n", "        \n", "        # part 1 : 'UDysRS'            \n", "        for column_name, meta_dict in rating_data['UDysRS'].items():\n", "            trial_nbrs = meta_dict.keys()\n", "            for trial_nbr in trial_nbrs:\n", "                try:\n", "                    ix = all_trials.index(trial_nbr)\n", "                except ValueError:\n", "                    # if trial_nbr is not found in all_trials, Skip\n", "                    continue\n", "                    \n", "                for p_ix, part in enumerate([ \"Neck\", \n", "                                              \"Right arm/shoulder\", \"Left arm/shoulder\",\n", "                                              \"Trunk\",\n", "                                              \"Right leg/hip\", \"Left leg/hip\"]):\n", "                    target_data[\"UDysRS\" + '_' + column_name + '_' + part][ix] = meta_dict[trial_nbr][p_ix]\n", "\n", "                \n", "        \n", "            \n", "        # part 2 : 'UPDRS'\n", "        for trial_nbr, val in rating_data['UPDRS']['Total'].items():\n", "            try:\n", "                ix = all_trials.index(trial_nbr)\n", "            except ValueError:\n", "                # if trial_nbr is not found in all_trials, Skip!\n", "                continue\n", "\n", "            target_data[\"UPDRS_Total\"][ix] = val\n", "\n", "            \n", "        # part 3 : 'CAPSIT'\n", "        for column_name, meta_dict in rating_data['CAPSIT'].items():\n", "            trial_nbrs = meta_dict.keys()\n", "            for trial_nbr in trial_nbrs:\n", "                try:\n", "                    ix = all_trials.index(trial_nbr)\n", "                except ValueError:\n", "                    # if trial_nbr is not found in all_trials, Skip\n", "                    continue\n", "                    \n", "                for p_ix, part in enumerate([ \"Neck\", \n", "                                              \"Trunk\", \n", "                                             \"Upper limb right\",\"Upper limb left\",\n", "                                              \"Lower limb right\", \"Lower limb left\" ]):\n", "                    target_data[\"CAPSIT\" + '_' + RaitingItem2Name[column_name] + '_' + part][ix] = meta_dict[trial_nbr][p_ix]\n", "        \n", "\n", "        # input data frame\n", "        input_df = pd.DataFrame(data=input_data).fillna(0)\n", "\n", "        # integratged target data frame\n", "        self.target_df = target_df = pd.DataFrame(data=target_data).fillna(0)        \n", "        \n", "        # valid target indices\n", "        valid_indices = self.target_df[self.target_df[self.target_columns]!=0][self.target_columns].dropna().index\n", "        valid_trial_nbrs = self.target_df['trial_nbr'].iloc[valid_indices].values\n", "        \n", "        if self.task_type=='typeA':\n", "            regex = lambda x: '^{}-.*$'.format(x)\n", "        elif self.task_type=='typeB':\n", "            regex = lambda x: '^{}$'.format(x)\n", "            \n", "        valid_sample_id_regex = '|'.join([regex(x) for x in valid_trial_nbrs])\n", "        input_df = input_df[input_df.sample_id.str.contains(valid_sample_id_regex)]\n", "        target_df = target_df.iloc[valid_indices]\n", "                \n", "        sorted_sample_ids = natsorted(input_df.sample_id.values)\n", "        \n", "        # set random seed, to consistency of performance\n", "        np.random.seed(seed)\n", "        \n", "        # shuffle before split\n", "        input_df = input_df.iloc[np.random.permutation(len(input_df))]\n", "        target_df = target_df.iloc[np.random.permutation(len(target_df))]\n", "        \n", "        if train_or_test=='train':\n", "            self.input_df = input_df[input_df.sample_id.isin(sorted_sample_ids[:int(len(input_df)*split_ratio[0])])]\n", "            self.target_df = target_df\n", "            \n", "        elif train_or_test=='test':\n", "            self.input_df = input_df[input_df.sample_id.isin(sorted_sample_ids[int(len(input_df)*split_ratio[0]):])]\n", "            self.target_df = target_df\n", "            \n", "    @property\n", "    def list_of_ratings(self):\n", "        if self.task_type == 'typeA':\n", "            list_of_ratings = ['UDysRS']\n", "        elif self.task_type == 'typeB':\n", "#             list_of_ratings = ['UPDRS', 'CAPSIT']\n", "            list_of_ratings = ['UPDRS']\n", "            \n", "        return list_of_ratings\n", "    \n", "    @property\n", "    def target_columns(self):\n", "        _target_columns = []\n", "        for rating_name in self.list_of_ratings:\n", "            # filter columns by name\n", "            _target_columns += list(filter(lambda x: x.startswith(rating_name), self.target_df.columns))\n", "\n", "        return _target_columns\n", "    \n", "    def __len__(self):\n", "        return len(self.input_df)\n", "    \n", "    def __getitem__(self, idx):\n", "        sample_id = self.input_df.iloc[idx].sample_id\n", "        trial_nbr = re.split(\"\\s|-\", sample_id)[0]  \n", "        \n", "        input_data = self.input_df[self.input_df.sample_id==sample_id].iloc[:, 1:].values[0]\n", "        target_data = self.target_df[self.target_df.trial_nbr==trial_nbr][self.target_columns].values[0]\n", "        \n", "        input_data = np.asanyarray(list(np.asanyarray(x) for x in input_data)).squeeze(axis=0)\n", "        target_data = np.asanyarray(list(np.asanyarray(x) for x in target_data))\n", "        \n", "        seq_len = len(input_data)\n", "        \n", "        # zero padding\n", "        input_data = np.pad(input_data, ((0,self.maxlen-len(input_data)),(0,0)),\n", "                                               'constant', constant_values=0).transpose(1,0)\n", "\n", "        sample = { 'keypoints_seq': torch.tensor(input_data, dtype=torch.float32),\n", "                   'targets': torch.tensor(target_data, dtype=torch.float32),\n", "                   'seq_len': torch.tensor(seq_len, dtype=torch.int32)\n", "        }\n", "        \n", "        return sample\n", "\n", "class DynamicBatch:\n", "    def __init__(self, batch):\n", "        integrated_data = list(zip(*[x.values() for x in batch]))\n", "\n", "        maxlen = max(integrated_data[2])\n", "        \n", "        self.keypoints_seq = torch.stack(integrated_data[0], 0)[..., :maxlen]\n", "        self.targets = torch.stack(integrated_data[1], 0)\n", "        self.seq_len = torch.stack(integrated_data[2], 0)\n", "    \n", "    def pin_memory(self):\n", "        self.keypoints_seq = self.keypoints_seq.pin_memory()\n", "        self.targets = self.targets.pin_memory()\n", "        self.seq_len = self.seq_len.pin_memory()\n", "        return self\n", "\n", "def collate_wrapper(batch):\n", "    return DynamicBatch(batch)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"scrolled": true}, "outputs": [], "source": ["easy_dataset = { x : ParkinsonDataset(task_type='typeB', train_or_test=x, curriculum_level='easy') \\\n", "                        for x in ['train', 'test'] }\n", "hard_dataset = { x : ParkinsonDataset(task_type='typeB', train_or_test=x, curriculum_level='hard') \\\n", "                        for x in ['train', 'test'] }\n", "target_dataset = { x : ParkinsonDataset(task_type='typeB', train_or_test=x, curriculum_level='target') \\\n", "                        for x in ['train', 'test'] }\n", "\n", "easy_dataloader = { x : DataLoader(easy_dataset[x],\n", "                        batch_size=5,\n", "                        shuffle=True,\n", "                        num_workers=4) \\\n", "                    for x in ['train', 'test'] }\n", "\n", "hard_dataloader = { x : DataLoader(hard_dataset[x],\n", "                        batch_size=5,\n", "                        shuffle=True,\n", "                        num_workers=4) \\\n", "                    for x in ['train', 'test'] }\n", "\n", "target_dataloader = { x : DataLoader(target_dataset[x],\n", "                        batch_size=5,\n", "                        shuffle=True,\n", "                        num_workers=4) \\\n", "                    for x in ['train', 'test'] }"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Define DNN"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["class Conv1d(nn.Conv1d):\n", "    def __init__(self, in_channels,\n", "                       out_channels,\n", "                       kernel_size,\n", "                       stride=1,\n", "                       padding=0,\n", "                       dilation=1,\n", "                       groups=1,\n", "                       bias=True,\n", "                       padding_type='same'):\n", "        \n", "        super(Conv1d, self).__init__(in_channels,\n", "                                     out_channels,\n", "                                     kernel_size,\n", "                                     stride,\n", "                                     padding,\n", "                                     dilation,\n", "                                     groups,\n", "                                     bias)\n", "        \n", "        self.padding_type = padding_type\n", "    \n", "    def forward(self, x, debug=False):\n", "        _, _, input_length = x.size()\n", "        if debug:\n", "            set_trace()\n", "        if self.padding_type == 'same':\n", "            padding_need = round((input_length * (self.stride[0]-1) + self.kernel_size[0] - self.stride[0]) / 2)\n", "            \n", "        return F.conv1d(x, self.weight, self.bias, self.stride, \n", "                        padding_need, self.dilation, self.groups)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["class ResidualBlock(nn.Module):\n", "    def __init__(self, C_in, C_out, pool, highway=True):\n", "        super(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        self.pool = pool\n", "        self.highway = highway\n", "                \n", "        stride = 1\n", "        \n", "        if C_in != C_out:\n", "            C = C_out\n", "        else:\n", "            C = C_in = C_out\n", "            \n", "        if pool:\n", "            # input dimension matchig\n", "            self.conv_1x1_matching = Conv1d(C_in, C, kernel_size=1, stride=1, padding_type='same')\n", "            self.bn_1x1_matching = nn.BatchNorm1d(C)\n", "\n", "            # for pooling of residual path\n", "            stride = 2\n", "            self.conv_1x1_pool = Conv1d(C_in, C, kernel_size=1, stride=2, padding_type='same')\n", "            self.bn_1x1_pool= nn.BatchNorm1d(C)\n", "                \n", "        # conv_1x1_a : reduce number of channels by factor of 4 (output_channel = C/4)\n", "        self.conv_1x1_a = Conv1d(C, int(C/4), kernel_size=1, stride=stride, padding_type='same')\n", "        self.bn_1x1_a = nn.BatchNorm1d(int(C/4))\n", "        \n", "        # conv_3x3_b : more wide receptive field (output_channel = C/4)\n", "        self.conv_3x3_b = Conv1d(int(C/4), int(C/4), kernel_size=3, stride=1, padding_type='same')\n", "        self.bn_3x3_b = nn.BatchNorm1d(int(C/4))\n", "        \n", "        # conv_1x1_c : recover org channel C (output_channel = C)\n", "        self.conv_1x1_c = Conv1d(int(C/4), C, kernel_size=1, stride=1, padding_type='same')\n", "        self.bn_1x1_c = nn.BatchNorm1d(C)\n", "        \n", "        if highway:\n", "            # conv_1x1_g : gating for highway network\n", "            self.conv_1x1_g = Conv1d(C, C, kernel_size=1, stride=1, padding_type='same')\n", "        \n", "        # output\n", "        self.bn_1x1_out = nn.BatchNorm1d(C)\n", "        \n", "    \n", "    def forward(self, x):\n", "        '''\n", "            x : size = (batch, C, maxlen)\n", "        '''\n", "        \n", "        res = x\n", "        \n", "        if self.pool:\n", "            # input dimension matching with 1x1 conv\n", "            x = self.conv_1x1_matching(x)\n", "            x = self.bn_1x1_matching(x)\n", "            \n", "            # pooling of residual path\n", "            res = self.conv_1x1_pool(res)\n", "            res = self.bn_1x1_pool(res)\n", "        \n", "        # 1x1_a (C/4)\n", "        x = self.conv_1x1_a(x)\n", "        x = self.bn_1x1_a(x)\n", "        x = <PERSON>.relu(x)\n", "        \n", "        # 3x3_b (C/4)\n", "        x = self.conv_3x3_b(x)\n", "        x = self.bn_3x3_b(x)\n", "        x = <PERSON>.relu(x)\n", "        \n", "        # 1x1_c (C)\n", "        x = self.conv_1x1_c(x)\n", "        x = self.bn_1x1_c(x)\n", "        \n", "        if self.highway:\n", "            # gating mechanism from \"highway network\"\n", "            \n", "            # gating factors controll intensity between x and f(x)\n", "            # gating = 1.0 (short circuit) --> output is identity (same as initial input)\n", "            # gating = 0.0 (open circuit)--> output is f(x) (case of non-residual network)\n", "            gating = torch.sigmoid(self.conv_1x1_g(x))\n", "            \n", "            # apply gating mechanism\n", "            x = gating * res + (1.0 - gating) * F.relu(x)\n", "\n", "            \n", "        else:\n", "            # normal residual ops (addition)\n", "            x = F.relu(x) + res\n", "            \n", "#         x = self.bn_1x1_out(x)\n", "#         x = <PERSON>.relu(x)\n", "        \n", "        return x"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["layer : 0, block : 0, C_in/C_out : 64/64\n", "layer : 1, block : 0, C_in/C_out : 64/64\n", "layer : 2, block : 0, C_in/C_out : 64/64\n", "layer : 0, block : 1, C_in/C_out : 64/128\n", "layer : 1, block : 1, C_in/C_out : 128/128\n", "layer : 2, block : 1, C_in/C_out : 128/128\n", "layer : 3, block : 1, C_in/C_out : 128/128\n", "layer : 0, block : 2, C_in/C_out : 128/128\n", "layer : 1, block : 2, C_in/C_out : 128/128\n", "layer : 2, block : 2, C_in/C_out : 128/128\n", "layer : 3, block : 2, C_in/C_out : 128/128\n", "layer : 4, block : 2, C_in/C_out : 128/128\n", "layer : 5, block : 2, C_in/C_out : 128/128\n", "Single GPU mode\n"]}, {"data": {"text/plain": ["Net(\n", "  (model): Sequential(\n", "    (0): Conv1d(12, 64, kernel_size=(7,), stride=(2,))\n", "    (1): BatchNorm1d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "    (2): ReLU()\n", "    (3): MaxPool1d(kernel_size=(3,), stride=2, padding=0, dilation=1, ceil_mode=False)\n", "    (4): Conv1d(64, 64, kernel_size=(3,), stride=(1,))\n", "    (5): BatchNorm1d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "    (6): ReLU()\n", "    (7): MaxPool1d(kernel_size=(3,), stride=2, padding=0, dilation=1, ceil_mode=False)\n", "    (8): ResidualBlock(\n", "      (conv_1x1_a): Conv1d(64, 16, kernel_size=(1,), stride=(1,))\n", "      (bn_1x1_a): BatchNorm1d(16, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv_3x3_b): Conv1d(16, 16, kernel_size=(3,), stride=(1,))\n", "      (bn_3x3_b): BatchNorm1d(16, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv_1x1_c): Conv1d(16, 64, kernel_size=(1,), stride=(1,))\n", "      (bn_1x1_c): BatchNorm1d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv_1x1_g): Conv1d(64, 64, kernel_size=(1,), stride=(1,))\n", "      (bn_1x1_out): BatchNorm1d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "    )\n", "    (9): ResidualBlock(\n", "      (conv_1x1_a): Conv1d(64, 16, kernel_size=(1,), stride=(1,))\n", "      (bn_1x1_a): BatchNorm1d(16, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv_3x3_b): Conv1d(16, 16, kernel_size=(3,), stride=(1,))\n", "      (bn_3x3_b): BatchNorm1d(16, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv_1x1_c): Conv1d(16, 64, kernel_size=(1,), stride=(1,))\n", "      (bn_1x1_c): BatchNorm1d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv_1x1_g): Conv1d(64, 64, kernel_size=(1,), stride=(1,))\n", "      (bn_1x1_out): BatchNorm1d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "    )\n", "    (10): ResidualBlock(\n", "      (conv_1x1_a): Conv1d(64, 16, kernel_size=(1,), stride=(1,))\n", "      (bn_1x1_a): BatchNorm1d(16, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv_3x3_b): Conv1d(16, 16, kernel_size=(3,), stride=(1,))\n", "      (bn_3x3_b): BatchNorm1d(16, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv_1x1_c): Conv1d(16, 64, kernel_size=(1,), stride=(1,))\n", "      (bn_1x1_c): BatchNorm1d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv_1x1_g): Conv1d(64, 64, kernel_size=(1,), stride=(1,))\n", "      (bn_1x1_out): BatchNorm1d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "    )\n", "    (11): Residual<PERSON>lock(\n", "      (conv_1x1_a): Conv1d(128, 32, kernel_size=(1,), stride=(1,))\n", "      (bn_1x1_a): BatchNorm1d(32, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv_3x3_b): Conv1d(32, 32, kernel_size=(3,), stride=(1,))\n", "      (bn_3x3_b): BatchNorm1d(32, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv_1x1_c): Conv1d(32, 128, kernel_size=(1,), stride=(1,))\n", "      (bn_1x1_c): BatchNorm1d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv_1x1_g): Conv1d(128, 128, kernel_size=(1,), stride=(1,))\n", "      (bn_1x1_out): BatchNorm1d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "    )\n", "    (12): Residual<PERSON>lock(\n", "      (conv_1x1_a): Conv1d(128, 32, kernel_size=(1,), stride=(1,))\n", "      (bn_1x1_a): BatchNorm1d(32, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv_3x3_b): Conv1d(32, 32, kernel_size=(3,), stride=(1,))\n", "      (bn_3x3_b): BatchNorm1d(32, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv_1x1_c): Conv1d(32, 128, kernel_size=(1,), stride=(1,))\n", "      (bn_1x1_c): BatchNorm1d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv_1x1_g): Conv1d(128, 128, kernel_size=(1,), stride=(1,))\n", "      (bn_1x1_out): BatchNorm1d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "    )\n", "    (13): Residual<PERSON>lock(\n", "      (conv_1x1_a): Conv1d(128, 32, kernel_size=(1,), stride=(1,))\n", "      (bn_1x1_a): BatchNorm1d(32, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv_3x3_b): Conv1d(32, 32, kernel_size=(3,), stride=(1,))\n", "      (bn_3x3_b): BatchNorm1d(32, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv_1x1_c): Conv1d(32, 128, kernel_size=(1,), stride=(1,))\n", "      (bn_1x1_c): BatchNorm1d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv_1x1_g): Conv1d(128, 128, kernel_size=(1,), stride=(1,))\n", "      (bn_1x1_out): BatchNorm1d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "    )\n", "    (14): Residual<PERSON>lock(\n", "      (conv_1x1_a): Conv1d(128, 32, kernel_size=(1,), stride=(1,))\n", "      (bn_1x1_a): BatchNorm1d(32, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv_3x3_b): Conv1d(32, 32, kernel_size=(3,), stride=(1,))\n", "      (bn_3x3_b): BatchNorm1d(32, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv_1x1_c): Conv1d(32, 128, kernel_size=(1,), stride=(1,))\n", "      (bn_1x1_c): BatchNorm1d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv_1x1_g): Conv1d(128, 128, kernel_size=(1,), stride=(1,))\n", "      (bn_1x1_out): BatchNorm1d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "    )\n", "    (15): Residual<PERSON>lock(\n", "      (conv_1x1_a): Conv1d(128, 32, kernel_size=(1,), stride=(1,))\n", "      (bn_1x1_a): BatchNorm1d(32, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv_3x3_b): Conv1d(32, 32, kernel_size=(3,), stride=(1,))\n", "      (bn_3x3_b): BatchNorm1d(32, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv_1x1_c): Conv1d(32, 128, kernel_size=(1,), stride=(1,))\n", "      (bn_1x1_c): BatchNorm1d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv_1x1_g): Conv1d(128, 128, kernel_size=(1,), stride=(1,))\n", "      (bn_1x1_out): BatchNorm1d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "    )\n", "    (16): Residual<PERSON>lock(\n", "      (conv_1x1_a): Conv1d(128, 32, kernel_size=(1,), stride=(1,))\n", "      (bn_1x1_a): BatchNorm1d(32, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv_3x3_b): Conv1d(32, 32, kernel_size=(3,), stride=(1,))\n", "      (bn_3x3_b): BatchNorm1d(32, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv_1x1_c): Conv1d(32, 128, kernel_size=(1,), stride=(1,))\n", "      (bn_1x1_c): BatchNorm1d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv_1x1_g): Conv1d(128, 128, kernel_size=(1,), stride=(1,))\n", "      (bn_1x1_out): BatchNorm1d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "    )\n", "    (17): Residual<PERSON>lock(\n", "      (conv_1x1_a): Conv1d(128, 32, kernel_size=(1,), stride=(1,))\n", "      (bn_1x1_a): BatchNorm1d(32, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv_3x3_b): Conv1d(32, 32, kernel_size=(3,), stride=(1,))\n", "      (bn_3x3_b): BatchNorm1d(32, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv_1x1_c): Conv1d(32, 128, kernel_size=(1,), stride=(1,))\n", "      (bn_1x1_c): BatchNorm1d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv_1x1_g): Conv1d(128, 128, kernel_size=(1,), stride=(1,))\n", "      (bn_1x1_out): BatchNorm1d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "    )\n", "    (18): Residual<PERSON>lock(\n", "      (conv_1x1_a): Conv1d(128, 32, kernel_size=(1,), stride=(1,))\n", "      (bn_1x1_a): BatchNorm1d(32, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv_3x3_b): Conv1d(32, 32, kernel_size=(3,), stride=(1,))\n", "      (bn_3x3_b): BatchNorm1d(32, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv_1x1_c): Conv1d(32, 128, kernel_size=(1,), stride=(1,))\n", "      (bn_1x1_c): BatchNorm1d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv_1x1_g): Conv1d(128, 128, kernel_size=(1,), stride=(1,))\n", "      (bn_1x1_out): BatchNorm1d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "    )\n", "    (19): Residual<PERSON>lock(\n", "      (conv_1x1_a): Conv1d(128, 32, kernel_size=(1,), stride=(1,))\n", "      (bn_1x1_a): BatchNorm1d(32, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv_3x3_b): Conv1d(32, 32, kernel_size=(3,), stride=(1,))\n", "      (bn_3x3_b): BatchNorm1d(32, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv_1x1_c): Conv1d(32, 128, kernel_size=(1,), stride=(1,))\n", "      (bn_1x1_c): BatchNorm1d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv_1x1_g): Conv1d(128, 128, kernel_size=(1,), stride=(1,))\n", "      (bn_1x1_out): BatchNorm1d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "    )\n", "    (20): Residual<PERSON>lock(\n", "      (conv_1x1_a): Conv1d(128, 32, kernel_size=(1,), stride=(1,))\n", "      (bn_1x1_a): BatchNorm1d(32, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv_3x3_b): Conv1d(32, 32, kernel_size=(3,), stride=(1,))\n", "      (bn_3x3_b): BatchNorm1d(32, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv_1x1_c): Conv1d(32, 128, kernel_size=(1,), stride=(1,))\n", "      (bn_1x1_c): BatchNorm1d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "      (conv_1x1_g): Conv1d(128, 128, kernel_size=(1,), stride=(1,))\n", "      (bn_1x1_out): BatchNorm1d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)\n", "    )\n", "    (21): GAP()\n", "    (22): Dropout(p=0.5)\n", "    (23): Linear(in_features=128, out_features=1, bias=True)\n", "  )\n", ")"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["class View(nn.<PERSON><PERSON><PERSON>):\n", "    def __init__(self, *shape):\n", "        super(View, self).__init__()\n", "        self.shape = shape\n", "    def forward(self, x):\n", "        return x.view(*self.shape)\n", "\n", "class GAP(nn.Module):\n", "    def __init__(self):\n", "        super(GA<PERSON>, self).__init__()\n", "    def forward(self, x):\n", "        '''\n", "            x : size = (B, C, L)\n", "        '''\n", "        return torch.mean(x, 2)\n", "        \n", "        \n", "class Net(nn.Module):\n", "    def __init__(self, input_size, target_size, num_layers = [3,4,6], num_filters = [64,128,128]):\n", "        super(Net, self).__init__()\n", "        \n", "        self.input_size = input_size\n", "        self.target_size = target_size\n", "        \n", "        def res_blocks(residual_blocks, num_layers, num_filters, block_ix, pool_first_layer=True):\n", "            block_layers = num_layers[block_ix]\n", "\n", "            for i in range(block_layers):\n", "                # default values\n", "                pool = False\n", "                block_filters = num_filters[block_ix]\n", "                \n", "                C_in = C_out = block_filters\n", "                \n", "                if pool_first_layer and i==0:\n", "                    pool = True\n", "                if i==0 and block_ix > 0:\n", "                    C_in = num_filters[block_ix-1]\n", "                    \n", "                print(f\"layer : {i}, block : {block_ix}, C_in/C_out : {C_in}/{C_out}\")\n", "                residual_blocks.append(ResidualBlock(C_in=C_in, C_out=C_out,pool=pool, highway=True))\n", "                \n", "        residual_blocks = []\n", "\n", "        for i in range(len(num_layers)):\n", "            pool_first_layer = True\n", "            if i == 0:\n", "                pool_first_layer = False\n", "            res_blocks(residual_blocks, num_layers=num_layers, num_filters=num_filters, block_ix=i,\n", "                       pool_first_layer=pool_first_layer)\n", "                \n", "        self.model = nn.Sequential(nn.Conv1d(input_size, num_filters[0], kernel_size=7, stride=2),\n", "                                   nn.BatchNorm1d(num_filters[0]),\n", "                                   nn.ReLU(),\n", "                                   nn.MaxPool1d(kernel_size=(3,), stride=2,),\n", "                                   nn.Conv1d(num_filters[0], num_filters[0], kernel_size=3, stride=1),\n", "                                   nn.BatchNorm1d(num_filters[0]),\n", "                                   nn.ReLU(),\n", "                                   nn.MaxPool1d(kernel_size=(3,), stride=2,),\n", "                                   *residual_blocks,\n", "                                   GAP(),\n", "                                   nn.Dropout(0.5),\n", "                                   nn.Linear(num_filters[-1], target_size),\n", "#                                    View(-1,735),\n", "#                                    nn.Linear(735, num_filters[-1]),\n", "#                                    nn.ReLU(),\n", "#                                    nn.Dropout(),\n", "#                                    nn.Linear(num_filters[-1], num_filters[-1]),\n", "#                                    nn.ReLU(),\n", "#                                    nn.Dropout(),\n", "#                                    nn.Linear(num_filters[-1], target_size)\n", "                                   )\n", "        \n", "    def forward(self, x):\n", "        '''\n", "            x : size = (batch, input_size, maxlen)\n", "        '''\n", "        return self.model(x)\n", "\n", "net = Net(input_size=np.array(target_dataset['train'].input_df.trajectory.values[0]).shape[1],\n", "          target_size=len(target_dataset['train'].target_columns),\n", "          num_layers = [3,4,6], num_filters = [64,128,128])\n", "\n", "if torch.cuda.device_count() > 1:\n", "    print(\"Let's use\", torch.cuda.device_count(), \"GPUs!\")\n", "    net = nn.DataParallel(net)\n", "else:\n", "    print(\"Single GPU mode\")\n", "    \n", "net.to(device)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "###start with easy dataloader###\n", "\n"]}, {"ename": "RuntimeError", "evalue": "Given groups=1, weight of size [32, 128, 1], expected input[5, 64, 72] to have 128 channels, but got 64 channels instead", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mRuntimeError\u001b[0m                              <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-9-06387be1ec81>\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[1;32m     46\u001b[0m             \u001b[0;32mwith\u001b[0m \u001b[0mtorch\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mset_grad_enabled\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mphase\u001b[0m\u001b[0;34m==\u001b[0m\u001b[0;34m'train'\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     47\u001b[0m                 \u001b[0;31m# feed data to network\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m---> 48\u001b[0;31m                 \u001b[0moutput\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mnet\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0minput\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m     49\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     50\u001b[0m                 \u001b[0;31m# compute loss\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/.conda/envs/torch/lib/python3.6/site-packages/torch/nn/modules/module.py\u001b[0m in \u001b[0;36m__call__\u001b[0;34m(self, *input, **kwargs)\u001b[0m\n\u001b[1;32m    487\u001b[0m             \u001b[0mresult\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_slow_forward\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m*\u001b[0m\u001b[0minput\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    488\u001b[0m         \u001b[0;32melse\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 489\u001b[0;31m             \u001b[0mresult\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mforward\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m*\u001b[0m\u001b[0minput\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    490\u001b[0m         \u001b[0;32mfor\u001b[0m \u001b[0mhook\u001b[0m \u001b[0;32min\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_forward_hooks\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mvalues\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    491\u001b[0m             \u001b[0mhook_result\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mhook\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0minput\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mresult\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m<ipython-input-8-f62f5f68fbba>\u001b[0m in \u001b[0;36mforward\u001b[0;34m(self, x)\u001b[0m\n\u001b[1;32m     76\u001b[0m             \u001b[0mx\u001b[0m \u001b[0;34m:\u001b[0m \u001b[0msize\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;34m(\u001b[0m\u001b[0mbatch\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0minput_size\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mmaxlen\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     77\u001b[0m         '''\n\u001b[0;32m---> 78\u001b[0;31m         \u001b[0;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mmodel\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mx\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m     79\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     80\u001b[0m net = Net(input_size=np.array(target_dataset['train'].input_df.trajectory.values[0]).shape[1],\n", "\u001b[0;32m~/.conda/envs/torch/lib/python3.6/site-packages/torch/nn/modules/module.py\u001b[0m in \u001b[0;36m__call__\u001b[0;34m(self, *input, **kwargs)\u001b[0m\n\u001b[1;32m    487\u001b[0m             \u001b[0mresult\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_slow_forward\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m*\u001b[0m\u001b[0minput\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    488\u001b[0m         \u001b[0;32melse\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 489\u001b[0;31m             \u001b[0mresult\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mforward\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m*\u001b[0m\u001b[0minput\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    490\u001b[0m         \u001b[0;32mfor\u001b[0m \u001b[0mhook\u001b[0m \u001b[0;32min\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_forward_hooks\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mvalues\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    491\u001b[0m             \u001b[0mhook_result\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mhook\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0minput\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mresult\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/.conda/envs/torch/lib/python3.6/site-packages/torch/nn/modules/container.py\u001b[0m in \u001b[0;36mforward\u001b[0;34m(self, input)\u001b[0m\n\u001b[1;32m     90\u001b[0m     \u001b[0;32mdef\u001b[0m \u001b[0mforward\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0minput\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     91\u001b[0m         \u001b[0;32mfor\u001b[0m \u001b[0mmodule\u001b[0m \u001b[0;32min\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_modules\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mvalues\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m---> 92\u001b[0;31m             \u001b[0minput\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mmodule\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0minput\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m     93\u001b[0m         \u001b[0;32mreturn\u001b[0m \u001b[0minput\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     94\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/.conda/envs/torch/lib/python3.6/site-packages/torch/nn/modules/module.py\u001b[0m in \u001b[0;36m__call__\u001b[0;34m(self, *input, **kwargs)\u001b[0m\n\u001b[1;32m    487\u001b[0m             \u001b[0mresult\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_slow_forward\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m*\u001b[0m\u001b[0minput\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    488\u001b[0m         \u001b[0;32melse\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 489\u001b[0;31m             \u001b[0mresult\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mforward\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m*\u001b[0m\u001b[0minput\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    490\u001b[0m         \u001b[0;32mfor\u001b[0m \u001b[0mhook\u001b[0m \u001b[0;32min\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_forward_hooks\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mvalues\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    491\u001b[0m             \u001b[0mhook_result\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mhook\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0minput\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mresult\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m<ipython-input-7-6b1997f0f9b8>\u001b[0m in \u001b[0;36mforward\u001b[0;34m(self, x)\u001b[0m\n\u001b[1;32m     59\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     60\u001b[0m         \u001b[0;31m# 1x1_a (C/4)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m---> 61\u001b[0;31m         \u001b[0mx\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mconv_1x1_a\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mx\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m     62\u001b[0m         \u001b[0mx\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mbn_1x1_a\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mx\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     63\u001b[0m         \u001b[0mx\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mF\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mrelu\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mx\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/.conda/envs/torch/lib/python3.6/site-packages/torch/nn/modules/module.py\u001b[0m in \u001b[0;36m__call__\u001b[0;34m(self, *input, **kwargs)\u001b[0m\n\u001b[1;32m    487\u001b[0m             \u001b[0mresult\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_slow_forward\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m*\u001b[0m\u001b[0minput\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    488\u001b[0m         \u001b[0;32melse\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 489\u001b[0;31m             \u001b[0mresult\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mforward\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m*\u001b[0m\u001b[0minput\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    490\u001b[0m         \u001b[0;32mfor\u001b[0m \u001b[0mhook\u001b[0m \u001b[0;32min\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_forward_hooks\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mvalues\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    491\u001b[0m             \u001b[0mhook_result\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mhook\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0minput\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mresult\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m<ipython-input-6-870ff5913ce2>\u001b[0m in \u001b[0;36mforward\u001b[0;34m(self, x, debug)\u001b[0m\n\u001b[1;32m     29\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     30\u001b[0m         return F.conv1d(x, self.weight, self.bias, self.stride, \n\u001b[0;32m---> 31\u001b[0;31m                         padding_need, self.dilation, self.groups)\n\u001b[0m", "\u001b[0;31mRuntimeError\u001b[0m: Given groups=1, weight of size [32, 128, 1], expected input[5, 64, 72] to have 128 channels, but got 64 channels instead"]}], "source": ["# define criterion\n", "criterion = nn.MS<PERSON><PERSON>()\n", "# criterion = nn.L1Loss()\n", "\n", "history = {'train': [],\n", "           'test': []}\n", "\n", "import torch.optim as optim\n", "from torch.optim import lr_scheduler\n", "\n", "# Observe that all parameters are being optimized\n", "optimizer = optim.<PERSON>(net.parameters(), lr=1e-4)\n", "# optimizer = optim.RMSprop(net.parameters(), lr=1e-2)\n", "\n", "epoch_loss = {'train': 0.0, 'test': 0.0}\n", "length_vs_mse = []\n", "pred_and_gt = []\n", "\n", "dataloader = easy_dataloader\n", "print('\\n###start with easy dataloader###\\n')\n", "\n", "end_epoch = 900\n", "\n", "for epoch in range(1,end_epoch+1):\n", "    if epoch==int(end_epoch/3):\n", "        print('\\n###change to hard dataloader###\\n')\n", "        dataloader = hard_dataloader\n", "    elif epoch==int(end_epoch*(2/3)):\n", "        print('\\n###change to target dataloader###\\n')\n", "        dataloader = target_dataloader\n", "        \n", "    for phase in ['train', 'test']:\n", "        if phase=='train':\n", "            net.train()\n", "        elif phase=='test':\n", "            net.eval()\n", "        \n", "        running_loss = 0.0\n", "\n", "        for idx, batch_item in enumerate(dataloader[phase]):\n", "            input, target, seq_len = batch_item['keypoints_seq'].to(device), batch_item['targets'].to(device), \\\n", "                                      batch_item['seq_len'].to(device)\n", "            \n", "            optimizer.zero_grad()\n", "            \n", "            with torch.set_grad_enabled(phase=='train'):\n", "                # feed data to network\n", "                output = net(input)\n", "\n", "                # compute loss\n", "                loss = criterion(output, target)\n", "                \n", "                if phase=='train':\n", "                    loss.backward()\n", "                    optimizer.step()\n", "                \n", "                if epoch==end_epoch and phase=='test':\n", "                    length_vs_mse.append([seq_len.cpu().numpy(), torch.pow(output[:,0].detach()-target[:,0],2).cpu().numpy()])\n", "                    pred_and_gt.append([output[:,0].detach().cpu().numpy(), target[:,0].cpu().numpy()])\n", "            running_loss += loss.item() * len(input)\n", "            \n", "            \n", "        avg_loss = running_loss / len(dataloader[phase].dataset)\n", "                \n", "        epoch_loss[phase] += avg_loss\n", "        \n", "        if epoch % 10 == 0:\n", "            print('=================={}========================'.format(phase.upper()))\n", "            print('EPOCH : {}, AVG_MSE : {:.4f}'.format(epoch, epoch_loss[phase] / 10))\n", "            history[phase].append(epoch_loss[phase] / 10)\n", "            \n", "            # init epoch_loss at its own phase\n", "            epoch_loss[phase] = 0.0"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"application/javascript": ["/* Put everything inside the global mpl namespace */\n", "window.mpl = {};\n", "\n", "\n", "mpl.get_websocket_type = function() {\n", "    if (typeof(WebSocket) !== 'undefined') {\n", "        return WebSocket;\n", "    } else if (typeof(MozWebSocket) !== 'undefined') {\n", "        return MozWebSocket;\n", "    } else {\n", "        alert('Your browser does not have WebSocket support.' +\n", "              'Please try Chrome, Safari or Firefox ≥ 6. ' +\n", "              'Firefox 4 and 5 are also supported but you ' +\n", "              'have to enable WebSockets in about:config.');\n", "    };\n", "}\n", "\n", "mpl.figure = function(figure_id, websocket, ondownload, parent_element) {\n", "    this.id = figure_id;\n", "\n", "    this.ws = websocket;\n", "\n", "    this.supports_binary = (this.ws.binaryType != undefined);\n", "\n", "    if (!this.supports_binary) {\n", "        var warnings = document.getElementById(\"mpl-warnings\");\n", "        if (warnings) {\n", "            warnings.style.display = 'block';\n", "            warnings.textContent = (\n", "                \"This browser does not support binary websocket messages. \" +\n", "                    \"Performance may be slow.\");\n", "        }\n", "    }\n", "\n", "    this.imageObj = new Image();\n", "\n", "    this.context = undefined;\n", "    this.message = undefined;\n", "    this.canvas = undefined;\n", "    this.rubberband_canvas = undefined;\n", "    this.rubberband_context = undefined;\n", "    this.format_dropdown = undefined;\n", "\n", "    this.image_mode = 'full';\n", "\n", "    this.root = $('<div/>');\n", "    this._root_extra_style(this.root)\n", "    this.root.attr('style', 'display: inline-block');\n", "\n", "    $(parent_element).append(this.root);\n", "\n", "    this._init_header(this);\n", "    this._init_canvas(this);\n", "    this._init_toolbar(this);\n", "\n", "    var fig = this;\n", "\n", "    this.waiting = false;\n", "\n", "    this.ws.onopen =  function () {\n", "            fig.send_message(\"supports_binary\", {value: fig.supports_binary});\n", "            fig.send_message(\"send_image_mode\", {});\n", "            if (mpl.ratio != 1) {\n", "                fig.send_message(\"set_dpi_ratio\", {'dpi_ratio': mpl.ratio});\n", "            }\n", "            fig.send_message(\"refresh\", {});\n", "        }\n", "\n", "    this.imageObj.onload = function() {\n", "            if (fig.image_mode == 'full') {\n", "                // Full images could contain transparency (where diff images\n", "                // almost always do), so we need to clear the canvas so that\n", "                // there is no ghosting.\n", "                fig.context.clearRect(0, 0, fig.canvas.width, fig.canvas.height);\n", "            }\n", "            fig.context.drawImage(fig.imageObj, 0, 0);\n", "        };\n", "\n", "    this.imageObj.onunload = function() {\n", "        fig.ws.close();\n", "    }\n", "\n", "    this.ws.onmessage = this._make_on_message_function(this);\n", "\n", "    this.ondownload = ondownload;\n", "}\n", "\n", "mpl.figure.prototype._init_header = function() {\n", "    var titlebar = $(\n", "        '<div class=\"ui-dialog-titlebar ui-widget-header ui-corner-all ' +\n", "        'ui-helper-clearfix\"/>');\n", "    var titletext = $(\n", "        '<div class=\"ui-dialog-title\" style=\"width: 100%; ' +\n", "        'text-align: center; padding: 3px;\"/>');\n", "    titlebar.append(titletext)\n", "    this.root.append(titlebar);\n", "    this.header = titletext[0];\n", "}\n", "\n", "\n", "\n", "mpl.figure.prototype._canvas_extra_style = function(canvas_div) {\n", "\n", "}\n", "\n", "\n", "mpl.figure.prototype._root_extra_style = function(canvas_div) {\n", "\n", "}\n", "\n", "mpl.figure.prototype._init_canvas = function() {\n", "    var fig = this;\n", "\n", "    var canvas_div = $('<div/>');\n", "\n", "    canvas_div.attr('style', 'position: relative; clear: both; outline: 0');\n", "\n", "    function canvas_keyboard_event(event) {\n", "        return fig.key_event(event, event['data']);\n", "    }\n", "\n", "    canvas_div.keydown('key_press', canvas_keyboard_event);\n", "    canvas_div.keyup('key_release', canvas_keyboard_event);\n", "    this.canvas_div = canvas_div\n", "    this._canvas_extra_style(canvas_div)\n", "    this.root.append(canvas_div);\n", "\n", "    var canvas = $('<canvas/>');\n", "    canvas.addClass('mpl-canvas');\n", "    canvas.attr('style', \"left: 0; top: 0; z-index: 0; outline: 0\")\n", "\n", "    this.canvas = canvas[0];\n", "    this.context = canvas[0].getContext(\"2d\");\n", "\n", "    var backingStore = this.context.backingStorePixelRatio ||\n", "\tthis.context.webkitBackingStorePixelRatio ||\n", "\tthis.context.mozBackingStorePixelRatio ||\n", "\tthis.context.msBackingStorePixelRatio ||\n", "\tthis.context.oBackingStorePixelRatio ||\n", "\tthis.context.backingStorePixelRatio || 1;\n", "\n", "    mpl.ratio = (window.devicePixelRatio || 1) / backingStore;\n", "\n", "    var rubberband = $('<canvas/>');\n", "    rubberband.attr('style', \"position: absolute; left: 0; top: 0; z-index: 1;\")\n", "\n", "    var pass_mouse_events = true;\n", "\n", "    canvas_div.resizable({\n", "        start: function(event, ui) {\n", "            pass_mouse_events = false;\n", "        },\n", "        resize: function(event, ui) {\n", "            fig.request_resize(ui.size.width, ui.size.height);\n", "        },\n", "        stop: function(event, ui) {\n", "            pass_mouse_events = true;\n", "            fig.request_resize(ui.size.width, ui.size.height);\n", "        },\n", "    });\n", "\n", "    function mouse_event_fn(event) {\n", "        if (pass_mouse_events)\n", "            return fig.mouse_event(event, event['data']);\n", "    }\n", "\n", "    rubberband.mousedown('button_press', mouse_event_fn);\n", "    rubberband.mouseup('button_release', mouse_event_fn);\n", "    // Throttle sequential mouse events to 1 every 20ms.\n", "    rubberband.mousemove('motion_notify', mouse_event_fn);\n", "\n", "    rubberband.mouseenter('figure_enter', mouse_event_fn);\n", "    rubberband.mouseleave('figure_leave', mouse_event_fn);\n", "\n", "    canvas_div.on(\"wheel\", function (event) {\n", "        event = event.originalEvent;\n", "        event['data'] = 'scroll'\n", "        if (event.deltaY < 0) {\n", "            event.step = 1;\n", "        } else {\n", "            event.step = -1;\n", "        }\n", "        mouse_event_fn(event);\n", "    });\n", "\n", "    canvas_div.append(canvas);\n", "    canvas_div.append(rubberband);\n", "\n", "    this.rubberband = rubberband;\n", "    this.rubberband_canvas = rubberband[0];\n", "    this.rubberband_context = rubberband[0].getContext(\"2d\");\n", "    this.rubberband_context.strokeStyle = \"#000000\";\n", "\n", "    this._resize_canvas = function(width, height) {\n", "        // Keep the size of the canvas, canvas container, and rubber band\n", "        // canvas in synch.\n", "        canvas_div.css('width', width)\n", "        canvas_div.css('height', height)\n", "\n", "        canvas.attr('width', width * mpl.ratio);\n", "        canvas.attr('height', height * mpl.ratio);\n", "        canvas.attr('style', 'width: ' + width + 'px; height: ' + height + 'px;');\n", "\n", "        rubberband.attr('width', width);\n", "        rubberband.attr('height', height);\n", "    }\n", "\n", "    // Set the figure to an initial 600x600px, this will subsequently be updated\n", "    // upon first draw.\n", "    this._resize_canvas(600, 600);\n", "\n", "    // Disable right mouse context menu.\n", "    $(this.rubberband_canvas).bind(\"contextmenu\",function(e){\n", "        return false;\n", "    });\n", "\n", "    function set_focus () {\n", "        canvas.focus();\n", "        canvas_div.focus();\n", "    }\n", "\n", "    window.setTimeout(set_focus, 100);\n", "}\n", "\n", "mpl.figure.prototype._init_toolbar = function() {\n", "    var fig = this;\n", "\n", "    var nav_element = $('<div/>')\n", "    nav_element.attr('style', 'width: 100%');\n", "    this.root.append(nav_element);\n", "\n", "    // Define a callback function for later on.\n", "    function toolbar_event(event) {\n", "        return fig.toolbar_button_onclick(event['data']);\n", "    }\n", "    function toolbar_mouse_event(event) {\n", "        return fig.toolbar_button_onmouseover(event['data']);\n", "    }\n", "\n", "    for(var toolbar_ind in mpl.toolbar_items) {\n", "        var name = mpl.toolbar_items[toolbar_ind][0];\n", "        var tooltip = mpl.toolbar_items[toolbar_ind][1];\n", "        var image = mpl.toolbar_items[toolbar_ind][2];\n", "        var method_name = mpl.toolbar_items[toolbar_ind][3];\n", "\n", "        if (!name) {\n", "            // put a spacer in here.\n", "            continue;\n", "        }\n", "        var button = $('<button/>');\n", "        button.addClass('ui-button ui-widget ui-state-default ui-corner-all ' +\n", "                        'ui-button-icon-only');\n", "        button.attr('role', 'button');\n", "        button.attr('aria-disabled', 'false');\n", "        button.click(method_name, toolbar_event);\n", "        button.mouseover(tooltip, toolbar_mouse_event);\n", "\n", "        var icon_img = $('<span/>');\n", "        icon_img.addClass('ui-button-icon-primary ui-icon');\n", "        icon_img.addClass(image);\n", "        icon_img.addClass('ui-corner-all');\n", "\n", "        var tooltip_span = $('<span/>');\n", "        tooltip_span.addClass('ui-button-text');\n", "        tooltip_span.html(tooltip);\n", "\n", "        button.append(icon_img);\n", "        button.append(tooltip_span);\n", "\n", "        nav_element.append(button);\n", "    }\n", "\n", "    var fmt_picker_span = $('<span/>');\n", "\n", "    var fmt_picker = $('<select/>');\n", "    fmt_picker.addClass('mpl-toolbar-option ui-widget ui-widget-content');\n", "    fmt_picker_span.append(fmt_picker);\n", "    nav_element.append(fmt_picker_span);\n", "    this.format_dropdown = fmt_picker[0];\n", "\n", "    for (var ind in mpl.extensions) {\n", "        var fmt = mpl.extensions[ind];\n", "        var option = $(\n", "            '<option/>', {selected: fmt === mpl.default_extension}).html(fmt);\n", "        fmt_picker.append(option)\n", "    }\n", "\n", "    // Add hover states to the ui-buttons\n", "    $( \".ui-button\" ).hover(\n", "        function() { $(this).addClass(\"ui-state-hover\");},\n", "        function() { $(this).removeClass(\"ui-state-hover\");}\n", "    );\n", "\n", "    var status_bar = $('<span class=\"mpl-message\"/>');\n", "    nav_element.append(status_bar);\n", "    this.message = status_bar[0];\n", "}\n", "\n", "mpl.figure.prototype.request_resize = function(x_pixels, y_pixels) {\n", "    // Request matplotlib to resize the figure. Matplotlib will then trigger a resize in the client,\n", "    // which will in turn request a refresh of the image.\n", "    this.send_message('resize', {'width': x_pixels, 'height': y_pixels});\n", "}\n", "\n", "mpl.figure.prototype.send_message = function(type, properties) {\n", "    properties['type'] = type;\n", "    properties['figure_id'] = this.id;\n", "    this.ws.send(JSON.stringify(properties));\n", "}\n", "\n", "mpl.figure.prototype.send_draw_message = function() {\n", "    if (!this.waiting) {\n", "        this.waiting = true;\n", "        this.ws.send(JSON.stringify({type: \"draw\", figure_id: this.id}));\n", "    }\n", "}\n", "\n", "\n", "mpl.figure.prototype.handle_save = function(fig, msg) {\n", "    var format_dropdown = fig.format_dropdown;\n", "    var format = format_dropdown.options[format_dropdown.selectedIndex].value;\n", "    fig.ondownload(fig, format);\n", "}\n", "\n", "\n", "mpl.figure.prototype.handle_resize = function(fig, msg) {\n", "    var size = msg['size'];\n", "    if (size[0] != fig.canvas.width || size[1] != fig.canvas.height) {\n", "        fig._resize_canvas(size[0], size[1]);\n", "        fig.send_message(\"refresh\", {});\n", "    };\n", "}\n", "\n", "mpl.figure.prototype.handle_rubberband = function(fig, msg) {\n", "    var x0 = msg['x0'] / mpl.ratio;\n", "    var y0 = (fig.canvas.height - msg['y0']) / mpl.ratio;\n", "    var x1 = msg['x1'] / mpl.ratio;\n", "    var y1 = (fig.canvas.height - msg['y1']) / mpl.ratio;\n", "    x0 = Math.floor(x0) + 0.5;\n", "    y0 = Math.floor(y0) + 0.5;\n", "    x1 = Math.floor(x1) + 0.5;\n", "    y1 = Math.floor(y1) + 0.5;\n", "    var min_x = Math.min(x0, x1);\n", "    var min_y = Math.min(y0, y1);\n", "    var width = Math.abs(x1 - x0);\n", "    var height = Math.abs(y1 - y0);\n", "\n", "    fig.rubberband_context.clearRect(\n", "        0, 0, fig.canvas.width, fig.canvas.height);\n", "\n", "    fig.rubberband_context.strokeRect(min_x, min_y, width, height);\n", "}\n", "\n", "mpl.figure.prototype.handle_figure_label = function(fig, msg) {\n", "    // Updates the figure title.\n", "    fig.header.textContent = msg['label'];\n", "}\n", "\n", "mpl.figure.prototype.handle_cursor = function(fig, msg) {\n", "    var cursor = msg['cursor'];\n", "    switch(cursor)\n", "    {\n", "    case 0:\n", "        cursor = 'pointer';\n", "        break;\n", "    case 1:\n", "        cursor = 'default';\n", "        break;\n", "    case 2:\n", "        cursor = 'crosshair';\n", "        break;\n", "    case 3:\n", "        cursor = 'move';\n", "        break;\n", "    }\n", "    fig.rubberband_canvas.style.cursor = cursor;\n", "}\n", "\n", "mpl.figure.prototype.handle_message = function(fig, msg) {\n", "    fig.message.textContent = msg['message'];\n", "}\n", "\n", "mpl.figure.prototype.handle_draw = function(fig, msg) {\n", "    // Request the server to send over a new figure.\n", "    fig.send_draw_message();\n", "}\n", "\n", "mpl.figure.prototype.handle_image_mode = function(fig, msg) {\n", "    fig.image_mode = msg['mode'];\n", "}\n", "\n", "mpl.figure.prototype.updated_canvas_event = function() {\n", "    // Called whenever the canvas gets updated.\n", "    this.send_message(\"ack\", {});\n", "}\n", "\n", "// A function to construct a web socket function for onmessage handling.\n", "// Called in the figure constructor.\n", "mpl.figure.prototype._make_on_message_function = function(fig) {\n", "    return function socket_on_message(evt) {\n", "        if (evt.data instanceof Blob) {\n", "            /* FIXME: We get \"Resource interpreted as Image but\n", "             * transferred with MIME type text/plain:\" errors on\n", "             * Chrome.  But how to set the MIME type?  It doesn't seem\n", "             * to be part of the websocket stream */\n", "            evt.data.type = \"image/png\";\n", "\n", "            /* Free the memory for the previous frames */\n", "            if (fig.imageObj.src) {\n", "                (window.URL || window.webkitURL).revokeObjectURL(\n", "                    fig.imageObj.src);\n", "            }\n", "\n", "            fig.imageObj.src = (window.URL || window.webkitURL).createObjectURL(\n", "                evt.data);\n", "            fig.updated_canvas_event();\n", "            fig.waiting = false;\n", "            return;\n", "        }\n", "        else if (typeof evt.data === 'string' && evt.data.slice(0, 21) == \"data:image/png;base64\") {\n", "            fig.imageObj.src = evt.data;\n", "            fig.updated_canvas_event();\n", "            fig.waiting = false;\n", "            return;\n", "        }\n", "\n", "        var msg = JSON.parse(evt.data);\n", "        var msg_type = msg['type'];\n", "\n", "        // Call the  \"handle_{type}\" callback, which takes\n", "        // the figure and JSON message as its only arguments.\n", "        try {\n", "            var callback = fig[\"handle_\" + msg_type];\n", "        } catch (e) {\n", "            console.log(\"No handler for the '\" + msg_type + \"' message type: \", msg);\n", "            return;\n", "        }\n", "\n", "        if (callback) {\n", "            try {\n", "                // console.log(\"Handling '\" + msg_type + \"' message: \", msg);\n", "                callback(fig, msg);\n", "            } catch (e) {\n", "                console.log(\"Exception inside the 'handler_\" + msg_type + \"' callback:\", e, e.stack, msg);\n", "            }\n", "        }\n", "    };\n", "}\n", "\n", "// from http://stackoverflow.com/questions/1114465/getting-mouse-location-in-canvas\n", "mpl.findpos = function(e) {\n", "    //this section is from http://www.quirksmode.org/js/events_properties.html\n", "    var targ;\n", "    if (!e)\n", "        e = window.event;\n", "    if (e.target)\n", "        targ = e.target;\n", "    else if (e.srcElement)\n", "        targ = e.srcElement;\n", "    if (targ.nodeType == 3) // defeat Safari bug\n", "        targ = targ.parentNode;\n", "\n", "    // jQ<PERSON>y normalizes the pageX and pageY\n", "    // pageX,Y are the mouse positions relative to the document\n", "    // offset() returns the position of the element relative to the document\n", "    var x = e.pageX - $(targ).offset().left;\n", "    var y = e.pageY - $(targ).offset().top;\n", "\n", "    return {\"x\": x, \"y\": y};\n", "};\n", "\n", "/*\n", " * return a copy of an object with only non-object keys\n", " * we need this to avoid circular references\n", " * http://stackoverflow.com/a/24161582/3208463\n", " */\n", "function simple<PERSON><PERSON>s (original) {\n", "  return Object.keys(original).reduce(function (obj, key) {\n", "    if (typeof original[key] !== 'object')\n", "        obj[key] = original[key]\n", "    return obj;\n", "  }, {});\n", "}\n", "\n", "mpl.figure.prototype.mouse_event = function(event, name) {\n", "    var canvas_pos = mpl.findpos(event)\n", "\n", "    if (name === 'button_press')\n", "    {\n", "        this.canvas.focus();\n", "        this.canvas_div.focus();\n", "    }\n", "\n", "    var x = canvas_pos.x * mpl.ratio;\n", "    var y = canvas_pos.y * mpl.ratio;\n", "\n", "    this.send_message(name, {x: x, y: y, button: event.button,\n", "                             step: event.step,\n", "                             guiEvent: simpleKeys(event)});\n", "\n", "    /* This prevents the web browser from automatically changing to\n", "     * the text insertion cursor when the button is pressed.  We want\n", "     * to control all of the cursor setting manually through the\n", "     * 'cursor' event from matplotlib */\n", "    event.preventDefault();\n", "    return false;\n", "}\n", "\n", "mpl.figure.prototype._key_event_extra = function(event, name) {\n", "    // Handle any extra behaviour associated with a key event\n", "}\n", "\n", "mpl.figure.prototype.key_event = function(event, name) {\n", "\n", "    // Prevent repeat events\n", "    if (name == 'key_press')\n", "    {\n", "        if (event.which === this._key)\n", "            return;\n", "        else\n", "            this._key = event.which;\n", "    }\n", "    if (name == 'key_release')\n", "        this._key = null;\n", "\n", "    var value = '';\n", "    if (event.ctrlKey && event.which != 17)\n", "        value += \"ctrl+\";\n", "    if (event.altKey && event.which != 18)\n", "        value += \"alt+\";\n", "    if (event.shiftKey && event.which != 16)\n", "        value += \"shift+\";\n", "\n", "    value += 'k';\n", "    value += event.which.toString();\n", "\n", "    this._key_event_extra(event, name);\n", "\n", "    this.send_message(name, {key: value,\n", "                             guiEvent: simpleKeys(event)});\n", "    return false;\n", "}\n", "\n", "mpl.figure.prototype.toolbar_button_onclick = function(name) {\n", "    if (name == 'download') {\n", "        this.handle_save(this, null);\n", "    } else {\n", "        this.send_message(\"toolbar_button\", {name: name});\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.toolbar_button_onmouseover = function(tooltip) {\n", "    this.message.textContent = tooltip;\n", "};\n", "mpl.toolbar_items = [[\"Home\", \"Reset original view\", \"fa fa-home icon-home\", \"home\"], [\"Back\", \"Back to previous view\", \"fa fa-arrow-left icon-arrow-left\", \"back\"], [\"Forward\", \"Forward to next view\", \"fa fa-arrow-right icon-arrow-right\", \"forward\"], [\"\", \"\", \"\", \"\"], [\"Pan\", \"Pan axes with left mouse, zoom with right\", \"fa fa-arrows icon-move\", \"pan\"], [\"Zoom\", \"Zoom to rectangle\", \"fa fa-square-o icon-check-empty\", \"zoom\"], [\"\", \"\", \"\", \"\"], [\"Download\", \"Download plot\", \"fa fa-floppy-o icon-save\", \"download\"]];\n", "\n", "mpl.extensions = [\"eps\", \"jpeg\", \"pdf\", \"png\", \"ps\", \"raw\", \"svg\", \"tif\"];\n", "\n", "mpl.default_extension = \"png\";var comm_websocket_adapter = function(comm) {\n", "    // Create a \"websocket\"-like object which calls the given IPython comm\n", "    // object with the appropriate methods. Currently this is a non binary\n", "    // socket, so there is still some room for performance tuning.\n", "    var ws = {};\n", "\n", "    ws.close = function() {\n", "        comm.close()\n", "    };\n", "    ws.send = function(m) {\n", "        //console.log('sending', m);\n", "        comm.send(m);\n", "    };\n", "    // Register the callback with on_msg.\n", "    comm.on_msg(function(msg) {\n", "        //console.log('receiving', msg['content']['data'], msg);\n", "        // Pass the mpl event to the overridden (by mpl) onmessage function.\n", "        ws.onmessage(msg['content']['data'])\n", "    });\n", "    return ws;\n", "}\n", "\n", "mpl.mpl_figure_comm = function(comm, msg) {\n", "    // This is the function which gets called when the mpl process\n", "    // starts-up an IPython Comm through the \"matplotlib\" channel.\n", "\n", "    var id = msg.content.data.id;\n", "    // Get hold of the div created by the display call when the Comm\n", "    // socket was opened in Python.\n", "    var element = $(\"#\" + id);\n", "    var ws_proxy = comm_websocket_adapter(comm)\n", "\n", "    function ondownload(figure, format) {\n", "        window.open(figure.imageObj.src);\n", "    }\n", "\n", "    var fig = new mpl.figure(id, ws_proxy,\n", "                           ondownload,\n", "                           element.get(0));\n", "\n", "    // Call onopen now - mpl needs it, as it is assuming we've passed it a real\n", "    // web socket which is closed, not our websocket->open comm proxy.\n", "    ws_proxy.onopen();\n", "\n", "    fig.parent_element = element.get(0);\n", "    fig.cell_info = mpl.find_output_cell(\"<div id='\" + id + \"'></div>\");\n", "    if (!fig.cell_info) {\n", "        console.error(\"Failed to find cell for figure\", id, fig);\n", "        return;\n", "    }\n", "\n", "    var output_index = fig.cell_info[2]\n", "    var cell = fig.cell_info[0];\n", "\n", "};\n", "\n", "mpl.figure.prototype.handle_close = function(fig, msg) {\n", "    var width = fig.canvas.width/mpl.ratio\n", "    fig.root.unbind('remove')\n", "\n", "    // Update the output cell to use the data from the current canvas.\n", "    fig.push_to_output();\n", "    var dataURL = fig.canvas.toDataURL();\n", "    // Re-enable the keyboard manager in IPython - without this line, in FF,\n", "    // the notebook keyboard shortcuts fail.\n", "    IPython.keyboard_manager.enable()\n", "    $(fig.parent_element).html('<img src=\"' + dataURL + '\" width=\"' + width + '\">');\n", "    fig.close_ws(fig, msg);\n", "}\n", "\n", "mpl.figure.prototype.close_ws = function(fig, msg){\n", "    fig.send_message('closing', msg);\n", "    // fig.ws.close()\n", "}\n", "\n", "mpl.figure.prototype.push_to_output = function(remove_interactive) {\n", "    // Turn the data on the canvas into data in the output cell.\n", "    var width = this.canvas.width/mpl.ratio\n", "    var dataURL = this.canvas.toDataURL();\n", "    this.cell_info[1]['text/html'] = '<img src=\"' + dataURL + '\" width=\"' + width + '\">';\n", "}\n", "\n", "mpl.figure.prototype.updated_canvas_event = function() {\n", "    // Tell IPython that the notebook contents must change.\n", "    IPython.notebook.set_dirty(true);\n", "    this.send_message(\"ack\", {});\n", "    var fig = this;\n", "    // Wait a second, then push the new image to the DOM so\n", "    // that it is saved nicely (might be nice to debounce this).\n", "    setTimeout(function () { fig.push_to_output() }, 1000);\n", "}\n", "\n", "mpl.figure.prototype._init_toolbar = function() {\n", "    var fig = this;\n", "\n", "    var nav_element = $('<div/>')\n", "    nav_element.attr('style', 'width: 100%');\n", "    this.root.append(nav_element);\n", "\n", "    // Define a callback function for later on.\n", "    function toolbar_event(event) {\n", "        return fig.toolbar_button_onclick(event['data']);\n", "    }\n", "    function toolbar_mouse_event(event) {\n", "        return fig.toolbar_button_onmouseover(event['data']);\n", "    }\n", "\n", "    for(var toolbar_ind in mpl.toolbar_items){\n", "        var name = mpl.toolbar_items[toolbar_ind][0];\n", "        var tooltip = mpl.toolbar_items[toolbar_ind][1];\n", "        var image = mpl.toolbar_items[toolbar_ind][2];\n", "        var method_name = mpl.toolbar_items[toolbar_ind][3];\n", "\n", "        if (!name) { continue; };\n", "\n", "        var button = $('<button class=\"btn btn-default\" href=\"#\" title=\"' + name + '\"><i class=\"fa ' + image + ' fa-lg\"></i></button>');\n", "        button.click(method_name, toolbar_event);\n", "        button.mouseover(tooltip, toolbar_mouse_event);\n", "        nav_element.append(button);\n", "    }\n", "\n", "    // Add the status bar.\n", "    var status_bar = $('<span class=\"mpl-message\" style=\"text-align:right; float: right;\"/>');\n", "    nav_element.append(status_bar);\n", "    this.message = status_bar[0];\n", "\n", "    // Add the close button to the window.\n", "    var buttongrp = $('<div class=\"btn-group inline pull-right\"></div>');\n", "    var button = $('<button class=\"btn btn-mini btn-primary\" href=\"#\" title=\"Stop Interaction\"><i class=\"fa fa-power-off icon-remove icon-large\"></i></button>');\n", "    button.click(function (evt) { fig.handle_close(fig, {}); } );\n", "    button.mouseover('Stop Interaction', toolbar_mouse_event);\n", "    buttongrp.append(button);\n", "    var titlebar = this.root.find($('.ui-dialog-titlebar'));\n", "    titlebar.prepend(buttongrp);\n", "}\n", "\n", "mpl.figure.prototype._root_extra_style = function(el){\n", "    var fig = this\n", "    el.on(\"remove\", function(){\n", "\tfig.close_ws(fig, {});\n", "    });\n", "}\n", "\n", "mpl.figure.prototype._canvas_extra_style = function(el){\n", "    // this is important to make the div 'focusable\n", "    el.attr('tabindex', 0)\n", "    // reach out to IPython and tell the keyboard manager to turn it's self\n", "    // off when our div gets focus\n", "\n", "    // location in version 3\n", "    if (IPython.notebook.keyboard_manager) {\n", "        IPython.notebook.keyboard_manager.register_events(el);\n", "    }\n", "    else {\n", "        // location in version 2\n", "        IPython.keyboard_manager.register_events(el);\n", "    }\n", "\n", "}\n", "\n", "mpl.figure.prototype._key_event_extra = function(event, name) {\n", "    var manager = IPython.notebook.keyboard_manager;\n", "    if (!manager)\n", "        manager = IPython.keyboard_manager;\n", "\n", "    // Check for shift+enter\n", "    if (event.shiftKey && event.which == 13) {\n", "        this.canvas_div.blur();\n", "        event.shiftKey = false;\n", "        // Send a \"J\" for go to next cell\n", "        event.which = 74;\n", "        event.keyCode = 74;\n", "        manager.command_mode();\n", "        manager.handle_keydown(event);\n", "    }\n", "}\n", "\n", "mpl.figure.prototype.handle_save = function(fig, msg) {\n", "    fig.ondownload(fig, null);\n", "}\n", "\n", "\n", "mpl.find_output_cell = function(html_output) {\n", "    // Return the cell and output element which can be found *uniquely* in the notebook.\n", "    // Note - this is a bit hacky, but it is done because the \"notebook_saving.Notebook\"\n", "    // IPython event is triggered only after the cells have been serialised, which for\n", "    // our purposes (turning an active figure into a static one), is too late.\n", "    var cells = IPython.notebook.get_cells();\n", "    var ncells = cells.length;\n", "    for (var i=0; i<ncells; i++) {\n", "        var cell = cells[i];\n", "        if (cell.cell_type === 'code'){\n", "            for (var j=0; j<cell.output_area.outputs.length; j++) {\n", "                var data = cell.output_area.outputs[j];\n", "                if (data.data) {\n", "                    // IPython >= 3 moved mimebundle to data attribute of output\n", "                    data = data.data;\n", "                }\n", "                if (data['text/html'] == html_output) {\n", "                    return [cell, data, j];\n", "                }\n", "            }\n", "        }\n", "    }\n", "}\n", "\n", "// Register the function which deals with the matplotlib target/channel.\n", "// The kernel may be null if the page has been refreshed.\n", "if (IPython.notebook.kernel != null) {\n", "    IPython.notebook.kernel.comm_manager.register_target('matplotlib', mpl.mpl_figure_comm);\n", "}\n"], "text/plain": ["<IPython.core.display.Javascript object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<img src=\"data:image/png;base64,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****************************************+x8xAtq0iRyF/eor19eteEGTjRU0pMwJ5T9l0AeiYxUbgaDxxEGYJFgnmwwGy8ZJMLkxZM0DLX+CnBlfEXrgQOQYrNwo6pdjsMp/wCcLl/CU/3Dzn2j0JnNRor6Yaq+VDa1smBpLsdn57k748WUodw9UecG17fPPwwMP+OcYrCYbV0oD/YLyH2h6kx6cio2kQ5zaDkwSrJONC5d7NsHos4BM0Go1nFQ4wwZHHoPdsAHy5k3tWHHrXfl3QyjYvyv/weY32dGZzEXJ9jVa+1rZ0MpGtGPF/Hvf3QU/vhS5WbTy8672O3aE996DN9+Em292fT2lL2iySSn8Ke9c+U85Bb52QMWGr+lzd94kwTrZuOPNng0wuhSQ+e/qxqkZNpo2DS65BGrVglmzorCfwleU/xSCb0HXyr8FJPjYBZO5yBYYtLKhlY3UjsVv74Cf/g/KPwiV+mToi3x6vmxZWL4cFi+G8uVT63pGvWuysZcbLzxT/r1AObh9qNgILrdOZCYJ1skmysGye32kupE5W6S6kePor/wea6VvX3jwQbj7bujfP8o+UvCa8p8C0C3qUvm3iAwfumIyF9kSvlY2tLKR+rE4rwcsfxXOewQueDpDfzZujHwvJX9+WLcOsmdPvfvH80CTjZ28eOWV8u8V0sHsR8VGMHlNj8okwTrZxDBY5ANto0tD5hx/VzcKZNj4iitg1Cj49FO46qoY+vHwVeXfQ7At7Er5t5AUH7lkMhfZErZWNrSyYcdYnHcrLB8I5e6GKhmvj8i15ZdfDk2bwrhxdrh/rBeabOzkxSuvlH+vkA5mPyo2gsmrVjZs4FVOpowpCwf3wGX/i3ys7QSP3Ch65pkgSyqrV0f+2bZHk41tjHjrj/LvLd5B603FRtAYPSYekwTrZBPHYFk6ABbcA6c1hAYTZMfuCY08/DD06QNPPgm9esXRV5KbKP9JBthy88q/5QRZ7p7JXGRLqLqMossotoxFOLQ/8pG2nUuhzqdw5ok3ZKxYAWefDSVKwM8/Q+bM9oQhnmiysYsPr71R/r1GPFj9qdgIFp//isYkwTrZxDlYnI+0NYJcZ0CLpZA19wkNpX16/qWX4I474uwvSc2U/yQB6xOzyr9PiLLUTZO5yJYQtbKhlQ1bxuI/fkxvC78Ocz0K++23ULs2HDoEkybBxRfbE4omG3u4SIUnyn8qUA9Onyo2gsPlcSMxSbBONgkMlj/XwNhycPggNF8Mecuc0Nhbb0HnznDaafDdd3D66Qn0a7Cp8m8QTB+aUv59SJpFLpvMRbaEpZUNrWzYMhaP9uOHZ+D7R6H4FXDxiAx97NYNBg2CmjVh6lTIkSP1IWmyST0HqfRA+U8l+v7vW8WG/znMMAKTBOtkk+BgObg3chR29xpo8g0UqnlCg/v2Qb16MHs2iPAYODDBvg00V/4NgOhjE8q/j8mzwHWTuciCcBwXtLKhlQ1bxuK//fj5HZjdCQpfAg2nZHgUVq4ur1IFNm+GIUOgQ4fUhqXJJrX4p7p35T/VDPi7fxUb/ubP1XuTBOtk4wq3+wuHDsK48+H3JVBvHBRrmmGbyZOhYUM46yz48cfUfjdF+XenN8hvKP9BZjf5sZnMRcn3NroetLKhlY3oRkqq3vp1JExvDfkrQdPvIFPGF2o0bw5ffAGvvQbdu6fKab1nI3XI29Gzig07ePCrFyo2/MpclH6bJFgnmyhBd3vt8GH4qhZsnQMXDYGzMl4fWbAgspxSrBjIxV85c7p1kJzflf/k4OoXq8q/X5iy00+TuciWCLWyoZUNW8biif3YNBUm1Yc8pSMXfWXOlqHPbdvCsGHQvz/cfXdqwtNkkxrcbelV+beFCX/6oWLDn7xF7bVJgnWyiRr26F6c0gw2jIdz74OcRWHHD/D7D3DoANT7HHIWSbezdClUqAAFCkSuMs+bN7ouTL6l/JtE03+2lH//cWaTxyZzkS1xaWVDKxu2jMWM/di2AMZXOf47x/ksfceO8N570Ls3PPqo9yFqsvEec5t6VP5tYsN/vqjY8B9nMXlskmCdbGKCPrqXf/w/2PYd5DsP8lWIVDgmXASZskCrXyBHwXQ7UtEoWxZy54ZVqyB//ui6MPWW8m8KSX/aUf79yZstXpvMRdbEdPiw7MDTRxAwSbBONh6NqXk9YPmrUPEJqPj4UZ2m3Swqn6N/5hmP/Pm7G+XfW7xt6035t40Rf/ljMhfZErkuoxzBhEmCdbLxaIjvWg1jykC2fJHqRrY86R2vXQtlykCWLLByJRT5Z1tH0p1T/pMOsdUdKP9W02O9cyZzkS3BqthQsWHLWIzfj1k3wOr3oXJ/OPfo4yf33hs5ldKjB7zySvxdxNpSk02siAXrfeU/WHx6HY2KDa8R97g/kwTrZOMheTsWwxeyh6MYXP4zZPnnS2xbtkCpUrB3b+RW0ZIlvfFL+fcGZ1t7Uf5tZcYffpnMRbZErJUNrWzYMhYT82Naa1g7EqoPhjKdj7L11FPw+ONw/fWREypePJpsvEDZ3j6Uf3u58YNnKjb8wFICPpokWCebBIiIp+mWufBVDch7NjSXi7+ypFv54w8oXRqkyvH995E7OJL9KP/JRthu+8q/3fzY7p3JXGRLrFrZ0MqGLWMxcT8mNYRNk6H2x1Ci3VH2Xn4Z7rwTWrWCkSMT78rNgiYbN4SC/bvyH2x+kx2dio1kI5xi+yYJ1skmBWRunAiTG0OBqnDpvKM+Sb9vH5xzDqxZA7NmQa1ayfVP+U8uvrZbV/5tZ8hu/0zmIlsi1cqGVjZsGYuJ+yFXxoyrDDv+Bw2nwmmXHGXznXegUyeoVw+mTEm8u4wsaLJJLr62W1f+bWfIbv9UbNjNT8LemSRYJ5uE6YjPwKoP4JvroVgLqDfmKBsHD0b2ayxbBnPmQPXq8XWpmgmlAAAgAElEQVQRTSvlPxqUgvuO8h9cbr2IzGQu8sLfaPrQyoZWNqIZJ/5559B+GFUS9qyLbBTNV+4o3wcNArlZNNknUzTZ+GfIJMNT5T8ZqIbHpoqNgHNtkmCdbFI4WJa+AAvug9K3QI03jnJk1y4oXhz27IH/b+86oKQq0u4Fhig5KlmSiohkRGiSgDKAIggmBAOCWWDVX0VEFkQFBBRWXWQFXaKiC4gEyQw5S5AsOaMDSI7/ua/ome5Jnaq767356pw+hPde1Vf3q351+0u1fz9QuHB45BT9hwdXu/Qq+reLpsyUU+deZMoMxbIhlg1T1qI+OS6dAiaXAK5dUiXMsxfx6rtHD2DIEKBfP6BnT33DevYkm014cLVLr6J/u2jKTDmFbJipF21S6VSwvGy0qSW4jta+AWz9FKjUC6j8T68+du4EypcHihUD9uwBYmKCGyKtp0T/+jG1U4+ifztpyzxZde5FpsxOLBti2TBlLeqV4+x+YGoZIHNuoPV+ICaHV/+xscCMGcCkSUDbtnqHZm+y2ejH1E49iv7tpC3zZBWyYZ5OtEqkU8HystGqmuA6W9oB2DMWqPEvoMJLXn1Mnw60aBG+NFjRf3Aqc8pTon+naDI689C5F0VnBslHFcuGWDZMWYv65fhrHTCzGpCzDNByu1cJ82vXlCvljz+AjRv1lzCXzUa/Ou3Uo+jfTtoyT1YhG+bpRKtEOhUsLxutqgm+s7lNgKNzgXqTgJLe/pLBg4F//EOlwn75ZfBDpPSk6F8vnnbrTfRvN42ZJa/OvciUmYllQywbpqzF8MhxaCawoDlQoDbQbJlXCfP4eBUkmiEDcPAgkDevPhFks9GHpR17Ev3bUWvmyCxkwxxdhEUSnQqWl01YVBR4pyxhPr0ycGoT0HQxUKiuVx9duwIjRgCdOwNffx1496k9IfrXh6UdexL921Fr5siscy8yZVZi2RDLhilrMXxy/DEaWP4MUPwhoL73ka/HjgF33w0cOQKMHw889ljoYly6BCzIFYfMMUCjs67QO5QebIeAkA3bqcwogYVsGKUO/cLoVLC8bPTrJ+ger14CprKE+WGg5RYg921eXc2dCzRtCuTMCaxdC5QrF/RI1oNMqb0aG2e5Zxr+7cJNN4XWnzxtPwTk+28/nZkksc69yJR5iWVDLBumrMXwyvH7J8D6t4FyXYFaXyUbq1cvVVG0enVgyRIga9bgxXnrLaD+wDirgx2DXejePfi+5El7IiBkw556M0VqIRumaCJMcuhUsLxswqSkYLu9dPJGCfPLQOt9QDbvQ1GuXAEaNQIWLwa6dVPlzINtNWsCvVcrstG1qAu7dgHZsgXbmzxnRwTk+29HrZkjs869yJRZiWVDLBumrMXwy7GmO7BtKFDpfaByn2Tj8WC2KlWAv/5SZOOll4AsWQIT69QpIH9+YOr1OKsM+gOXXVZaLdNrpaUfBIRspB9dh2OmQjbCgapBfepUsLxsDFKsW5Sze4GpZQFkAPLeBeS5E8hbCchfAyjS2MqB/flnoHVrgEW/SpUC3nsP6NQJyJzZv/lMmwa0agXMzBxnuWIanXGhdGlg+3b/+/BvJLnLZATk+2+ydsyXTedeZMpsxbIhlg1T1mJk5ODx85v7A5fivccr8wxQawSQMQZr1gAffACQOLDdeiswbJgqb+6rsUgYi4XNyR5nWUX613Fh5kxg9GhFWqSlDwSEbKQPPYdrlkI2woWsIf3qVLC8bAxRakpisPYGM1NObVYfEpDzB1Vq7L3jgZjs1lMrVyrSwewSZpQcOOC78BcDTJnRwtTXjBmBDNNccLmA224DNm8GMmUyGBcRTRsC8v3XBmW67EjnXmQKgGLZEMuGKWsxenLQvTKvGfD3dqBwfaD+VCBLngR5nn8eGDlSWSzSyixhRdICBYDixYGxp1WAqOukCw0bAgsXAhMnAu3bR2+aMnLkEBCyETmsnTiSkA0nalXIhsO16uf0LhxXZc3/WgPkvRtoNAvIXsR6mAe1Va4MlCmjYi9Ss05MmaLiPTp2BDpPSSQbs2cDzZqp4mHr1nlVTPdTOLnNbggI2bCbxsySV8hGBPQxfPhwjB49Ghs3bkTz5s0xeXJixcfTp0/jhRdewLRp05A9e3a88sor6MUCCTear+u+xNepYHnZ+ELbwOuX/wYWPawObivcALhvHpAhoyUo02IXLIAVQNqyZcqy0+oxdCgwahRQtlsi2aDXhlkuGzaE54RZA5FM9yLJ9z/dL4GQANC5F4UkiMaHjXOj/PTTT8iYMSPmzJmDAwcOeJGNTp064ejRo5gwYQKOHTuGJk2aoF+/fujIn5JgAF7a133hplPB8rLxhbah169eBGbVAk5uAGr8C6jwkiXo//4HtGmjKo3++mvKspNQ/PYbsHs3sL9KItng3f37Az17Ar17qzgQac5GQL7/ztZvuGency8Kt6z+9m8c2XAL/sEHH2D9+vUJZOPcuXPIly8flixZgho1ali3DRw40LJyLFy4EL6u+wOITgXLy8YfxA2956+1inBkygbEbgJylgaLfrGM+d69KtCzYkVv2f/8EyhYEFaaK8lGUv3T/cIg0UqVlHVDmrMRkO+/s/Ub7tnp3IvCLau//duGbKxbtw7VqlXD5cuXEcNqSQBmz56N9u3bIz4+Hr6u+wOITgXLy8YfxA2+57f3gM0fAkXuAxrPtgItBg4EWIr8xReBL77wlt1t+XjmGeCbb5KTDd59113Apk3A1q2KeEhzLgLy/XeubiMxM517USTk9WcM25CNuLg4K4bjzJkzCfNatWoV6tSpgytXrsDX9ZTAoPWkTx/vSpLX6WDX0ORlowHEaHZBd8rM6io1tta/gXJdrMqizDThAWsHD3qnwb72mqrF8d13wFNPpUw2uNToQvnwQ+Ddd6M5ORk73AjI9z/cCDu7fyEbEdRvUjcKLRfVq1fHpUuXEiwbjOto165dgmUjrev+iK5TwfKy8Qdxw+/5cxXw6z1AppuAFpuAm0qia1dgxIjkabBuq8W+fUCJEimTDVo1eF/VqqoWhzTnIiDff+fqNhIz07kXRUJef8awjWXDHZOxdOlSi3SwDRo0CFOnTsWiRYsSYjZSu+4XGBkyQCwb/iCVju7hSbE8MbZII6DhTGzaksUiDCQUtFIwBuOW3Hvx1dtjMHPXi1izMb8FTkqbDY1md9wBbNsG63A2ptJKcyYCQjacqddIzUrIRgSQpkuEH2aZbNiwAd9//72VnZIlSxYr6+TEiRMYP358QjZK3759E7JRfF33Jb5OBcvLxhfaNrl+9cKN7JSNQIm2QN0JaPZADFg7g61skZ2Y37MRShQ4gEWHnkf9N0akSjZ4gRkpzEwZMAB4802bYCBiBoyAfP8Dhkwe8EBA515kCrDGWTZSiqNo0KABFixYANbR6Nq1q1edjffffz8BS1/XfYGuU8HysvGFto2us7T5bBdwZhdwa0fE3zYKM2ZmxNEd29CxWGMUyHHImsw1ZEHG1ruBHEVTtGzwHhb1qlYNqF0bWL7cRhiIqAEhIN//gOCSm5MgoHMvMgVc48hGNIHRqWB52URTk2EYmyXNZ9cDzh0Ayr8EVHgZmHsfcOEIUOZZXM+SHxm2DgLueBOoOiBVskFXStmyKj3WHd8RBmmlyygjIN//KCvA5sPr3ItMgULIRphMV/KyMWWJa5Tj9HZgTn3gwlFVg4MulnJdgZpfqP+bUhrImBVovQ9xhVUxDZ6NkrQxfZZptKw2+vrrGuWTroxBQL7/xqjCloII2bCl2vwXWqeC5WXjP+62uvPkRmBOA3VEffmXgRrDEg87WdEF2PU1cHd/xNWtlyrZWLECuOceoF49IE4VGpXmMATk++8whUZ4Ojr3ogiLnupwYtkQy4Ypa9E+cpzeocqZl2jjfaoa/3/abUC2woh79odUyQZdKaVKqSPr6Uph7Q5pzkJAyIaz9Bnp2QjZiDTiER5Pp4LlZRNh5ZkyXFw7YP8kxL0wB8iYJUU3CkV95x3g44+Bhx8GfvxRToI1RX265JDvvy4k02c/OvciUxAUy4ZYNkxZi86Q48/VwKyaiOvyCxCTK1WywUK4PLaegaJjxwJPPOGM6cssFAJCNmQlhIKAkI1Q0LPBszoVLC8bGyg8XCLOa4q4Vq8DmXLAdbpxqqMsXAg0bAjky6cOd7vllnAJJP1GGgH5/kcacWeNp3MvMgUZsWyIZcOUtegcOY7MRVzZ89Z8XAtnA5X/CWTJk+L8mI3y+edAq1bAlCniTnHKIhCy4RRNRmceQjaig3vERtWpYHnZRExtRg4Ul3uulRrr+rolkK0IUHUQUPrJZGzi3Dng7ruBnTuB0aOBTp2MnI4IFSAC8v0PEDC53QsBnXuRKdCKZUMsG6asRUfJoTab63DN+AbY/a2a283NgPr/A2JyeM11yRLA5QJy51ZH0Et2SipLgXVNrpwDsqrzZ0xuQjZM1o75sgnZMF9HIUmoU8HysglJFbZ/2Ev/xxYDq7oCp34HirYE6v8EZMzsNUeekzJoEPDcc8DIkbafvp4JXLsM/LkSODofODoPOL5UWYYeWAfkuV3PGGHqRb7/YQI2nXSrcy8yBTKxbIhlw5S16Cg5km02F04Ac1zA6a3W+Sq4ZxSQIWPCnE+fBooVA65cAfbvBwoWdBQcgU+GxUhm1gDi1yY+mzELcO0SUPZ5oLY68M7UJmTDVM3YQy4hG/bQU9BS6lSwvGyCVoMjHkxR/2f3A7PrAuf2A7f3UHEc/KXOdv063ux+BoM+y4kPP8yAd991BAzBTyL+N2BGFSBHSeC2V4EijYGbSgNTSgHXrwAP7QeymcvI5PsfvOrlSb4WMuA6CbeDmlg2xLLhoOVszlRS3WxObQXm1AMu/gnc/g8gS17lKvhzlXWo22vffo6fNr9q1d/I7O1pMWdykZBkY19g4/tW6Xfc+U7iiKtfB7Z/DlTuC1R6LxKSBDWGkI2gYJOHbiAgZMPhS0GnguVl4/DF4mN6aer/xEpgXmPgytnEXjJkAq5fQ/z5Qij64h6M/m92PPpolDFkzARl5CdzbiBzrtQFYvAmrRF/rlDk6a81QME6QK0RQMaYwCcysxbw1yogdiOQt1Li83/vAn4urzJ8HtoDZMoaeN8ReEK+/xEA2cFD6NyLTIFJLBti2TBlLTpKDp+bzbE4laWS5y6gQC0gXxVg+dPAvu/x8ujhWHfmZSxdGgQkVy+mvQHHbwBOLAHKPKNOrk3aji8DVnYG/t6p4iPcjQGtRe5T58EUf8g6/8W659B04OAvwLEF3ve7nyv9FFBntFd8is9ZnTsETC4G3HQr8OCu5MVHFj0MHJgM3PMtUKZj2t1tGQyc2QlUHwZkzORzaF03+NS/roGkH0ciIGTDkWr1+HGp0U8mLxuHL5ZQLBupPRu/HphRFYdOlUSpV3di6bLMqFnTTxyvXwO2DgF+e1cRAm7EmbJ4P0xrw9zGwOXTQO7bVZBqwXvUPfQPb/sMWPemionIWkhZM2JuUp+/tyvXDxsDW7MXU7En7pY5L1CoLlCgtiJP2W8GFrZS95TrAtT8yv+KZTu/BlZ2ASq8BtT4LDkAxxapk3dJ0B5Ym3q/l04CPxVRJKj6Z8Btr/kJZui3yfc/dAzTcw9CNhyufZ0KlpeNwxdLOMgG+5wfCxyegY5ffotrpTpizBg/cGSmy/JOysrgbkVjgXqTgJjs6n9ObgbmNlCEIfcdwOktijQwboSf1S8D+38EMmYFagwDynb23sSvXQGOxwH7f1Kf84eAvJUBjlO0hSItSd0ltHzMqQ+cPwzc1g2oNtg/wrGgFXBoGtB4DnDzfckB8MxUuW8+UKRhyiDtGgWseFZdy5QDaLEZyFk6bUDZ9/5JwLWrQLEWabuOPHsi2fPILpLvvx/rVm5JFQGde5EpMIsbxUMTOhUsLxtTlnh05Aha/6zJMceFrYfvwN3vbsKePRkTz0zhIW/HlwA3lVAuBm6cdIssfUJt/rRW8Bf82u6qpkeRRkD9qVbgKWa71J/uLJg/RgNruykrhxUvclX16ZoE5K/mY0O+Blz+O9US7F4PUw5aIS6eAO7sCdzdL+2+WbTrxwLWibloczy5dcb99O4xwLKngGIPAg2mpNznvGbAkdnK/XN0LnBzU6DRrNQJz9VLwOqXgF3/Uf2ReBV9ACjRDijeSll6UmonNwIci/3XHmnJHLT+o7NcZVTDENC5F5kyNSEbQjZMWYuOkiOkzYbE4PhiPDzkJ1ws9DBq174OV6EhaJjnLWTMcDVlnG7tBNQYDmTOCVw4Dsy/H4hfp9watCyc25fcnXHuALCii2VJQfHWyq3C7Bjdje6hOY2AyyeBWv9WcqTWDkwFFj0ElHwUqDch9ftIDKaWBs4fAVptB3KV8773/FFgclEga2EV98GaHbTmcI5lnk7eL61Di9sCdNFkvwUo0gQ4OBW4fErdm7UAQCtK3ru8n2Xw7Myaqm82Wnnq/YC4gqutf7pOunSjKf2lAwSEbDhcyToVHNJm43Cc08P0QtL/oRnAglis+qMmGvabj5GdO+Pxeyfg/KVsGDKjO2pW+RuNau5GzMXd1vkrqNQ7eaAk4xUWxAInlim4Sz0B1PkueZAk3QYkIqxn4a75EQ4F0WLDDByO1/hXZXVJqa14Htg1EqgzBrj1ybQl2dwf+K2nCna95xvve7cNB9a8mhj3wcBX1jjJnAdouUXFlLgbrS+MLznzB5CvGtBgKpCjGEBCc2QO8Mc3ysWUozjQbJn6093c8rIOCC1HNyxKcW37BE42SFzo7gmnHsKhW+lTOwI69yLtwgXZoVg2PIDTqeCQNpsglSmPmYNASPq3YhKqAfHrcRYlcRP24fTVUvjx2E/oNaQaDh4EypYFxo9H2gGkl88Aq14AYnIBNT5PViI94mjRdbP8GSBLPqDZCiB3eW8RGPfwv6LK5dLmmO8zUOgCmloGuBSvUmTzVEzs79e6wImlihy4g2DXdFNBsIwzKdYSOLkJOLVJ1Ti5eh4o8QhQ59tkZ9dYBIlyM3uIcSpN45RLZe9EYMljQLabgdjf6HcB5jezLEpxXaZbxMG1bo+KdWH2ES1J+auqlOAC9wB57gRObQSOLgCOLQRObQbyVVXn59xUKuLqkQHNQUDnXmTKrIRsCNkwZS06So6QyAaR2Ps9sORGoY2bmwD3jrcqZv75J9C5MzB5MhATA/TrB/BclYyJlc/NxnHdW8CWgUCuCsD9yxXxcDfWH/m1NlC4AdBkgX/zYAbO2h4qHbf+ZPXM2b3AlNKq4uiDfyRaCki+pt8FnN3j3Xem7EDF/wMq9Uo9RZdWjoUtlKWDsRl0Wc2qqWJXaKmhjtgsi1ILxD2sCpG5vm6VOBazejxrqySdIbEgcWImEAkHs3sCaRx7cTtlean9jVhIAsHOsHuFbBimEN3i6FRwyJuN7slJfxFFIGT9MxuCAZz81cyN0CPTgz+0//1voHt34MIFIDYW+O9/gfzmH4aqsjziHgYO/qw26HrfJxKO394DNn8IVP0UuKOHf/qiG+nnCirFtukSoNC9wO8DgPX/B1R8G6jykXc/tGJsHw7kLKuKheWpBOQs41/hsUun1Pk2DAh1E4eUxrh8BnH5l1hF2lwLfwUKu4BC9YCsBYHT24E/lwMnlitLBq0xJFf8sHYJiRPlY10TpguXvZFN4wsNkqEFzdWBdWx8tnxXX0/pu84YGStV+kb2U6A906pFokTCxSBbuzQGNB+eBeybpKxjPGRRQ9O5F2kQR0sXYtnwgFGngkPebLSoVzqJFgKR0P/mzcAjjwBbtwKlSgGTJgE1akRrxgGMS2sA4ye4aWeIUemtdGFsG6JiHlpuT+5iSat7t3umkAtosjDBBYXmvwH5KgcgmB+30hUy6x7g/EHlCmm6KEX3VEj63/FvYPUrqt4Ja41U+TjtTZzskym+xIEWozO7AFpr6Frylerrx5R93sKgXhKFXOWBZstVkHKgzR0YzOfuWwAUaRBoDynfzyq4tPhkK6SnP/ZC6xQL2TFFmn9ePaf6jskJtDmiiGiITedeFKIo2h4XsiFkQ9tiko4SEQhpswkAyL//Bp5/Hpg4EciSBRg2TP3b+BhDVgnd+AFw4H8qRsPdmL7LAM5AGq0lM+5WlgIebrfuDWUxiN0UHiB4vs3OEcr64hks6iFzyPpnHMfiR1RdlJzl1Cm3qQXVus+RYaG1+1cAO74AGDzLoNXGswOr3hoI7ryXNVcWP6qIEVupx4B7xwWO+40MLKuPwg2BJvMDlcT7frrMGGhMNxsDoNnnHf9Q8Toe9VD8HuTKeVXHZt9E4OA0ZcVwEwxmIJV8BCjaXAvRYLdCNvzWjD1v1KngkF829oRQpL6BQCT1zx+2w4cDPXqoI+pr1QIeewxo314dW290Y7Ewppsy24MugDveAMo+F7jInr+M+XSUD2rTon+eErzqJVXgjK3Ms0C1Qd5xLrvHAss6qF/VTRcD+e4GWLKeqb4Mfq35BVD+xcDx9OcJxhWxxgtdINWGAFsHq429xr+ACi/504O6x8oUuhfIfZv69+ltKs04tWJtafXMtG8GAZNwMf6FjefoXDiq/s4xbuuu3FN0VflqVmXewcDGPsCVM+puYs0YoZLtgJubBe86SmNsnXuRrylG6rpYNjyQ1qlgLS+bSK0CGUc7AtHQ/7JlwDPPANu2qenQulGvHvDaa8rd4uhGxsV4ChY9Y2u1I3ntjQgCoE3/nNe+74E1rwEXjqlfzkyPZawKf13TosCibA2mqQJk7sbS9LNqq/NvLHfKrXpmz82XBHHfD8DyG+fS3POdSlNmgC9PNGZrshgoWMu/MRe1URYuqyBadmDpkykHCXNsWq9yV0z5nBse0mdVrD2k3HOln1DklfczRmjrp1b9GquxLg3P7EmrMQ6FlXkZk8Eic6xFU+pR4BZaMIKMTfEPEbFs+ImTbW8TsmFb1RknuLbNJsCZcW9auxaYMAH4/ntg3z7VwVtvAR99ZKOslQDnbd1+o/qqFUtx/436IsH0o+EZ7fq/+Jc6t2bPfxW5yJhNbXgMyqz0vtpYkzZ3wC3dL41mB34QHWMdGPi4Z6xKI2b8A64njkI57h2rNmB32/6FKn3Pui3N16piaGk1BsxOuz3xFF+ShOmVgNNbgfvmJbqOSK5IQui2ueUBoO4E7wq2Z/cposFMJLpyqgxQlXaTNhIiBiiTkNSdCJRqn7J0h2erCrW0iNAlx/GSFnTTsE5S60LnXhRGMQPqWiwbHnDpVLD2l01AapWbo42ACfq/dg2YMQN46ikgPh54+GGVtXJT6PFr0YY39fFJOBgUmUosRaQED5v+ySb9DcihO4XpuQzE9fcwPHdswt5xKjbBffIvD9rLnEtZDJgZRVcCSU6J1t6QUj6Sgr3jgfzVVal4FlBjVhVTkVnzxFP+lV1V/MvdHwF3vq362jNeuWcK11fBoqziyqqydLchgyI8dIfU/1kFErNC7uz66nTf1IrXeUpJIsF6KMx8id3gvVZoPdnQG9h8o6w+caOLKCZHpJaONY7OvSiigqcxmJANIRumrEVHyRG2zSYIlLZvB1q0AHbypPXqwNSpinAcOKA+p08D998P5E7l6I8ghrTqYKmXZjBP2/8ZY/TPqqgscMbqphXfAar0Tw4urQZ0FbBIGd0N7tgEEgwGPpamW6O+/4GVDM78tY6KGUnaaGWp+aUiC3RTTCmlYida708slc+AX9ZDYQl4VpLd8okiTCQvtDCs6a7iWCgfy99v+qdyr9DNwVRqf2Ix2Me2oYoMsU4Kg0aZZbKso7Ke0GJEtw7jMqLQhGxEAfRIDqlTwca8bCIJoIyVgIBp+mcxsDZtgEWLFAFwkwG3wIULqwJhzz4LZMoUmiJ37AAefBDYtQtgv0WKqI/LBfwfS4bYpQBZCDAYpX9u1Pzlz7NpmK3DrAw2Zrps+xzYPiwxmJLxIMVaKdcIMzcyZQ0OBRIYlsonoSDR4YeWEpICxj+wPglTRrcMUocDVvvUe5w9E4Cljyf+HwupuX5U1hWSkQ09gd8/SbxO1wqLuvkrL+Wzgmg3q9OIS7YHFj4IxK9V2T8NpyUGrAaHQEhP6dyLQhJE48Ni2fAAU6eCjXrZaFww0pV/CJio/0uXgG7dVD0Obv7Fi6sPLRuM72CrXBkYMgRo3Ni/eSa9i8GpjRoBhw8D+fIBp04BdOe421dfAV0jWGsquFmE9pRVbT5znNVJs4uukMlbaNLcePr4UmBeExVUys2VMQs7vlS/5ukaKf6gOvyuWAtt6ZvJ5GbMhzuzw506yrFZ5TVpfIWVzlxZ1V0p9Thwz+jkJwAzE2fl86rSKk83DjRokycm083ExtgSumNYXI2kxlesiRalpN6Jzr0ozKL63b2QDSEbfi8WudF/BEwkG2lJv3q1qki6+EawfoMGqix627ZAdj8D71lcjETjyBH1LKuccuP96y+A/bdsCeTKBWzZAtxyi/9Y2u1OYnjSpcjG1ckuPPSQITM4NBNY9OCNQE8e5ZJVpRlXfCuyZ7Gc2a2KlrFuRdnOQO2vUwaIQZ9/rgRKtEndhcMKnsxgCdZft2UwsO6GpYdY1PgiOamJgvqEbEQB9EgOqVPBdttsIolzehjLjvonMaDV4513lAuELU8e4IknlHuF8R6pvdNJIEg0jh4FunQBvvwyubuEZGboUFX/g0XInNpYVO2hkYpsDG7kwrwbFcSNmC+zS9a/o+pE0J2SPUqsj4uNMRmsOupPjEW4wGNA6Ka+AAuikWwES1o0y6dzL9IsWtDdiWVDLBtBLx55MHUE7Eg23LOh24Mb5H/+A/z0E0D3C1u5csDjj6uCYRUrquDSJUuApUuBceOAEyeAF14A/vWvlOMyzpxRz+3fD0ybpoJWndbOnQNuvhkY93ccMmYAWlx34bfflHtKmiDgLwJCNvxFyqb36VSwnTcbm6rPKLGdouuXJ9oAABhkSURBVH+6QMaOBcaMAVauTIS4QAFYJ9B6NhYPo+UirR+HP/+sgkdLlgR4tkvOAI7RmDkTmDJFxXxUqWKUuhOEIel68kkVs8FA26YXXJZL6etUPAVmzkKkijYCOveiaM/FPb5YNsSyYcpadJQcTiEbnkr54w9VLGz8eEUU7roLqFsXuPde9eetfhapbNdOuWtYXv3TT1UAKQNJz55V5dVTIivz5wMPPJBoZSFh6dXLvIPnmEL866/AvJviEJMJaH7VhatXlTWnYMHUlzitRH36AA0bKrIiLX0jIGTD4frXqWAnbjYOV7/W6Tld/zyDJSYmOMgOHQLuuAPgIXKMCSHRcKfikrSQzJTwKP5IN0T9+iprhlYCWji4ObPFxgLMcPG8PzipQn/q4EFlsWGmz8RzKmZjYgeX5VZi9da3b9Ss8hyJ8+Z8X34ZOHkSyJEDIKljH9LSLwI69yJTUBTLhlg2TFmLjpLD6WQjVGWNHq2IQ7ZsQP78Kk2WMR3caPnv775TMR27dyvLCTNcevcGPvgAuHgR+PZboH9/YO9eoGpVFTfCvqLZPvlEEYo33wRajVBko/AKF26/XaUYc26ZPc7+ohvqxReBH35Q1pxKlYCNG1VW0ODB0ZyJjB1tBIRsRFsDYR5fp4JlswmzsgzvXvTvW0FJK28zEJWbNet8sHHTZSApi4Qxw4UWDE8XC+9v2lQVKot2XATncuedKq130ybgr7qKbLhOuizrC8vGs5YJXUi0ypBMDRumSFSpUgDJF4Nn6YqiW4nZQEWL+sZQ7nAmAjr3IlMQEsuGWDZMWYuOkkPIRvDqnDxZnV5LtwIb61QwxiMltw03a1o2+CezZ5iiG422ahVQq5ZKD2ZNEU/90+3TvLkiI7RwzJ6dWOjs6aeBzz5LLBVPskULySuvKDIiLX0iIGTD4XrXqWDZbBy+WHxMT/Qfmv737AFYr4JnuDCmIa3CYnFxqsYHXRR0p5B8sDEwkySA/08SEM5GcsDYjM8/B1591Zts0FJBqwWrq7KxoFnHjkCnTip2xbMxfZjWDVpteJaNCbEo4cRN+k4ZAZ17kSkYi2XDQxM6FSybjSlLPDpyiP4jiztjHP7xD6BMGWUZoNuCLphjx1TNjxEjgOeeC49MjCGhy4MBrwx+ZdZJUv2TEDGFmFk0zZqlHVzLLBueU8MUX7qOpKU/BHTuRaagJ2RDyIYpa9FRcgjZiKw6GTPByqR0t7gb3S4MLl22DLh8GRg4EHjjDf1yvf66smjwoLsff1T9h6L/+Hhl3WAqMONVSpcOTmZaVObOVeXiiY0hxTGDm0w6e0rIhsMVrlPBobxsHA5zupie6D/yaqZlgTEQWbMqCwLrcuTNq+pePPwwwOqeLMX+4Yf6Nl66eFjOnUXO1q5Vqa+hkg0+/89/quyb1q2VO4nzYJowT9EtVChtbJmhM2qU+uzbp+79+GN14q40eyCgcy8yZcZi2RDLhilr0VFyCNkwS520bjArhEGndE/QEpElS2gysrAZg0LPnwdmzVKZMe4Wqv5Ze4TWDVo5kjYWU2vVSn04PuuPcH48AI6ZOSwh765bwiJhrPxKokWrC60v0sxHQMiG+ToKSUKdCg71ZRPSROThqCMg+o+6CpIJsGGDipfgYXG33aYIB/8dTOMGX7MmsH27iq/o2dO7Fx36J2mYOlUVPXN/WKvDHWjKEWntoCxucsH/YxVWZvPwwxgWlninZYd1SEhGatQIZsbyTCQR0LkXRVLutMYSy4YHOjoVrONlY8oiETkCR0D0HzhmkXiCRcJYs2POHDUa02oZXMrKnbQOMJtl+XIVVMpUVRba4p+Mm2AMCP+fH1b8pKWA1gWm6vL/PFs49c8sFZ4xww8DT0ku6tVTJeP5J+VNKs+gQarYGA+Jo6VDslwisdqCH0PnXhS8FHqfFLIhZEPvipLeLATCudkIxKEhQCsACQLPZmGKLTdmBlMG2mg1WLNGxVMkbZHSP9N7eeCbr8Y5033EA+FIoJo0Uee18ENLD90rzOLxrHDq7pNpuLyHNUIiGWRKiw0DexkPk96akA2Ha1yngiP1snG4Smw7PdG/+apjrAV/8fOkWp5FwswVfurUUQSEMRmsBso/ee4JN2ySEm7wLK/OQ+S4cafUTNQ/N24WF2OGSkqtQQOVzeN5YBzjQFgojVkxnCutQh06qPnrbsSVQbaMf2FQLy1NxJtBvQyWTYkI+ZKBOmY/hw+rwm/8sCw+A4lr1/b1dPSu69yLojcL75HFsuGBh04Fm/iyMWXRpQc5RP/pQcupz9FU/TNjhxk0JAt0pfBDEkLLxrp1yl3EGA9abd59Fxg+XJEsumpIuNgY+9G2LVC5cmJ2DDNkeDgfi5LxzBd+WOOEmzs3en5IJt57T2XvJLWQrFgBPPWUIjXuxswbBraSHLAo25gxsM6Z8adRlm++USfpsvZJ0kaXGA/Ho3UrqcvJn/7DfY/OvSjcsvrbv5ANIRv+rhW5LwAETN1sApiC3BoCAnbTPzd1WjAmTlRVW2ndYAotSQnLqdOaQasD3TDjxqkCZsG2Rx4BvvxSjUECwrRcWi74d1qWmLZ8//2KzNDNxWqrDJZlFVnWSqE7KLUTh2kJYSwNSQ2Dd9l4oB8zeBivwg+JD60lFy4ALVuqc2n8ddXw2enTVeYPK9UyCDdXrmCRSP05IRv6MTWqR50KttvLxihFOEAY0b8DlBjCFOyof1ow+GufGzX/TmsHS7Bzg/ZstDTQzUFLx/HjyoLBP+nm4Kbt/tDawdLs7s/69apEO0kM+xwwABg5UmXIMECX7iweqJfU6kESwngSEhJaLNg/A3OZYcN0Y8ozf75yD/HcGQYBs/EaTwZOKfuGmUk8FI+EhNYdHv5HgpMzp/dcOTYDakkw+CHh8my5c6s6KCxRzwP1dDWde5EumULtRywbHgjqVLAdXzahLiZ5PhEB0X/6Xg121j9/tdPSEWxacFqaZ9AnT/Oli8PdaCGgtcSXi4TBuCRCJBV0/bCxgBvLxXs2xtz07Qvcd1/aa5DWmRdfVGXk2UiWXC5VDI6EhoSKH89aJ3Qv0VJSvz7wyy9KbgbQMkiX5IyWH6Yjh9p07kWhyqLreSEbQjZ0rSXpxwMBO282osjQERD9p40h64ewNgkDVkkMSBr8bSzMRisDM4p4oi7dMSQW/DRurOJI/G204DBG5YcfVGAqY008Gwu/MXCWVg+SDNZn8bS8MCaFLqEvvlCVXRlQrCMGRMiGvxq06X06FSwvG5suAk1ii/41AWnTbkT/9lOcOxuGBIbWDJIXVmBlDIuvxqwXuod8WWd89eO+rnMv8nfMcN8nlg2xbIR7jaXL/mWzSZdqT5i06D996z/U2QvZCBVBw5/XqWB52Riu7DCLJ/oPM8CGdy/6N1xBhouncy8yZapi2RDLhilr0VFyyGbjKHUGPBnRf8CQyQNh2otMAVbIRpgULC8bU5Z4dOQQ/UcHd1NGFf2bogl7yiGWDXvqzW+pdSpYXjZ+w+7IG0X/jlSr35MS/fsNldyYAgI69yJTABbLhlg2TFmLjpJDNhtHqTPgyYj+A4ZMHgjTXmQKsI4kG5cvX0b37t0xduxYkCE++eSTGDJkCGJSq3F7Qxs62aS8bExZ4tGRQ/QfHdxNGVX0b4om7CmHzr3IFAQcSTZ69+6NKVOmYMaMGRbOzZs3R5s2bfD++++nibtOBcvLxpQlHh05RP/Rwd2UUUX/pmjCnnLo3ItMQcCRZKNEiRKWJeMRnvgDVof7AW+88Qb2supKGk2nguVlY8oSj44cov/o4G7KqKJ/UzRhTzl07kWmIOA4shEfH4/8+fNjx44dKFeunIUz/16hQgWcPHkSedIoXK9TwfKyMWWJR0cO0X90cDdlVNG/KZqwpxw69yJTEHAc2di/fz9KliyJ48ePoyCL5oMnEh5H4cKFwWvFixdPwP6DDz5Anz59vHRxncXyNTR52WgA0cZdiP5trDwNoov+NYCYjrsQsmED5bstGzt37kTZsmUtifn38uXLi2XDBvpzioiy2ThFk8HNQ/QfHG7ylEJAyIZNVgJjNoYOHYq2bdtaEk+aNAk9evTAvn370pyBTgXLy8YmiyVMYor+wwSsTboV/dtEUYaKqXMvMmWKjnOjEFhmnUybNg3TeQ4xgNjYWLRu3VqyUUxZdelADtls0oGS05ii6D996z/U2QvZCBXBCD3POhvdunXDuHHjrBE7dOggdTYihL0MoxCQzSZ9rwTRf/rWf6izF7IRKoKGP69TwfKyMVzZYRZP9B9mgA3vXvRvuIIMF0/nXmTKVB3pRgkWXJ0KlpdNsFpwxnOif2foMdhZiP6DRU6eIwI69yJTEBWy4aEJnQqWl40pSzw6coj+o4O7KaOK/k3RhD3l0LkXmYKAkI0kZMMUxYgcgoAgIAgIAukXAV01n0xBUMhGGDXhRHYaRrj86low9QumgG8SXAOGzOcDgqlPiIK6QXANCraoPyRkI4wqkC+FfnAFU/2YskfBVT+ugql+TGWthgfTSPQqZCOMKMvLRj+4gql+TOUFLpiGB4Hw9CrvgPDgGu5ehWyEEWGevcKPNH0ICKb6sPTsSXDVj6tgqh9T9ii4hgfXcPcqZCPcCEv/goAgIAgIAoJAOkdAyEY6XwAyfUFAEBAEBAFBINwICNkIN8LSvyAgCAgCgoAgkM4RELKRzheATF8QEAQEAUFAEAg3AkI2NCPMQ+C6d++OsWPHWumETz75pF+HwGkWw1bdXbx4Ea+88grmzJmDEydOoFixYnjrrbfw7LPPWvM4ffo0XnjhBesk3+zZs1v39urVK2GOvq7bCowwCHv+/HncddddFrYnT54UTDVgPHXqVOsU6R07diBPnjzW37lGfa1FX9c1iGbLLg4ePIiXX34ZcXFx1nuzUaNGGD58OIoUKQJf71Rf120JiAOFFrKhWam9e/fGlClTMGPGDKvn5s2bo02bNj6Pt9cshq26O3v2LD755BN06tQJZcqUwYoVKyzcJk6ciGbNmln/f/ToUUyYMAHHjh1DkyZN0K9fP3Ts2NGap6/rtgIjDMK++eabWLVqFdavX59ANnxh5ut6GMS0TZczZ85E586dMWbMGLhcLotgcH3efvvtPtei4Jqymh966CGLZBBTVs7kj7QcOXJY33lf71Rf122zsBwuqJANzQouUaKEZcl45JFHrJ5/+OEHvPHGG9i7d6/mkZzdHQlapUqV8PbbbyNfvnxYsmQJatSoYU164MCBlpVj4cKFOHfuXJrXnY2S79mtXbsWTz31FAYPHoxHH33UIhu+MPN13feozr6jZs2aeP7559GlSxevifrCzdd1Z6OW9uwqV65sfdefeOIJ60Zahj/66CNs2rQJvt6pvq6nZ1xNmruQDY3aiI+PR/78+S3Tarly5aye+fcKFSpYL3maW6X5RuDChQsWfkOHDkXZsmVRrVo1y5QaExNjPTx79my0b98exHvdunVpXvc9mnPvuHLlCmrXro1PP/3UmmTr1q2tdegLM1/XnYuY75nRCpcrVy4MGDAAI0eOtPBs0KABPvvsMxw+fFjWqm8IU7xj9OjRlkWYf9Ky0aFDB9x5550WAUnrnXrt2jV55waJeaQfE7KhEfH9+/ejZMmSOH78OAoWLGj1zL8XLlwYvFa8eHGNozmzK75o+EucPty5c+daFg26VM6cOZMwYboE6tSpA26m9PGmdd2ZKPk3K7qmtm7dilGjRmHBggUJZMMXZr6u+ze6M+86cOCA9Uubv8QZt1GgQAErVoNuFMZtyFoNTu/bt2/HM888g2XLllkd8PtNdxXJXFrvVL4v5J0bHOaRfkrIhkbE3ZaNnTt3Wr/I2fj38uXLi2XDD5z54njxxRexZs0aK1iUliD+yq5evTouXbqUYNngtXbt2iVYNtK67sewjrxl165dVpAd8eOG6Ek2BNPgVc7Nj249WjWee+45qyNize84SRpjOGStBoYvrROM1aK10l1xmX8uXrwYv/zyi2W5SO2d6rZsyDs3MMyjcbeQDc2o81cPzf9t27a1ep40aRJ69OiBffv2aR7JWd2RaDAaffny5ZZFgy90Nrefe+nSpRbpYBs0aJD1q3LRokU+rzsLJf9nQ3M0iZvbdccNkBslrWzff/89mjZtCsHUfzw97yxVqpRlxUhKNohvoUKFBNcAYWWWFHHztP56WomrVq2a5jtV3rkBAh6l24VsaAaeLyEGL06fPt3qOTY21jJf8/+lpY4AiQZ/ycybN8/6Je7ZmHXCF9L48eMTslH69u2bkI3i63p6xJ3prqdOnUqYOokFzdTbtm2z8OVGKZgGtzI+/PBDK/Db/aubbpRDhw5ZsUS+1qKv68FJZP+naBliUD0zS9ho2WCQKEmHr3eqr+v2R8cZMxCyoVmPDGTs1q0bxo0bZ/XMQCdmp7iDGzUP54jumKlTunRpZM2a1QsnYvfVV19ZqYVdu3b1qrPhSd58XXcESCFOwtONwq58Yebreoji2Prxq1evWnVgvv32W2sedFcNGzYMN998s+AapGZ///13qz7R6tWrQdcIrRkMbOafvt6pvq4HKZI8phkBIRuaAZXuBAFBQBAQBAQBQcAbASEbsiIEAUFAEBAEBAFBIKwICNkIK7zSuSAgCAgCgoAgIAgI2ZA1IAgIAoKAICAICAJhRUDIRljhlc4FAUFAEBAEBAFBQMiGrAFBQBAQBAQBQUAQCCsCQjbCCq90LggIAoKAICAICAJCNmQNCAKCgCAgCAgCgkBYERCyEVZ4pXNBQBBICwFWily/fj0mT54sQAkCgoCDERCy4WDlytQEgUAQaNiwoXXqZubMmRMey5Ytm1XWPFxNyEa4kJV+BQGzEBCyYZY+RBpBIGoIkGzwHB+W249UE7IRKaRlHEEguggI2Ygu/jK6IGAMAmmRjQwZMlgnb37xxRc4evQo7r//fowYMSLhVFmeafH6669j8+bNKFq0KHr16oXHH388YW48RO/jjz/G7t27rRN9+/Tpg6effto6cGvNmjXgSapjxoxB7ty5MXDgQDz66KPG4CKCCAKCQOgICNkIHUPpQRBwBAK+yEb16tUxdepU5MiRA+3bt0exYsUwatQo6+j6cuXKWSd28gRUnjDbokULzJo1C3Xr1sXPP/9sEQuelMox6JY5ePCgdcgWyUb//v2tQ83YJ0/65AnAPEU1V65cjsBVJiEICAKAkA1ZBYKAIGAhQCKwYsUK6/Rdd6tZs6Z1dDotGxMnTrQIARvvq1+/PniUPa0W/fr1w5YtWxKe69Kli/V3Wj+aN2+OOnXqWEeFJ20kGzNnzsTy5cutS9evXwfjREhYSG6kCQKCgDMQELLhDD3KLASBkBHwZdkgIahdu7Y1zpEjR3DLLbdYf44ePRo8wn7GjBkJMtBlsmjRIkyfPh0VK1ZM5lZx35hSzEbevHmt7BTKI00QEAScgYCQDWfoUWYhCISMgC+y4WnZWLlyJVwuV6qWDbpTrl27lmDZuPfeey3CkZJlI2nqq5CNkFUpHQgCxiEgZMM4lYhAgkB0EPBFNmrUqIEpU6ZYMRuPPfYYihQpYsVaxMfHWzEbffv2Bd0nTJ+NjY21LB316tWzrBSdO3fGjz/+aBGUpDEbQjaio28ZVRCIJAJCNiKJtowlCBiMQEp1Niju3r17UbBgwYRsFLpOmjVrhq+//hq0QrDR0sGUWXc2Ss+ePdGhQ4eE2X733XcYMGCA1Vf+/PktYtKxY0crQFTIhsGLQkQTBDQhIGRDE5DSjSDgZAQYILpu3TpUqVLFydOUuQkCgkCYEBCyESZgpVtBwEkICNlwkjZlLoJA5BEQshF5zGVEQcB2CAjZsJ3KRGBBwCgEhGwYpQ4RRhAQBAQBQUAQcB4CQjacp1OZkSAgCAgCgoAgYBQCQjaMUocIIwgIAoKAICAIOA8BIRvO06nMSBAQBAQBQUAQMAoBIRtGqUOEEQQEAUFAEBAEnIeAkA3n6VRmJAgIAoKAICAIGIWAkA2j1CHCCAKCgCAgCAgCzkNAyIbzdCozEgQEAUFAEBAEjEJAyIZR6hBhBAFBQBAQBAQB5yEgZMN5OpUZCQKCgCAgCAgCRiEgZMModYgwgoAgIAgIAoKA8xAQsuE8ncqMBAFBQBAQBAQBoxAQsmGUOkQYQUAQEAQEAUHAeQgI2XCeTmVGgoAgIAgIAoKAUQgI2TBKHSKMICAICAKCgCDgPASEbDhPpzIjQUAQEAQEAUHAKASEbBilDhFGEBAEBAFBQBBwHgJCNpynU5mRICAICAKCgCBgFAJCNoxShwgjCAgCgoAgIAg4DwEhG87TqcxIEBAEBAFBQBAwCoH/B0C5riSRo0H0AAAAAElFTkSuQmCC\" width=\"598.8889047540268\">"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Saving learning curve...\n"]}], "source": ["# plot learning curve\n", "fig = plt.figure(figsize=(6,6))\n", "ax = fig.add_subplot(111)\n", "ax.plot(np.arange(len(history['train'])*10, step=10), history['train'], label='train', color='b')\n", "ax.plot(np.arange(len(history['test'])*10, step=10), history['test'], label='test', color='orange')\n", "\n", "label = 'Level change'\n", "for x,level in zip(range(0,end_epoch,int(end_epoch/3)), ['easy','hard','target']):\n", "    ax.axvline(x, color='m', label=label)\n", "    label=None\n", "\n", "ax.set_title('Learning Curve')\n", "ax.set_xlabel('Epoch')\n", "ax.set_ylabel('MSE')\n", "plt.legend()\n", "\n", "print(\"Saving learning curve...\")\n", "fig.savefig('learning.png', dpi=fig.dpi)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {"scrolled": false}, "outputs": [{"data": {"application/javascript": ["/* Put everything inside the global mpl namespace */\n", "window.mpl = {};\n", "\n", "\n", "mpl.get_websocket_type = function() {\n", "    if (typeof(WebSocket) !== 'undefined') {\n", "        return WebSocket;\n", "    } else if (typeof(MozWebSocket) !== 'undefined') {\n", "        return MozWebSocket;\n", "    } else {\n", "        alert('Your browser does not have WebSocket support.' +\n", "              'Please try Chrome, Safari or Firefox ≥ 6. ' +\n", "              'Firefox 4 and 5 are also supported but you ' +\n", "              'have to enable WebSockets in about:config.');\n", "    };\n", "}\n", "\n", "mpl.figure = function(figure_id, websocket, ondownload, parent_element) {\n", "    this.id = figure_id;\n", "\n", "    this.ws = websocket;\n", "\n", "    this.supports_binary = (this.ws.binaryType != undefined);\n", "\n", "    if (!this.supports_binary) {\n", "        var warnings = document.getElementById(\"mpl-warnings\");\n", "        if (warnings) {\n", "            warnings.style.display = 'block';\n", "            warnings.textContent = (\n", "                \"This browser does not support binary websocket messages. \" +\n", "                    \"Performance may be slow.\");\n", "        }\n", "    }\n", "\n", "    this.imageObj = new Image();\n", "\n", "    this.context = undefined;\n", "    this.message = undefined;\n", "    this.canvas = undefined;\n", "    this.rubberband_canvas = undefined;\n", "    this.rubberband_context = undefined;\n", "    this.format_dropdown = undefined;\n", "\n", "    this.image_mode = 'full';\n", "\n", "    this.root = $('<div/>');\n", "    this._root_extra_style(this.root)\n", "    this.root.attr('style', 'display: inline-block');\n", "\n", "    $(parent_element).append(this.root);\n", "\n", "    this._init_header(this);\n", "    this._init_canvas(this);\n", "    this._init_toolbar(this);\n", "\n", "    var fig = this;\n", "\n", "    this.waiting = false;\n", "\n", "    this.ws.onopen =  function () {\n", "            fig.send_message(\"supports_binary\", {value: fig.supports_binary});\n", "            fig.send_message(\"send_image_mode\", {});\n", "            if (mpl.ratio != 1) {\n", "                fig.send_message(\"set_dpi_ratio\", {'dpi_ratio': mpl.ratio});\n", "            }\n", "            fig.send_message(\"refresh\", {});\n", "        }\n", "\n", "    this.imageObj.onload = function() {\n", "            if (fig.image_mode == 'full') {\n", "                // Full images could contain transparency (where diff images\n", "                // almost always do), so we need to clear the canvas so that\n", "                // there is no ghosting.\n", "                fig.context.clearRect(0, 0, fig.canvas.width, fig.canvas.height);\n", "            }\n", "            fig.context.drawImage(fig.imageObj, 0, 0);\n", "        };\n", "\n", "    this.imageObj.onunload = function() {\n", "        fig.ws.close();\n", "    }\n", "\n", "    this.ws.onmessage = this._make_on_message_function(this);\n", "\n", "    this.ondownload = ondownload;\n", "}\n", "\n", "mpl.figure.prototype._init_header = function() {\n", "    var titlebar = $(\n", "        '<div class=\"ui-dialog-titlebar ui-widget-header ui-corner-all ' +\n", "        'ui-helper-clearfix\"/>');\n", "    var titletext = $(\n", "        '<div class=\"ui-dialog-title\" style=\"width: 100%; ' +\n", "        'text-align: center; padding: 3px;\"/>');\n", "    titlebar.append(titletext)\n", "    this.root.append(titlebar);\n", "    this.header = titletext[0];\n", "}\n", "\n", "\n", "\n", "mpl.figure.prototype._canvas_extra_style = function(canvas_div) {\n", "\n", "}\n", "\n", "\n", "mpl.figure.prototype._root_extra_style = function(canvas_div) {\n", "\n", "}\n", "\n", "mpl.figure.prototype._init_canvas = function() {\n", "    var fig = this;\n", "\n", "    var canvas_div = $('<div/>');\n", "\n", "    canvas_div.attr('style', 'position: relative; clear: both; outline: 0');\n", "\n", "    function canvas_keyboard_event(event) {\n", "        return fig.key_event(event, event['data']);\n", "    }\n", "\n", "    canvas_div.keydown('key_press', canvas_keyboard_event);\n", "    canvas_div.keyup('key_release', canvas_keyboard_event);\n", "    this.canvas_div = canvas_div\n", "    this._canvas_extra_style(canvas_div)\n", "    this.root.append(canvas_div);\n", "\n", "    var canvas = $('<canvas/>');\n", "    canvas.addClass('mpl-canvas');\n", "    canvas.attr('style', \"left: 0; top: 0; z-index: 0; outline: 0\")\n", "\n", "    this.canvas = canvas[0];\n", "    this.context = canvas[0].getContext(\"2d\");\n", "\n", "    var backingStore = this.context.backingStorePixelRatio ||\n", "\tthis.context.webkitBackingStorePixelRatio ||\n", "\tthis.context.mozBackingStorePixelRatio ||\n", "\tthis.context.msBackingStorePixelRatio ||\n", "\tthis.context.oBackingStorePixelRatio ||\n", "\tthis.context.backingStorePixelRatio || 1;\n", "\n", "    mpl.ratio = (window.devicePixelRatio || 1) / backingStore;\n", "\n", "    var rubberband = $('<canvas/>');\n", "    rubberband.attr('style', \"position: absolute; left: 0; top: 0; z-index: 1;\")\n", "\n", "    var pass_mouse_events = true;\n", "\n", "    canvas_div.resizable({\n", "        start: function(event, ui) {\n", "            pass_mouse_events = false;\n", "        },\n", "        resize: function(event, ui) {\n", "            fig.request_resize(ui.size.width, ui.size.height);\n", "        },\n", "        stop: function(event, ui) {\n", "            pass_mouse_events = true;\n", "            fig.request_resize(ui.size.width, ui.size.height);\n", "        },\n", "    });\n", "\n", "    function mouse_event_fn(event) {\n", "        if (pass_mouse_events)\n", "            return fig.mouse_event(event, event['data']);\n", "    }\n", "\n", "    rubberband.mousedown('button_press', mouse_event_fn);\n", "    rubberband.mouseup('button_release', mouse_event_fn);\n", "    // Throttle sequential mouse events to 1 every 20ms.\n", "    rubberband.mousemove('motion_notify', mouse_event_fn);\n", "\n", "    rubberband.mouseenter('figure_enter', mouse_event_fn);\n", "    rubberband.mouseleave('figure_leave', mouse_event_fn);\n", "\n", "    canvas_div.on(\"wheel\", function (event) {\n", "        event = event.originalEvent;\n", "        event['data'] = 'scroll'\n", "        if (event.deltaY < 0) {\n", "            event.step = 1;\n", "        } else {\n", "            event.step = -1;\n", "        }\n", "        mouse_event_fn(event);\n", "    });\n", "\n", "    canvas_div.append(canvas);\n", "    canvas_div.append(rubberband);\n", "\n", "    this.rubberband = rubberband;\n", "    this.rubberband_canvas = rubberband[0];\n", "    this.rubberband_context = rubberband[0].getContext(\"2d\");\n", "    this.rubberband_context.strokeStyle = \"#000000\";\n", "\n", "    this._resize_canvas = function(width, height) {\n", "        // Keep the size of the canvas, canvas container, and rubber band\n", "        // canvas in synch.\n", "        canvas_div.css('width', width)\n", "        canvas_div.css('height', height)\n", "\n", "        canvas.attr('width', width * mpl.ratio);\n", "        canvas.attr('height', height * mpl.ratio);\n", "        canvas.attr('style', 'width: ' + width + 'px; height: ' + height + 'px;');\n", "\n", "        rubberband.attr('width', width);\n", "        rubberband.attr('height', height);\n", "    }\n", "\n", "    // Set the figure to an initial 600x600px, this will subsequently be updated\n", "    // upon first draw.\n", "    this._resize_canvas(600, 600);\n", "\n", "    // Disable right mouse context menu.\n", "    $(this.rubberband_canvas).bind(\"contextmenu\",function(e){\n", "        return false;\n", "    });\n", "\n", "    function set_focus () {\n", "        canvas.focus();\n", "        canvas_div.focus();\n", "    }\n", "\n", "    window.setTimeout(set_focus, 100);\n", "}\n", "\n", "mpl.figure.prototype._init_toolbar = function() {\n", "    var fig = this;\n", "\n", "    var nav_element = $('<div/>')\n", "    nav_element.attr('style', 'width: 100%');\n", "    this.root.append(nav_element);\n", "\n", "    // Define a callback function for later on.\n", "    function toolbar_event(event) {\n", "        return fig.toolbar_button_onclick(event['data']);\n", "    }\n", "    function toolbar_mouse_event(event) {\n", "        return fig.toolbar_button_onmouseover(event['data']);\n", "    }\n", "\n", "    for(var toolbar_ind in mpl.toolbar_items) {\n", "        var name = mpl.toolbar_items[toolbar_ind][0];\n", "        var tooltip = mpl.toolbar_items[toolbar_ind][1];\n", "        var image = mpl.toolbar_items[toolbar_ind][2];\n", "        var method_name = mpl.toolbar_items[toolbar_ind][3];\n", "\n", "        if (!name) {\n", "            // put a spacer in here.\n", "            continue;\n", "        }\n", "        var button = $('<button/>');\n", "        button.addClass('ui-button ui-widget ui-state-default ui-corner-all ' +\n", "                        'ui-button-icon-only');\n", "        button.attr('role', 'button');\n", "        button.attr('aria-disabled', 'false');\n", "        button.click(method_name, toolbar_event);\n", "        button.mouseover(tooltip, toolbar_mouse_event);\n", "\n", "        var icon_img = $('<span/>');\n", "        icon_img.addClass('ui-button-icon-primary ui-icon');\n", "        icon_img.addClass(image);\n", "        icon_img.addClass('ui-corner-all');\n", "\n", "        var tooltip_span = $('<span/>');\n", "        tooltip_span.addClass('ui-button-text');\n", "        tooltip_span.html(tooltip);\n", "\n", "        button.append(icon_img);\n", "        button.append(tooltip_span);\n", "\n", "        nav_element.append(button);\n", "    }\n", "\n", "    var fmt_picker_span = $('<span/>');\n", "\n", "    var fmt_picker = $('<select/>');\n", "    fmt_picker.addClass('mpl-toolbar-option ui-widget ui-widget-content');\n", "    fmt_picker_span.append(fmt_picker);\n", "    nav_element.append(fmt_picker_span);\n", "    this.format_dropdown = fmt_picker[0];\n", "\n", "    for (var ind in mpl.extensions) {\n", "        var fmt = mpl.extensions[ind];\n", "        var option = $(\n", "            '<option/>', {selected: fmt === mpl.default_extension}).html(fmt);\n", "        fmt_picker.append(option)\n", "    }\n", "\n", "    // Add hover states to the ui-buttons\n", "    $( \".ui-button\" ).hover(\n", "        function() { $(this).addClass(\"ui-state-hover\");},\n", "        function() { $(this).removeClass(\"ui-state-hover\");}\n", "    );\n", "\n", "    var status_bar = $('<span class=\"mpl-message\"/>');\n", "    nav_element.append(status_bar);\n", "    this.message = status_bar[0];\n", "}\n", "\n", "mpl.figure.prototype.request_resize = function(x_pixels, y_pixels) {\n", "    // Request matplotlib to resize the figure. Matplotlib will then trigger a resize in the client,\n", "    // which will in turn request a refresh of the image.\n", "    this.send_message('resize', {'width': x_pixels, 'height': y_pixels});\n", "}\n", "\n", "mpl.figure.prototype.send_message = function(type, properties) {\n", "    properties['type'] = type;\n", "    properties['figure_id'] = this.id;\n", "    this.ws.send(JSON.stringify(properties));\n", "}\n", "\n", "mpl.figure.prototype.send_draw_message = function() {\n", "    if (!this.waiting) {\n", "        this.waiting = true;\n", "        this.ws.send(JSON.stringify({type: \"draw\", figure_id: this.id}));\n", "    }\n", "}\n", "\n", "\n", "mpl.figure.prototype.handle_save = function(fig, msg) {\n", "    var format_dropdown = fig.format_dropdown;\n", "    var format = format_dropdown.options[format_dropdown.selectedIndex].value;\n", "    fig.ondownload(fig, format);\n", "}\n", "\n", "\n", "mpl.figure.prototype.handle_resize = function(fig, msg) {\n", "    var size = msg['size'];\n", "    if (size[0] != fig.canvas.width || size[1] != fig.canvas.height) {\n", "        fig._resize_canvas(size[0], size[1]);\n", "        fig.send_message(\"refresh\", {});\n", "    };\n", "}\n", "\n", "mpl.figure.prototype.handle_rubberband = function(fig, msg) {\n", "    var x0 = msg['x0'] / mpl.ratio;\n", "    var y0 = (fig.canvas.height - msg['y0']) / mpl.ratio;\n", "    var x1 = msg['x1'] / mpl.ratio;\n", "    var y1 = (fig.canvas.height - msg['y1']) / mpl.ratio;\n", "    x0 = Math.floor(x0) + 0.5;\n", "    y0 = Math.floor(y0) + 0.5;\n", "    x1 = Math.floor(x1) + 0.5;\n", "    y1 = Math.floor(y1) + 0.5;\n", "    var min_x = Math.min(x0, x1);\n", "    var min_y = Math.min(y0, y1);\n", "    var width = Math.abs(x1 - x0);\n", "    var height = Math.abs(y1 - y0);\n", "\n", "    fig.rubberband_context.clearRect(\n", "        0, 0, fig.canvas.width, fig.canvas.height);\n", "\n", "    fig.rubberband_context.strokeRect(min_x, min_y, width, height);\n", "}\n", "\n", "mpl.figure.prototype.handle_figure_label = function(fig, msg) {\n", "    // Updates the figure title.\n", "    fig.header.textContent = msg['label'];\n", "}\n", "\n", "mpl.figure.prototype.handle_cursor = function(fig, msg) {\n", "    var cursor = msg['cursor'];\n", "    switch(cursor)\n", "    {\n", "    case 0:\n", "        cursor = 'pointer';\n", "        break;\n", "    case 1:\n", "        cursor = 'default';\n", "        break;\n", "    case 2:\n", "        cursor = 'crosshair';\n", "        break;\n", "    case 3:\n", "        cursor = 'move';\n", "        break;\n", "    }\n", "    fig.rubberband_canvas.style.cursor = cursor;\n", "}\n", "\n", "mpl.figure.prototype.handle_message = function(fig, msg) {\n", "    fig.message.textContent = msg['message'];\n", "}\n", "\n", "mpl.figure.prototype.handle_draw = function(fig, msg) {\n", "    // Request the server to send over a new figure.\n", "    fig.send_draw_message();\n", "}\n", "\n", "mpl.figure.prototype.handle_image_mode = function(fig, msg) {\n", "    fig.image_mode = msg['mode'];\n", "}\n", "\n", "mpl.figure.prototype.updated_canvas_event = function() {\n", "    // Called whenever the canvas gets updated.\n", "    this.send_message(\"ack\", {});\n", "}\n", "\n", "// A function to construct a web socket function for onmessage handling.\n", "// Called in the figure constructor.\n", "mpl.figure.prototype._make_on_message_function = function(fig) {\n", "    return function socket_on_message(evt) {\n", "        if (evt.data instanceof Blob) {\n", "            /* FIXME: We get \"Resource interpreted as Image but\n", "             * transferred with MIME type text/plain:\" errors on\n", "             * Chrome.  But how to set the MIME type?  It doesn't seem\n", "             * to be part of the websocket stream */\n", "            evt.data.type = \"image/png\";\n", "\n", "            /* Free the memory for the previous frames */\n", "            if (fig.imageObj.src) {\n", "                (window.URL || window.webkitURL).revokeObjectURL(\n", "                    fig.imageObj.src);\n", "            }\n", "\n", "            fig.imageObj.src = (window.URL || window.webkitURL).createObjectURL(\n", "                evt.data);\n", "            fig.updated_canvas_event();\n", "            fig.waiting = false;\n", "            return;\n", "        }\n", "        else if (typeof evt.data === 'string' && evt.data.slice(0, 21) == \"data:image/png;base64\") {\n", "            fig.imageObj.src = evt.data;\n", "            fig.updated_canvas_event();\n", "            fig.waiting = false;\n", "            return;\n", "        }\n", "\n", "        var msg = JSON.parse(evt.data);\n", "        var msg_type = msg['type'];\n", "\n", "        // Call the  \"handle_{type}\" callback, which takes\n", "        // the figure and JSON message as its only arguments.\n", "        try {\n", "            var callback = fig[\"handle_\" + msg_type];\n", "        } catch (e) {\n", "            console.log(\"No handler for the '\" + msg_type + \"' message type: \", msg);\n", "            return;\n", "        }\n", "\n", "        if (callback) {\n", "            try {\n", "                // console.log(\"Handling '\" + msg_type + \"' message: \", msg);\n", "                callback(fig, msg);\n", "            } catch (e) {\n", "                console.log(\"Exception inside the 'handler_\" + msg_type + \"' callback:\", e, e.stack, msg);\n", "            }\n", "        }\n", "    };\n", "}\n", "\n", "// from http://stackoverflow.com/questions/1114465/getting-mouse-location-in-canvas\n", "mpl.findpos = function(e) {\n", "    //this section is from http://www.quirksmode.org/js/events_properties.html\n", "    var targ;\n", "    if (!e)\n", "        e = window.event;\n", "    if (e.target)\n", "        targ = e.target;\n", "    else if (e.srcElement)\n", "        targ = e.srcElement;\n", "    if (targ.nodeType == 3) // defeat Safari bug\n", "        targ = targ.parentNode;\n", "\n", "    // jQ<PERSON>y normalizes the pageX and pageY\n", "    // pageX,Y are the mouse positions relative to the document\n", "    // offset() returns the position of the element relative to the document\n", "    var x = e.pageX - $(targ).offset().left;\n", "    var y = e.pageY - $(targ).offset().top;\n", "\n", "    return {\"x\": x, \"y\": y};\n", "};\n", "\n", "/*\n", " * return a copy of an object with only non-object keys\n", " * we need this to avoid circular references\n", " * http://stackoverflow.com/a/24161582/3208463\n", " */\n", "function simple<PERSON><PERSON>s (original) {\n", "  return Object.keys(original).reduce(function (obj, key) {\n", "    if (typeof original[key] !== 'object')\n", "        obj[key] = original[key]\n", "    return obj;\n", "  }, {});\n", "}\n", "\n", "mpl.figure.prototype.mouse_event = function(event, name) {\n", "    var canvas_pos = mpl.findpos(event)\n", "\n", "    if (name === 'button_press')\n", "    {\n", "        this.canvas.focus();\n", "        this.canvas_div.focus();\n", "    }\n", "\n", "    var x = canvas_pos.x * mpl.ratio;\n", "    var y = canvas_pos.y * mpl.ratio;\n", "\n", "    this.send_message(name, {x: x, y: y, button: event.button,\n", "                             step: event.step,\n", "                             guiEvent: simpleKeys(event)});\n", "\n", "    /* This prevents the web browser from automatically changing to\n", "     * the text insertion cursor when the button is pressed.  We want\n", "     * to control all of the cursor setting manually through the\n", "     * 'cursor' event from matplotlib */\n", "    event.preventDefault();\n", "    return false;\n", "}\n", "\n", "mpl.figure.prototype._key_event_extra = function(event, name) {\n", "    // Handle any extra behaviour associated with a key event\n", "}\n", "\n", "mpl.figure.prototype.key_event = function(event, name) {\n", "\n", "    // Prevent repeat events\n", "    if (name == 'key_press')\n", "    {\n", "        if (event.which === this._key)\n", "            return;\n", "        else\n", "            this._key = event.which;\n", "    }\n", "    if (name == 'key_release')\n", "        this._key = null;\n", "\n", "    var value = '';\n", "    if (event.ctrlKey && event.which != 17)\n", "        value += \"ctrl+\";\n", "    if (event.altKey && event.which != 18)\n", "        value += \"alt+\";\n", "    if (event.shiftKey && event.which != 16)\n", "        value += \"shift+\";\n", "\n", "    value += 'k';\n", "    value += event.which.toString();\n", "\n", "    this._key_event_extra(event, name);\n", "\n", "    this.send_message(name, {key: value,\n", "                             guiEvent: simpleKeys(event)});\n", "    return false;\n", "}\n", "\n", "mpl.figure.prototype.toolbar_button_onclick = function(name) {\n", "    if (name == 'download') {\n", "        this.handle_save(this, null);\n", "    } else {\n", "        this.send_message(\"toolbar_button\", {name: name});\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.toolbar_button_onmouseover = function(tooltip) {\n", "    this.message.textContent = tooltip;\n", "};\n", "mpl.toolbar_items = [[\"Home\", \"Reset original view\", \"fa fa-home icon-home\", \"home\"], [\"Back\", \"Back to previous view\", \"fa fa-arrow-left icon-arrow-left\", \"back\"], [\"Forward\", \"Forward to next view\", \"fa fa-arrow-right icon-arrow-right\", \"forward\"], [\"\", \"\", \"\", \"\"], [\"Pan\", \"Pan axes with left mouse, zoom with right\", \"fa fa-arrows icon-move\", \"pan\"], [\"Zoom\", \"Zoom to rectangle\", \"fa fa-square-o icon-check-empty\", \"zoom\"], [\"\", \"\", \"\", \"\"], [\"Download\", \"Download plot\", \"fa fa-floppy-o icon-save\", \"download\"]];\n", "\n", "mpl.extensions = [\"eps\", \"jpeg\", \"pdf\", \"png\", \"ps\", \"raw\", \"svg\", \"tif\"];\n", "\n", "mpl.default_extension = \"png\";var comm_websocket_adapter = function(comm) {\n", "    // Create a \"websocket\"-like object which calls the given IPython comm\n", "    // object with the appropriate methods. Currently this is a non binary\n", "    // socket, so there is still some room for performance tuning.\n", "    var ws = {};\n", "\n", "    ws.close = function() {\n", "        comm.close()\n", "    };\n", "    ws.send = function(m) {\n", "        //console.log('sending', m);\n", "        comm.send(m);\n", "    };\n", "    // Register the callback with on_msg.\n", "    comm.on_msg(function(msg) {\n", "        //console.log('receiving', msg['content']['data'], msg);\n", "        // Pass the mpl event to the overridden (by mpl) onmessage function.\n", "        ws.onmessage(msg['content']['data'])\n", "    });\n", "    return ws;\n", "}\n", "\n", "mpl.mpl_figure_comm = function(comm, msg) {\n", "    // This is the function which gets called when the mpl process\n", "    // starts-up an IPython Comm through the \"matplotlib\" channel.\n", "\n", "    var id = msg.content.data.id;\n", "    // Get hold of the div created by the display call when the Comm\n", "    // socket was opened in Python.\n", "    var element = $(\"#\" + id);\n", "    var ws_proxy = comm_websocket_adapter(comm)\n", "\n", "    function ondownload(figure, format) {\n", "        window.open(figure.imageObj.src);\n", "    }\n", "\n", "    var fig = new mpl.figure(id, ws_proxy,\n", "                           ondownload,\n", "                           element.get(0));\n", "\n", "    // Call onopen now - mpl needs it, as it is assuming we've passed it a real\n", "    // web socket which is closed, not our websocket->open comm proxy.\n", "    ws_proxy.onopen();\n", "\n", "    fig.parent_element = element.get(0);\n", "    fig.cell_info = mpl.find_output_cell(\"<div id='\" + id + \"'></div>\");\n", "    if (!fig.cell_info) {\n", "        console.error(\"Failed to find cell for figure\", id, fig);\n", "        return;\n", "    }\n", "\n", "    var output_index = fig.cell_info[2]\n", "    var cell = fig.cell_info[0];\n", "\n", "};\n", "\n", "mpl.figure.prototype.handle_close = function(fig, msg) {\n", "    var width = fig.canvas.width/mpl.ratio\n", "    fig.root.unbind('remove')\n", "\n", "    // Update the output cell to use the data from the current canvas.\n", "    fig.push_to_output();\n", "    var dataURL = fig.canvas.toDataURL();\n", "    // Re-enable the keyboard manager in IPython - without this line, in FF,\n", "    // the notebook keyboard shortcuts fail.\n", "    IPython.keyboard_manager.enable()\n", "    $(fig.parent_element).html('<img src=\"' + dataURL + '\" width=\"' + width + '\">');\n", "    fig.close_ws(fig, msg);\n", "}\n", "\n", "mpl.figure.prototype.close_ws = function(fig, msg){\n", "    fig.send_message('closing', msg);\n", "    // fig.ws.close()\n", "}\n", "\n", "mpl.figure.prototype.push_to_output = function(remove_interactive) {\n", "    // Turn the data on the canvas into data in the output cell.\n", "    var width = this.canvas.width/mpl.ratio\n", "    var dataURL = this.canvas.toDataURL();\n", "    this.cell_info[1]['text/html'] = '<img src=\"' + dataURL + '\" width=\"' + width + '\">';\n", "}\n", "\n", "mpl.figure.prototype.updated_canvas_event = function() {\n", "    // Tell IPython that the notebook contents must change.\n", "    IPython.notebook.set_dirty(true);\n", "    this.send_message(\"ack\", {});\n", "    var fig = this;\n", "    // Wait a second, then push the new image to the DOM so\n", "    // that it is saved nicely (might be nice to debounce this).\n", "    setTimeout(function () { fig.push_to_output() }, 1000);\n", "}\n", "\n", "mpl.figure.prototype._init_toolbar = function() {\n", "    var fig = this;\n", "\n", "    var nav_element = $('<div/>')\n", "    nav_element.attr('style', 'width: 100%');\n", "    this.root.append(nav_element);\n", "\n", "    // Define a callback function for later on.\n", "    function toolbar_event(event) {\n", "        return fig.toolbar_button_onclick(event['data']);\n", "    }\n", "    function toolbar_mouse_event(event) {\n", "        return fig.toolbar_button_onmouseover(event['data']);\n", "    }\n", "\n", "    for(var toolbar_ind in mpl.toolbar_items){\n", "        var name = mpl.toolbar_items[toolbar_ind][0];\n", "        var tooltip = mpl.toolbar_items[toolbar_ind][1];\n", "        var image = mpl.toolbar_items[toolbar_ind][2];\n", "        var method_name = mpl.toolbar_items[toolbar_ind][3];\n", "\n", "        if (!name) { continue; };\n", "\n", "        var button = $('<button class=\"btn btn-default\" href=\"#\" title=\"' + name + '\"><i class=\"fa ' + image + ' fa-lg\"></i></button>');\n", "        button.click(method_name, toolbar_event);\n", "        button.mouseover(tooltip, toolbar_mouse_event);\n", "        nav_element.append(button);\n", "    }\n", "\n", "    // Add the status bar.\n", "    var status_bar = $('<span class=\"mpl-message\" style=\"text-align:right; float: right;\"/>');\n", "    nav_element.append(status_bar);\n", "    this.message = status_bar[0];\n", "\n", "    // Add the close button to the window.\n", "    var buttongrp = $('<div class=\"btn-group inline pull-right\"></div>');\n", "    var button = $('<button class=\"btn btn-mini btn-primary\" href=\"#\" title=\"Stop Interaction\"><i class=\"fa fa-power-off icon-remove icon-large\"></i></button>');\n", "    button.click(function (evt) { fig.handle_close(fig, {}); } );\n", "    button.mouseover('Stop Interaction', toolbar_mouse_event);\n", "    buttongrp.append(button);\n", "    var titlebar = this.root.find($('.ui-dialog-titlebar'));\n", "    titlebar.prepend(buttongrp);\n", "}\n", "\n", "mpl.figure.prototype._root_extra_style = function(el){\n", "    var fig = this\n", "    el.on(\"remove\", function(){\n", "\tfig.close_ws(fig, {});\n", "    });\n", "}\n", "\n", "mpl.figure.prototype._canvas_extra_style = function(el){\n", "    // this is important to make the div 'focusable\n", "    el.attr('tabindex', 0)\n", "    // reach out to IPython and tell the keyboard manager to turn it's self\n", "    // off when our div gets focus\n", "\n", "    // location in version 3\n", "    if (IPython.notebook.keyboard_manager) {\n", "        IPython.notebook.keyboard_manager.register_events(el);\n", "    }\n", "    else {\n", "        // location in version 2\n", "        IPython.keyboard_manager.register_events(el);\n", "    }\n", "\n", "}\n", "\n", "mpl.figure.prototype._key_event_extra = function(event, name) {\n", "    var manager = IPython.notebook.keyboard_manager;\n", "    if (!manager)\n", "        manager = IPython.keyboard_manager;\n", "\n", "    // Check for shift+enter\n", "    if (event.shiftKey && event.which == 13) {\n", "        this.canvas_div.blur();\n", "        event.shiftKey = false;\n", "        // Send a \"J\" for go to next cell\n", "        event.which = 74;\n", "        event.keyCode = 74;\n", "        manager.command_mode();\n", "        manager.handle_keydown(event);\n", "    }\n", "}\n", "\n", "mpl.figure.prototype.handle_save = function(fig, msg) {\n", "    fig.ondownload(fig, null);\n", "}\n", "\n", "\n", "mpl.find_output_cell = function(html_output) {\n", "    // Return the cell and output element which can be found *uniquely* in the notebook.\n", "    // Note - this is a bit hacky, but it is done because the \"notebook_saving.Notebook\"\n", "    // IPython event is triggered only after the cells have been serialised, which for\n", "    // our purposes (turning an active figure into a static one), is too late.\n", "    var cells = IPython.notebook.get_cells();\n", "    var ncells = cells.length;\n", "    for (var i=0; i<ncells; i++) {\n", "        var cell = cells[i];\n", "        if (cell.cell_type === 'code'){\n", "            for (var j=0; j<cell.output_area.outputs.length; j++) {\n", "                var data = cell.output_area.outputs[j];\n", "                if (data.data) {\n", "                    // IPython >= 3 moved mimebundle to data attribute of output\n", "                    data = data.data;\n", "                }\n", "                if (data['text/html'] == html_output) {\n", "                    return [cell, data, j];\n", "                }\n", "            }\n", "        }\n", "    }\n", "}\n", "\n", "// Register the function which deals with the matplotlib target/channel.\n", "// The kernel may be null if the page has been refreshed.\n", "if (IPython.notebook.kernel != null) {\n", "    IPython.notebook.kernel.comm_manager.register_target('matplotlib', mpl.mpl_figure_comm);\n", "}\n"], "text/plain": ["<IPython.core.display.Javascript object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<img src=\"data:image/png;base64,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\" width=\"598.8889047540268\">"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["<matplotlib.legend.Legend at 0x7fa1c6a9da58>"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["tranposed_data = list(zip(*pred_and_gt))\n", "preds = np.concatenate(tranposed_data[0])\n", "gts = np.concatenate(tranposed_data[1])\n", "\n", "df = pd.DataFrame(np.c_[preds,gts], columns=['Predict', 'GT'])\n", "\n", "fig = plt.figure(figsize=(6,6))\n", "plt.plot([min(preds)-0.5, max(gts)+0.5], [min(preds)-0.5, max(gts)+0.5], 'b--', label='y=x')\n", "\n", "label = 'margin'\n", "for xs,ys in zip(preds, gts):\n", "    xe = ye = xs\n", "    plt.plot([xs, xe], [ys, ye], 'r', label=label)\n", "    label = None\n", "    \n", "    \n", "sns.regplot(\"Predict\", \"GT\", data=df,color=\"g\",label='data')\n", "#plt.xlim((min(preds)-0.5, max(preds)+0.5))\n", "\n", "\n", "handles, labels = plt.gca().get_legend_handles_labels()\n", "order = [0,2,1]\n", "plt.legend([handles[idx] for idx in order],[labels[idx] for idx in order])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["transposed_test = list(zip(*length_vs_mse))\n", "\n", "x = np.concatenate(transposed_test[0])[:, None]\n", "y = np.concatenate(transposed_test[1])[:, None]\n", "df = pd.DataFrame(np.c_[x,y], columns=['SEQ_LEN', 'MSE'])\n", "plt.figure(figsize=(8,4))\n", "g = sns.jointplot(\"SEQ_LEN\", \"MSE\", data=df, kind=\"reg\",color=\"m\", height=7)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["_inp, _target = mydataset['test'][3]['keypoints_seq'][None,:,:].to(device), mydataset['test'][3]['targets'][None,:].to(device)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["net.eval()\n", "_pred = net(_inp)\n", "\n", "print(f'_pred : {_pred}, _target : {_target}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Random Forest"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["X = {'train': [], 'test': []}\n", "y = {'train': [], 'test': []}\n", "\n", "for phase in ['train', 'test']:\n", "    i = 0\n", "    while True:\n", "        try:\n", "            X[phase].append(mydataset[phase][i]['keypoints_seq'].numpy().flatten())\n", "            y[phase].append(mydataset[phase][i]['targets'].numpy())\n", "            i += 1\n", "        except:\n", "            break"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["param_grid = {\n", "    'bootstrap': [True],\n", "    'max_depth': [80, 90, 100, 110],\n", "    'max_features': [2, 4, 6, 8],\n", "    'min_samples_leaf': [3, 4, 5],\n", "    'min_samples_split': [8, 10, 12],\n", "    'n_estimators': [100, 200, 300, 1000]\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from sklearn.ensemble import RandomForestRegressor\n", "from sklearn.model_selection import GridSearchCV\n", "\n", "# Use the random grid to search for best hyperparameters\n", "# First create the base model to tune\n", "rf = RandomForestRegressor()\n", "# Random search of parameters, using 3 fold cross validation, \n", "# search across 100 different combinations, and use all available cores\n", "grid_search = GridSearchCV(estimator=rf, param_grid=param_grid,\n", "                          cv = 3, n_jobs=-1, verbose=2)\n", "\n", "X_train, y_train, X_test, y_test = np.asanyarray(X['train']), np.asanyarray(y['train']).flatten(), \\\n", "                                    np.asanyarray(X['test']), np.asanyarray(y['test']).flatten()\n", "\n", "grid_search.fit(X_train, y_train)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grid_search.best_params_"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from sklearn import metrics\n", "\n", "best_model = RandomForestRegressor(**grid_search.best_params_, n_jobs=-1)\n", "best_model.fit(X_train, y_train)\n", "\n", "y_pred = best_model.predict(X_test)\n", "print('Mean Absolute Error:', metrics.mean_absolute_error(y_test, y_pred))  \n", "print('Mean Squared Error:', metrics.mean_squared_error(y_test, y_pred))  \n", "print('Root Mean Squared Error:', np.sqrt(metrics.mean_squared_error(y_test, y_pred)))  "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.8"}}, "nbformat": 4, "nbformat_minor": 2}