{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<a id='top'></a>\n", "# Code for using Parkinson's Pose Estimation Dataset"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This notebook describes the dataset accompanying the paper:\n", "'Vision-Based Assessment of Parkinsonism and Levodopa-Induced Dyskinesia with Deep Learning Pose Estimation' - <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON> (2017).\n", "\n", "The data includes all movement trajectories extracted from the videos of <PERSON>'s assessments using Convolutional Pose Machines (CPM) (https://arxiv.org/abs/1602.00134), as well as the confidence values from CPM. The dataset also includes ground truth ratings of parkinsonism and dyskinesia severity using the UDysRS, UPDRS, and CAPSIT.\n", "\n", "Camera shake has been removed from trajectories as described in the paper. No other preprocessing has been performed.\n", "\n", "Place data files in the same folder as this file (data_import_demo.py). \n", "\n", "Files are saved in JSON format, so you'll require a JSON reading package in Python. Python comes with a package called json but we recommend ujson or simplejson (both of which are drop-in replacements for json and included in Anaconda).\n", "\n", "### Table of contents:\n", "\n", "1. [Trajectories](#Trajectories)\n", "    1. [Communication/Drinking Tasks](#Comm/Drink)\n", "    2. [Leg Agility Task](#legagility)\n", "    3. [<PERSON><PERSON> Task](#toetapping)\n", "2. [Rat<PERSON>](#<PERSON><PERSON>)\n", "    1. [UDysRS](#UDysRS)\n", "    2. [UPDRS](#UPDRS)\n", "    3. [CAPSIT](#CAPSIT)\n", "3. [Subject numbers](#Subjects)\n", "4. [Example](#Demo)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"collapsed": true}, "outputs": [], "source": ["try:\n", "    import u<PERSON><PERSON> as json\n", "except ImportError:\n", "    try:\n", "        import <PERSON><PERSON><PERSON> as json\n", "    except ImportError:\n", "        import json\n", "\n", "import numpy as np\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<a id='Trajectories'></a>\n", "## 1. Trajectories"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<a id='Comm/Drink'></a>\n", "### A. Communication/Drinking Tasks"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's try loading the data from the communication task. The format of the files for the communication and drinking tasks is the same."]}, {"cell_type": "code", "execution_count": 94, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/plain": ["387"]}, "execution_count": 94, "metadata": {}, "output_type": "execute_result"}], "source": ["trajectory_file = 'Communication_all_export.txt'\n", "\n", "with open(trajectory_file, 'r') as infile:\n", "    comm_dict = json.load(infile)\n", "\n", "len(comm_dict.keys())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["When reading in from the JSON file, the data is formatted as a dictionary. The dictionary has 387 keys. These represent separate trajectories - let's look at the format of the keys."]}, {"cell_type": "code", "execution_count": 95, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/plain": ["[u'26-1',\n", " u'22-3',\n", " u'22-2',\n", " u'25-1',\n", " u'25-3',\n", " u'51-1',\n", " u'51-3',\n", " u'51-2',\n", " u'52-3',\n", " u'248-1',\n", " u'248-3',\n", " u'20-2 (occl)',\n", " u'79-1',\n", " u'74-2',\n", " u'74-3',\n", " u'74-1',\n", " u'289-1',\n", " u'240-1',\n", " u'240-3',\n", " u'21-1']"]}, "execution_count": 95, "metadata": {}, "output_type": "execute_result"}], "source": ["comm_dict.keys()[:20]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Each key is a string with the format 'trial number - segment number (noise type)'. Most trials have 3 parts. Some trajectories contain noise such as occlusions or shaking. These may be omitted if you choose, although we kept all trajectories included in this data set. Camera shake has already been removed as described in the paper.\n", "\n", "Associated with each key is another dictionary containing two parts:"]}, {"cell_type": "code", "execution_count": 96, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/plain": ["[u'position', u'resp']"]}, "execution_count": 96, "metadata": {}, "output_type": "execute_result"}], "source": ["comm_dict['26-1'].keys()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["'position' is the trajectory of each joint and 'resp' is the confidence from CPM. These are each provided for 15 joints:"]}, {"cell_type": "code", "execution_count": 97, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/plain": ["[u'Lank',\n", " u'<PERSON>lb',\n", " u'Lhip',\n", " u'Lkne',\n", " u'Lsho',\n", " u'<PERSON><PERSON><PERSON>',\n", " u'Rank',\n", " u'<PERSON><PERSON><PERSON>',\n", " u'<PERSON><PERSON>',\n", " u'Rkne',\n", " u'<PERSON><PERSON>',\n", " u'<PERSON><PERSON><PERSON>',\n", " u'face',\n", " u'head',\n", " u'neck']"]}, "execution_count": 97, "metadata": {}, "output_type": "execute_result"}], "source": ["sorted(comm_dict['26-1']['position'].keys())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Note that 'face' position was produced using the MEEM object tracker, while the confidence is the average of the 'head' and 'neck' confidences. Let's visualize one of the trajectories."]}, {"cell_type": "code", "execution_count": 98, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/plain": ["[<matplotlib.lines.Line2D at 0x338dd4a8>]"]}, "execution_count": 98, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAX0AAAEACAYAAABfxaZOAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\noOgTQkgF8f8BNGikn+Fvc9AAAAAASUVORK5CYII=\n", "text/plain": ["<matplotlib.figure.Figure at 0x27b09208>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["%matplotlib inline\n", "\n", "plt.plot(np.array(comm_dict['52-3']['position']['Lsho'])[:,0]) # visualizing the horizontal movement"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<a id='legagility'></a>\n", "### B. Leg Agility Task"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Load the leg agility task:"]}, {"cell_type": "code", "execution_count": 99, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/plain": ["132"]}, "execution_count": 99, "metadata": {}, "output_type": "execute_result"}], "source": ["trajectory_file = 'LA_split_all_export.txt'\n", "\n", "with open(trajectory_file, 'r') as infile:\n", "    la_dict = json.load(infile)\n", "\n", "len(la_dict.keys())"]}, {"cell_type": "code", "execution_count": 100, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/plain": ["[u'217', u'212', u'90', u'93', u'24']"]}, "execution_count": 100, "metadata": {}, "output_type": "execute_result"}], "source": ["la_dict.keys()[:5]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Trials are not separated into segments like for the communication task. However, noise type may need to be stripped from keys (use str.split()). "]}, {"cell_type": "code", "execution_count": 101, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/plain": ["[u'position', u'resp', u'idx']"]}, "execution_count": 101, "metadata": {}, "output_type": "execute_result"}], "source": ["la_dict['217'].keys()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Each entry contains three parts - position and resp (as with communication task) and idx, an index of the frame number."]}, {"cell_type": "markdown", "metadata": {}, "source": ["For the lower limbs, trajectory are split into active ('\\_act') and rest ('\\_rst'), indicating whether the leg agility task is being performed by that leg."]}, {"cell_type": "code", "execution_count": 102, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/plain": ["[u'Lank_act',\n", " u'Lank_rst',\n", " u'<PERSON>lb',\n", " u'Lhip_act',\n", " u'Lhip_rst',\n", " u'Lkne_act',\n", " u'Lkne_rst',\n", " u'Lsho',\n", " u'<PERSON><PERSON><PERSON>',\n", " u'Rank_act',\n", " u'Rank_rst',\n", " u'<PERSON><PERSON><PERSON>',\n", " u'Rhip_act',\n", " u'Rhip_rst',\n", " u'Rkne_act',\n", " u'Rkne_rst',\n", " u'<PERSON><PERSON>',\n", " u'<PERSON><PERSON><PERSON>',\n", " u'face',\n", " u'head',\n", " u'neck']"]}, "execution_count": 102, "metadata": {}, "output_type": "execute_result"}], "source": ["sorted(la_dict['217']['position'].keys())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can visualize the difference between active and rest by plotting the trajectories."]}, {"cell_type": "code", "execution_count": 103, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/plain": ["(380, 500)"]}, "execution_count": 103, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAX0AAAEACAYAAABfxaZOAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nbYSJvmEYRhvx/wEL6uKWmDWHxAAAAABJRU5ErkJggg==\n", "text/plain": ["<matplotlib.figure.Figure at 0x16575be0>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["%matplotlib inline\n", "\n", "plt.plot(np.array(la_dict['217']['position']['Lank_act'])[:,1]) # visualizing the vertical movement\n", "plt.ylim(380, 500)"]}, {"cell_type": "code", "execution_count": 104, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/plain": ["(380, 500)"]}, "execution_count": 104, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAX0AAAEACAYAAABfxaZOAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nR14bCbElNQAAAABJRU5ErkJggg==\n", "text/plain": ["<matplotlib.figure.Figure at 0x26b6c668>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["%matplotlib inline\n", "\n", "plt.plot(np.array(la_dict['217']['position']['Lank_rst'])[:,1])\n", "plt.ylim(380, 500)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["There is a greater amplitude of movement in the active compared to the rest, as expected."]}, {"cell_type": "markdown", "metadata": {}, "source": ["<a id='toetapping'></a>\n", "### <PERSON><PERSON> <PERSON> Task"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Load the toe tapping task:"]}, {"cell_type": "code", "execution_count": 105, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/plain": ["133"]}, "execution_count": 105, "metadata": {}, "output_type": "execute_result"}], "source": ["trajectory_file = 'TT_opt_flow_export.txt'\n", "\n", "with open(trajectory_file, 'r') as infile:\n", "    tt_dict = json.load(infile)\n", "\n", "len(tt_dict.keys())"]}, {"cell_type": "code", "execution_count": 106, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/plain": ["[u'217', u'212', u'228', u'21 (shake)', u'226']"]}, "execution_count": 106, "metadata": {}, "output_type": "execute_result"}], "source": ["tt_dict.keys()[:5]"]}, {"cell_type": "code", "execution_count": 107, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/plain": ["[u'Lank', u'Rank']"]}, "execution_count": 107, "metadata": {}, "output_type": "execute_result"}], "source": ["tt_dict['217'].keys()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["There are two flows available, for the left foot and right foot. Flow bounding boxes were based on left and right ankle locations from CPM. Flows have already been normalized by head distance. \n", "\n", "There are three signals provided for each flow:\n", "\n", "[0] Total optical flow (norm of X and Y flows)  \n", "[1] Total X flow (horizontal)  \n", "[2] Total Y flow (vertical)"]}, {"cell_type": "code", "execution_count": 108, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/plain": ["[<matplotlib.lines.Line2D at 0x103f1dd8>]"]}, "execution_count": 108, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAY8AAAEACAYAAABLfPrqAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nAABJRU5ErkJggg==\n", "text/plain": ["<matplotlib.figure.Figure at 0x16556ba8>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["%matplotlib inline\n", "\n", "plt.plot(tt_dict['217']['Lank'][1])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<a id='Ratings'></a>\n", "## 2. <PERSON><PERSON>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<a id='UDysRS'></a>\n", "### <PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": 109, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/plain": ["[u'Communication', u'Drinking', u'Higher']"]}, "execution_count": 109, "metadata": {}, "output_type": "execute_result"}], "source": ["rating_file = 'UDysRS.txt'\n", "\n", "with open(rating_file, 'r') as infile:\n", "    ratings = json.load(infile)\n", "\n", "ratings.keys()"]}, {"cell_type": "code", "execution_count": 110, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/plain": ["147"]}, "execution_count": 110, "metadata": {}, "output_type": "execute_result"}], "source": ["len(ratings['Communication'].keys())"]}, {"cell_type": "code", "execution_count": 111, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/plain": ["[u'217', u'214', u'215', u'212', u'226', u'225', u'24', u'25', u'26', u'27']"]}, "execution_count": 111, "metadata": {}, "output_type": "execute_result"}], "source": ["ratings['Communication'].keys()[:10]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["There are three parts available in this UDysRS rating - ratings for the communication task, the drinking task, and the highest rating across all tasks that were used. Note that the ambulation task was also used to calculate the 'Higher' score, but is not included as trajectories were not extracted for this task. Ratings are indexed by trial number.\n", "\n", "Within each rating is the subscores:"]}, {"cell_type": "code", "execution_count": 112, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/plain": ["[2, 1, 1, 0, 0, 0]"]}, "execution_count": 112, "metadata": {}, "output_type": "execute_result"}], "source": ["ratings['Communication']['215']"]}, {"cell_type": "markdown", "metadata": {}, "source": ["There are 6 subscores available for UDysRS. Subscore indices are:\n", "\n", "[0] Neck  \n", "[1] Right arm/shoulder  \n", "[2] Left arm/shoulder  \n", "[3] Trunk  \n", "[4] Right leg/hip  \n", "[5] Left leg/hip  \n", "\n", "Note that while the facial dyskinesia score is usually part of UDysRS, we did not analyze facial dyskinesia for this study."]}, {"cell_type": "markdown", "metadata": {}, "source": ["<a id='UPDRS'></a>\n", "### B. UPDRS"]}, {"cell_type": "markdown", "metadata": {}, "source": ["For the UPDRS, the rating keys correspond to the item number in the MDS-UPDRS. For items where there are two ratings, the first rating is the right side and the second is the left. For item 3.17 (rest tremor amplitude), the items in order are right upper extremity, left upper extremity, right lower extremity, left lower extremity, and lip/jaw. The total score is also included."]}, {"cell_type": "code", "execution_count": 113, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/plain": ["[u'3.1',\n", " u'3.10',\n", " u'3.11',\n", " u'3.12',\n", " u'3.13',\n", " u'3.14',\n", " u'3.15',\n", " u'3.16',\n", " u'3.17',\n", " u'3.18',\n", " u'3.2',\n", " u'3.4',\n", " u'3.5',\n", " u'3.6',\n", " u'3.7',\n", " u'3.8',\n", " u'3.9',\n", " u'Total']"]}, "execution_count": 113, "metadata": {}, "output_type": "execute_result"}], "source": ["rating_file = 'UPDRS.txt'\n", "\n", "with open(rating_file, 'r') as infile:\n", "    ratings = json.load(infile)\n", "\n", "sorted(ratings.keys())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<a id='CAPSIT'></a>\n", "### C. CAPSIT"]}, {"cell_type": "markdown", "metadata": {}, "source": ["For the CAPSIT, rating keys also correspond to items from the MDS-UPDRS."]}, {"cell_type": "code", "execution_count": 114, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/plain": ["[u'3.1', u'3.10', u'3.4', u'3.5', u'3.6', u'3.8', u'3.9']"]}, "execution_count": 114, "metadata": {}, "output_type": "execute_result"}], "source": ["rating_file = 'CAPSIT.txt'\n", "\n", "with open(rating_file, 'r') as infile:\n", "    ratings = json.load(infile)\n", "\n", "sorted(ratings.keys())"]}, {"cell_type": "code", "execution_count": 115, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/plain": ["[0, 0, 0, 0, 0, 0, 0]"]}, "execution_count": 115, "metadata": {}, "output_type": "execute_result"}], "source": ["ratings['3.10']['214']"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Within each item is 6 subscores, numbered as follows:\n", "\n", "[0] Neck  \n", "[1] Trunk  \n", "[2] Upper limb right  \n", "[3] Upper limb left  \n", "[4] Lower limb right  \n", "[5] Lower limb left  \n", "\n", "Face has been omitted."]}, {"cell_type": "markdown", "metadata": {}, "source": ["<a id='Subjects'></a>\n", "## 3. Subject Numbers"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Subject numbers have also been provided to ensure unbiased cross-validation. Dictionary keys are the trial number and the value is the subject number."]}, {"cell_type": "code", "execution_count": 116, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/plain": ["135"]}, "execution_count": 116, "metadata": {}, "output_type": "execute_result"}], "source": ["sn_file = 'sn_numbers.txt'\n", "\n", "with open(sn_file, 'r') as infile:\n", "    subj_num = json.load(infile)\n", "\n", "len(subj_num.keys())"]}, {"cell_type": "code", "execution_count": 117, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/plain": ["11"]}, "execution_count": 117, "metadata": {}, "output_type": "execute_result"}], "source": ["subj_num['217']"]}, {"cell_type": "markdown", "metadata": {"collapsed": true}, "source": ["<a id='Demo'></a>\n", "## 4. Example "]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's look at a sample trajectory, the right shoulder for the communication task, trial 56-2."]}, {"cell_type": "code", "execution_count": 118, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/plain": ["[<matplotlib.lines.Line2D at 0x10624860>]"]}, "execution_count": 118, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAX0AAAEACAYAAABfxaZOAAAABHNCSVQICAgIfAhkiAAAAAlwSFlz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\nyBH/H13tAgPI0CB/AAAAAElFTkSuQmCC\n", "text/plain": ["<matplotlib.figure.Figure at 0xea77c88>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["%matplotlib inline\n", "\n", "trial_num = '56-2'\n", "\n", "plt.plot(np.array(comm_dict[trial_num]['position']['Rsho'])[:,0])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Based on the amount of movement, we would likely expect a non-zero dyskinesia severity score. Let's check the UDysRS."]}, {"cell_type": "code", "execution_count": 120, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/plain": ["[1, 2, 1.5, 1.5, 2, 2]"]}, "execution_count": 120, "metadata": {}, "output_type": "execute_result"}], "source": ["rating_file = 'UDysRS.txt'\n", "\n", "with open(rating_file, 'r') as infile:\n", "    ratings = json.load(infile)\n", "\n", "# If using communication task, will have to split the trial num to get the key\n", "# split can also be done using str.split(), but regexp allows split on space or hyphen\n", "import re\n", "\n", "trial_key = re.split(\"\\s|-\", trial_num)[0]  \n", "ratings['Communication'][trial_key]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The right shoulder/arm score for the UDysRS is the 2nd rating, which is 2. We can also check the subject number for this trajectory."]}, {"cell_type": "code", "execution_count": 71, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/plain": ["6"]}, "execution_count": 71, "metadata": {}, "output_type": "execute_result"}], "source": ["subj_num[trial_key]"]}], "metadata": {"kernelspec": {"display_name": "Python 2", "language": "python", "name": "python2"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.9"}}, "nbformat": 4, "nbformat_minor": 0}